'use client'

import { useAuth } from '@/contexts/AuthContext'
import { useEffect } from 'react'
import { apiClient } from '@/lib/api'
import AuthScreen from '@/components/AuthScreen'
import MainApp from '@/components/MainApp'

export default function Home() {
  const { user, token, isLoading } = useAuth()

  useEffect(() => {
    if (token) {
      apiClient.setToken(token)
    }
  }, [token])

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-yellow-400 mx-auto mb-4"></div>
          <p className="text-gray-400">Loading Hehe Miner...</p>
        </div>
      </div>
    )
  }

  if (!user) {
    return <AuthScreen />
  }

  return <MainApp />
}
