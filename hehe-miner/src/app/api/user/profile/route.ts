import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { getUserFromRequest } from '@/lib/auth'

export async function GET(request: NextRequest) {
  try {
    const user = getUserFromRequest(request)
    if (!user) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const userProfile = await prisma.user.findUnique({
      where: { id: user.id },
      include: {
        miningSessions: {
          where: { isCompleted: false },
          orderBy: { startTime: 'desc' },
          take: 1
        }
      }
    })

    if (!userProfile) {
      return NextResponse.json(
        { success: false, error: 'User not found' },
        { status: 404 }
      )
    }

    const currentMiningSession = userProfile.miningSessions[0]
    let canMine = true
    let timeUntilNextMining = 0

    if (currentMiningSession) {
      const miningDuration = 4 * 60 * 60 * 1000 // 4 hours in milliseconds
      const endTime = new Date(currentMiningSession.startTime.getTime() + miningDuration)
      const now = new Date()

      if (now < endTime) {
        canMine = false
        timeUntilNextMining = endTime.getTime() - now.getTime()
      }
    }

    return NextResponse.json({
      success: true,
      user: {
        id: userProfile.id,
        telegramId: userProfile.telegramId,
        username: userProfile.username,
        firstName: userProfile.firstName,
        lastName: userProfile.lastName,
        totalBalance: userProfile.totalBalance,
        miningPower: userProfile.miningPower,
        hasBasicPlan: userProfile.hasBasicPlan,
        speedUpgrades: userProfile.speedUpgrades,
        canMine,
        timeUntilNextMining,
        currentMiningSession: currentMiningSession ? {
          id: currentMiningSession.id,
          startTime: currentMiningSession.startTime,
          tokensEarned: currentMiningSession.tokensEarned
        } : null
      }
    })
  } catch (error) {
    console.error('Profile error:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}
