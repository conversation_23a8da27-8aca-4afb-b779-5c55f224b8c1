import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { getUserFromRequest } from '@/lib/auth'

export async function POST(request: NextRequest) {
  try {
    const user = getUserFromRequest(request)
    if (!user) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const userProfile = await prisma.user.findUnique({
      where: { id: user.id }
    })

    if (!userProfile) {
      return NextResponse.json(
        { success: false, error: 'User not found' },
        { status: 404 }
      )
    }

    if (!userProfile.hasBasicPlan) {
      return NextResponse.json(
        { success: false, error: 'Basic plan required for speed upgrades' },
        { status: 403 }
      )
    }

    // Each speed upgrade costs $1 and increases mining power by 0.25
    const speedUpgradeValue = 0.25
    const newMiningPower = userProfile.miningPower + speedUpgradeValue
    const newSpeedUpgrades = userProfile.speedUpgrades + 1

    // In a real app, you would integrate with a payment processor here
    // For now, we'll simulate the purchase
    const updatedUser = await prisma.user.update({
      where: { id: user.id },
      data: {
        miningPower: newMiningPower,
        speedUpgrades: newSpeedUpgrades
      }
    })

    // Create subscription record
    await prisma.subscription.create({
      data: {
        userId: user.id,
        upgradeType: 'speed',
        amount: 1.0,
        upgradeValue: speedUpgradeValue
      }
    })

    return NextResponse.json({
      success: true,
      message: 'Speed upgrade purchased successfully',
      user: {
        id: updatedUser.id,
        miningPower: updatedUser.miningPower,
        speedUpgrades: updatedUser.speedUpgrades
      }
    })
  } catch (error) {
    console.error('Speed upgrade purchase error:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}
