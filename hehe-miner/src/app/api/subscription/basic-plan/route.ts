import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { getUserFromRequest } from '@/lib/auth'

export async function POST(request: NextRequest) {
  try {
    const user = getUserFromRequest(request)
    if (!user) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const userProfile = await prisma.user.findUnique({
      where: { id: user.id }
    })

    if (!userProfile) {
      return NextResponse.json(
        { success: false, error: 'User not found' },
        { status: 404 }
      )
    }

    if (userProfile.hasBasicPlan) {
      return NextResponse.json(
        { success: false, error: 'User already has basic plan' },
        { status: 400 }
      )
    }

    // In a real app, you would integrate with a payment processor here
    // For now, we'll simulate the purchase
    const updatedUser = await prisma.user.update({
      where: { id: user.id },
      data: {
        hasBasicPlan: true
      }
    })

    // Create subscription record
    await prisma.subscription.create({
      data: {
        userId: user.id,
        upgradeType: 'basic_plan',
        amount: 1.0,
        upgradeValue: 0
      }
    })

    return NextResponse.json({
      success: true,
      message: 'Basic plan purchased successfully',
      user: {
        id: updatedUser.id,
        hasBasicPlan: updatedUser.hasBasicPlan,
        miningPower: updatedUser.miningPower
      }
    })
  } catch (error) {
    console.error('Basic plan purchase error:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}
