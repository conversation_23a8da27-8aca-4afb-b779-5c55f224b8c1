import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { generateToken } from '@/lib/auth'
import { validateTelegramAuth, isTelegramAuthExpired, TelegramUser } from '@/lib/telegram'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    
    // For local development, allow mock authentication
    if (process.env.ENABLE_MOCK_AUTH === 'true' && body.mock) {
      let user = await prisma.user.findUnique({
        where: { telegramId: 'mock-telegram-id' }
      })

      if (!user) {
        user = await prisma.user.create({
          data: {
            telegramId: 'mock-telegram-id',
            username: 'mock_user',
            firstName: 'Mock',
            lastName: 'User',
            hasBasicPlan: true // Give mock user the basic plan
          }
        })
      }

      const token = generateToken({
        id: user.id,
        telegramId: user.telegramId,
        username: user.username || undefined
      })

      return NextResponse.json({
        success: true,
        token,
        user: {
          id: user.id,
          telegramId: user.telegramId,
          username: user.username,
          firstName: user.firstName,
          lastName: user.lastName,
          totalBalance: user.totalBalance,
          miningPower: user.miningPower,
          hasBasicPlan: user.hasBasicPlan
        }
      })
    }

    const telegramData: TelegramUser = body

    // Validate Telegram authentication
    if (!validateTelegramAuth(telegramData)) {
      return NextResponse.json(
        { success: false, error: 'Invalid Telegram authentication' },
        { status: 401 }
      )
    }

    // Check if auth is expired
    if (isTelegramAuthExpired(telegramData.auth_date)) {
      return NextResponse.json(
        { success: false, error: 'Authentication expired' },
        { status: 401 }
      )
    }

    // Find or create user
    let user = await prisma.user.findUnique({
      where: { telegramId: telegramData.id.toString() }
    })

    if (!user) {
      user = await prisma.user.create({
        data: {
          telegramId: telegramData.id.toString(),
          username: telegramData.username,
          firstName: telegramData.first_name,
          lastName: telegramData.last_name
        }
      })
    }

    const token = generateToken({
      id: user.id,
      telegramId: user.telegramId,
      username: user.username || undefined
    })

    return NextResponse.json({
      success: true,
      token,
      user: {
        id: user.id,
        telegramId: user.telegramId,
        username: user.username,
        firstName: user.firstName,
        lastName: user.lastName,
        totalBalance: user.totalBalance,
        miningPower: user.miningPower,
        hasBasicPlan: user.hasBasicPlan
      }
    })
  } catch (error) {
    console.error('Auth error:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}
