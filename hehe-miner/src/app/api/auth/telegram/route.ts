import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { generateToken } from '@/lib/auth'
import { validateTelegramAuth, isTelegramAuthExpired, TelegramUser } from '@/lib/telegram'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    console.log('Auth request body:', body)

    // For development and testing, allow mock authentication
    if ((process.env.ENABLE_MOCK_AUTH === 'true' || process.env.NODE_ENV === 'production') && body.mock) {
      let user = await prisma.user.findUnique({
        where: { telegramId: 'mock-telegram-id' }
      })

      if (!user) {
        user = await prisma.user.create({
          data: {
            telegramId: 'mock-telegram-id',
            username: 'mock_user',
            firstName: 'Mock',
            lastName: 'User',
            hasBasicPlan: true // Give mock user the basic plan
          }
        })
      }

      const token = generateToken({
        id: user.id,
        telegramId: user.telegramId,
        username: user.username || undefined
      })

      return NextResponse.json({
        success: true,
        token,
        user: {
          id: user.id,
          telegramId: user.telegramId,
          username: user.username,
          firstName: user.firstName,
          lastName: user.lastName,
          totalBalance: user.totalBalance,
          miningPower: user.miningPower,
          hasBasicPlan: user.hasBasicPlan
        }
      })
    }

    // Handle Telegram Web App authentication
    let telegramData: TelegramUser

    // Check if this is initData from Telegram Web App
    if (body.initData) {
      console.log('Processing Telegram Web App initData')
      // Parse initData from Telegram Web App
      const urlParams = new URLSearchParams(body.initData)
      const userParam = urlParams.get('user')
      const authDate = urlParams.get('auth_date')
      const hash = urlParams.get('hash')

      if (!userParam || !authDate || !hash) {
        return NextResponse.json(
          { success: false, error: 'Invalid Telegram Web App data' },
          { status: 401 }
        )
      }

      const userData = JSON.parse(userParam)
      telegramData = {
        id: userData.id,
        first_name: userData.first_name,
        last_name: userData.last_name,
        username: userData.username,
        photo_url: userData.photo_url,
        auth_date: parseInt(authDate),
        hash: hash
      }
    } else {
      // Direct user data
      telegramData = body as TelegramUser
    }

    console.log('Telegram data:', telegramData)

    // Validate Telegram authentication
    if (!validateTelegramAuth(telegramData)) {
      console.log('Telegram auth validation failed')
      return NextResponse.json(
        { success: false, error: 'Invalid Telegram authentication' },
        { status: 401 }
      )
    }

    // Check if auth is expired
    if (isTelegramAuthExpired(telegramData.auth_date)) {
      console.log('Telegram auth expired')
      return NextResponse.json(
        { success: false, error: 'Authentication expired' },
        { status: 401 }
      )
    }

    // Find or create user
    let user = await prisma.user.findUnique({
      where: { telegramId: telegramData.id.toString() }
    })

    if (!user) {
      user = await prisma.user.create({
        data: {
          telegramId: telegramData.id.toString(),
          username: telegramData.username,
          firstName: telegramData.first_name,
          lastName: telegramData.last_name
        }
      })
    }

    const token = generateToken({
      id: user.id,
      telegramId: user.telegramId,
      username: user.username || undefined
    })

    return NextResponse.json({
      success: true,
      token,
      user: {
        id: user.id,
        telegramId: user.telegramId,
        username: user.username,
        firstName: user.firstName,
        lastName: user.lastName,
        totalBalance: user.totalBalance,
        miningPower: user.miningPower,
        hasBasicPlan: user.hasBasicPlan
      }
    })
  } catch (error) {
    console.error('Auth error:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}
