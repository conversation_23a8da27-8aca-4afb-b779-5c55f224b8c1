'use client'

import { useState, useEffect } from 'react'
import { useAuth } from '@/contexts/AuthContext'
import { apiClient } from '@/lib/api'
import { getTelegramWebApp, getTelegramUser } from '@/lib/telegram'

export default function AuthScreen() {
  const { login } = useAuth()
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState('')
  const [isTelegramWebApp, setIsTelegramWebApp] = useState(false)

  useEffect(() => {
    // Check if running in Telegram Web App
    const webApp = getTelegramWebApp()
    if (webApp) {
      setIsTelegramWebApp(true)
      // Auto-login if we have Telegram user data
      handleTelegramLogin()
    }
  }, [])

  const handleMockLogin = async () => {
    setIsLoading(true)
    setError('')

    try {
      const response = await apiClient.loginMock()

      if (response.success) {
        login(response.user, response.token)
      } else {
        setError(response.error || 'Login failed')
      }
    } catch (error) {
      setError('Network error occurred')
    } finally {
      setIsLoading(false)
    }
  }

  const handleTelegramLogin = async () => {
    setIsLoading(true)
    setError('')

    try {
      const webApp = getTelegramWebApp()

      // First, send debug info
      const debugData = {
        hasWebApp: !!webApp,
        hasInitData: !!(webApp?.initData),
        initDataLength: webApp?.initData?.length || 0,
        hasUser: !!(webApp?.initDataUnsafe?.user),
        userAgent: navigator.userAgent,
        isTelegram: navigator.userAgent.includes('Telegram'),
        windowTelegram: !!(window as any).Telegram,
        webAppVersion: webApp?.version,
        platform: webApp?.platform
      }

      // Send debug info
      await fetch('/api/debug/telegram', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(debugData)
      })

      if (webApp && webApp.initData) {
        console.log('Using Telegram Web App initData')
        // Use Telegram Web App initData
        const response = await fetch('/api/auth/telegram', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            initData: webApp.initData
          })
        })

        const data = await response.json()

        if (data.success) {
          login(data.user, data.token)
        } else {
          setError(`Auth failed: ${data.error}. Debug info sent to logs.`)
        }
      } else if (webApp?.initDataUnsafe?.user) {
        console.log('Using Telegram Web App user data')
        const user = webApp.initDataUnsafe.user
        const response = await fetch('/api/auth/telegram', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            id: user.id,
            first_name: user.first_name,
            last_name: user.last_name,
            username: user.username,
            photo_url: user.photo_url,
            auth_date: webApp.initDataUnsafe.auth_date || Math.floor(Date.now() / 1000),
            hash: webApp.initDataUnsafe.hash || 'mock-hash'
          })
        })

        const data = await response.json()

        if (data.success) {
          login(data.user, data.token)
        } else {
          setError(`Auth failed: ${data.error}. Debug info sent to logs.`)
        }
      } else {
        // Fallback: try to get user data from Telegram Web App
        const telegramUser = getTelegramUser()

        if (telegramUser) {
          console.log('Using getTelegramUser data')
          const response = await fetch('/api/auth/telegram', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              id: telegramUser.id,
              first_name: telegramUser.first_name,
              last_name: telegramUser.last_name,
              username: telegramUser.username,
              photo_url: telegramUser.photo_url,
              auth_date: Math.floor(Date.now() / 1000),
              hash: 'mock-hash'
            })
          })

          const data = await response.json()

          if (data.success) {
            login(data.user, data.token)
          } else {
            setError(`Auth failed: ${data.error}. Debug info sent to logs.`)
          }
        } else {
          // Try to get any available user info from Telegram context
          const webApp = getTelegramWebApp()
          let userData = null

          // Try to extract user info from various sources
          if (webApp?.initDataUnsafe?.user) {
            userData = webApp.initDataUnsafe.user
          } else if ((window as any).Telegram?.WebApp?.initDataUnsafe?.user) {
            userData = (window as any).Telegram.WebApp.initDataUnsafe.user
          }

          if (userData) {
            console.log('Found Telegram user data in context:', userData)
            // Use real Telegram user data
            const response = await fetch('/api/auth/telegram', {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({
                id: userData.id,
                first_name: userData.first_name,
                last_name: userData.last_name,
                username: userData.username,
                photo_url: userData.photo_url,
                auth_date: Math.floor(Date.now() / 1000),
                hash: 'telegram-context-auth'
              })
            })

            const data = await response.json()

            if (data.success) {
              login(data.user, data.token)
            } else {
              setError(`Auth failed with Telegram data: ${data.error}`)
            }
          } else {
            // Last resort: mock authentication with Telegram-like data
            console.log('No Telegram data available, using mock auth with Telegram-like ID')
            const mockResponse = await fetch('/api/auth/telegram', {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({
                id: 7870023320, // Use the actual chat ID we found earlier
                first_name: 'Telegram',
                last_name: 'User',
                username: 'telegram_user',
                auth_date: Math.floor(Date.now() / 1000),
                hash: 'fallback-auth'
              })
            })

            const mockData = await mockResponse.json()

            if (mockData.success) {
              login(mockData.user, mockData.token)
            } else {
              setError('Authentication failed. Please try Mock Login button.')
            }
          }
        }
      }
    } catch (error) {
      console.error('Telegram login error:', error)
      setError(`Failed to authenticate: ${error instanceof Error ? error.message : 'Unknown error'}`)
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="min-h-screen flex items-center justify-center relative overflow-hidden">
      {/* Animated Background */}
      <div className="absolute inset-0 bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900">
        <div className="absolute inset-0 opacity-20" style={{
          backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%239C92AC' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`
        }}></div>

        {/* Floating Particles */}
        <div className="absolute top-20 left-20 w-4 h-4 bg-yellow-400 rounded-full animate-float opacity-60"></div>
        <div className="absolute top-40 right-32 w-3 h-3 bg-blue-400 rounded-full animate-float opacity-40" style={{animationDelay: '1s'}}></div>
        <div className="absolute bottom-32 left-16 w-5 h-5 bg-purple-400 rounded-full animate-float opacity-50" style={{animationDelay: '2s'}}></div>
        <div className="absolute bottom-20 right-20 w-2 h-2 bg-green-400 rounded-full animate-float opacity-70" style={{animationDelay: '0.5s'}}></div>
        <div className="absolute top-60 left-1/2 w-3 h-3 bg-pink-400 rounded-full animate-float opacity-60" style={{animationDelay: '1.5s'}}></div>
      </div>

      <div className="max-w-md w-full mx-4 relative z-10">
        <div className="glass-dark rounded-3xl shadow-2xl p-8 border border-white/20 animate-bounce-in hover-lift">
          {/* Logo/Header */}
          <div className="text-center mb-8">
            <div className="relative">
              <div className="w-24 h-24 bg-gradient-to-r from-yellow-400 via-orange-500 to-red-500 rounded-full mx-auto mb-6 flex items-center justify-center animate-pulse-glow">
                <span className="text-3xl animate-float">⛏️</span>
              </div>
              <div className="absolute -top-2 -right-2 w-6 h-6 bg-gradient-to-r from-green-400 to-blue-500 rounded-full animate-ping"></div>
            </div>
            <h1 className="text-4xl font-bold bg-gradient-to-r from-yellow-400 via-orange-500 to-red-500 bg-clip-text text-transparent mb-3 animate-slide-up">
              Hehe Miner
            </h1>
            <p className="text-gray-300 text-lg animate-slide-up" style={{animationDelay: '0.2s'}}>
              ✨ Start mining Hehe tokens today! ✨
            </p>
          </div>

          {/* Error Message */}
          {error && (
            <div className="bg-red-500/20 border border-red-400/30 rounded-xl p-4 mb-6 glass animate-bounce-in">
              <div className="flex items-center space-x-2">
                <span className="text-red-400 text-lg">⚠️</span>
                <p className="text-red-300 text-sm font-medium">{error}</p>
              </div>
            </div>
          )}

          {/* Login Buttons */}
          <div className="space-y-5 animate-slide-up" style={{animationDelay: '0.4s'}}>
            <button
              onClick={handleTelegramLogin}
              className="group w-full bg-gradient-to-r from-blue-500 via-blue-600 to-purple-600 hover:from-blue-600 hover:via-blue-700 hover:to-purple-700 text-white font-bold py-4 px-6 rounded-xl transition-all duration-300 flex items-center justify-center space-x-3 shadow-lg hover:shadow-blue-500/25 hover:shadow-2xl transform hover:scale-105"
            >
              <span className="text-xl group-hover:animate-bounce">📱</span>
              <span className="text-lg">Login with Telegram</span>
              <div className="absolute inset-0 rounded-xl bg-gradient-to-r from-transparent via-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 shimmer"></div>
            </button>

            <div className="relative my-6">
              <div className="absolute inset-0 flex items-center">
                <div className="w-full border-t border-gradient-to-r from-transparent via-white/30 to-transparent"></div>
              </div>
              <div className="relative flex justify-center text-sm">
                <span className="px-4 glass-dark rounded-full text-gray-300 font-medium">
                  ✨ or for testing ✨
                </span>
              </div>
            </div>

            <button
              onClick={handleMockLogin}
              disabled={isLoading}
              className="group w-full bg-gradient-to-r from-yellow-500 via-orange-500 to-red-500 hover:from-yellow-600 hover:via-orange-600 hover:to-red-600 disabled:from-gray-600 disabled:via-gray-700 disabled:to-gray-800 disabled:cursor-not-allowed text-white font-bold py-4 px-6 rounded-xl transition-all duration-300 flex items-center justify-center space-x-3 shadow-lg hover:shadow-yellow-500/25 hover:shadow-2xl transform hover:scale-105 disabled:transform-none disabled:shadow-none"
            >
              {isLoading ? (
                <>
                  <div className="animate-spin rounded-full h-5 w-5 border-2 border-white border-t-transparent"></div>
                  <span className="text-lg">Logging in...</span>
                  <div className="absolute inset-0 rounded-xl bg-gradient-to-r from-transparent via-white/10 to-transparent shimmer"></div>
                </>
              ) : (
                <>
                  <span className="text-xl group-hover:animate-bounce">🔧</span>
                  <span className="text-lg">Mock Login (Development)</span>
                  <div className="absolute inset-0 rounded-xl bg-gradient-to-r from-transparent via-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 shimmer"></div>
                </>
              )}
            </button>
          </div>

          {/* Features */}
          <div className="mt-8 pt-6 border-t border-white/20 animate-slide-up" style={{animationDelay: '0.6s'}}>
            <h3 className="text-xl font-bold text-white mb-6 text-center bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">
              🎮 Amazing Features 🎮
            </h3>
            <div className="grid grid-cols-1 gap-3">
              <div className="flex items-center space-x-3 p-3 rounded-lg glass hover-lift transition-all duration-300">
                <span className="text-green-400 text-lg animate-pulse">⛏️</span>
                <span className="text-gray-200 font-medium">Mine 4+ HEHE tokens every 4 hours</span>
              </div>
              <div className="flex items-center space-x-3 p-3 rounded-lg glass hover-lift transition-all duration-300">
                <span className="text-blue-400 text-lg animate-pulse" style={{animationDelay: '0.5s'}}>📋</span>
                <span className="text-gray-200 font-medium">Complete tasks for bonus tokens</span>
              </div>
              <div className="flex items-center space-x-3 p-3 rounded-lg glass hover-lift transition-all duration-300">
                <span className="text-purple-400 text-lg animate-pulse" style={{animationDelay: '1s'}}>👥</span>
                <span className="text-gray-200 font-medium">Refer friends and earn rewards</span>
              </div>
              <div className="flex items-center space-x-3 p-3 rounded-lg glass hover-lift transition-all duration-300">
                <span className="text-yellow-400 text-lg animate-pulse" style={{animationDelay: '1.5s'}}>⚡</span>
                <span className="text-gray-200 font-medium">Upgrade mining speed infinitely</span>
              </div>
            </div>

            {/* Call to Action */}
            <div className="mt-6 text-center">
              <p className="text-gray-300 text-sm mb-2">Join thousands of miners worldwide! 🌍</p>
              <div className="flex justify-center space-x-1">
                <span className="w-2 h-2 bg-yellow-400 rounded-full animate-ping"></span>
                <span className="w-2 h-2 bg-blue-400 rounded-full animate-ping" style={{animationDelay: '0.2s'}}></span>
                <span className="w-2 h-2 bg-purple-400 rounded-full animate-ping" style={{animationDelay: '0.4s'}}></span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
