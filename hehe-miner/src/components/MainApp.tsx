'use client'

import { useState, useEffect } from 'react'
import { useAuth } from '@/contexts/AuthContext'
import { apiClient } from '@/lib/api'
import MiningScreen from './MiningScreen'
import TasksScreen from './TasksScreen'
import ReferralsScreen from './ReferralsScreen'
import AirdropScreen from './AirdropScreen'
import SubscriptionScreen from './SubscriptionScreen'

type Screen = 'mining' | 'tasks' | 'referrals' | 'airdrop' | 'subscription'

export default function MainApp() {
  const { user, updateUser, logout } = useAuth()
  const [currentScreen, setCurrentScreen] = useState<Screen>('mining')
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    fetchUserProfile()
  }, [])

  const fetchUserProfile = async () => {
    try {
      const response = await apiClient.getUserProfile()
      if (response.success) {
        updateUser(response.user)
      }
    } catch (error) {
      console.error('Failed to fetch user profile:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const handleLogout = () => {
    logout()
  }

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-yellow-400 mx-auto mb-4"></div>
          <p className="text-gray-400">Loading your profile...</p>
        </div>
      </div>
    )
  }

  const renderScreen = () => {
    switch (currentScreen) {
      case 'mining':
        return <MiningScreen />
      case 'tasks':
        return <TasksScreen />
      case 'referrals':
        return <ReferralsScreen />
      case 'airdrop':
        return <AirdropScreen />
      case 'subscription':
        return <SubscriptionScreen />
      default:
        return <MiningScreen />
    }
  }

  return (
    <div className="min-h-screen bg-gray-900">
      {/* Header */}
      <header className="bg-gray-800 border-b border-gray-700 px-4 py-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-full flex items-center justify-center">
              <span className="text-sm font-bold text-gray-900">⛏️</span>
            </div>
            <div>
              <h1 className="text-lg font-bold text-white">Hehe Miner</h1>
              <p className="text-xs text-gray-400">
                {user?.firstName} {user?.lastName}
              </p>
            </div>
          </div>
          <div className="flex items-center space-x-4">
            <div className="text-right">
              <p className="text-sm font-semibold text-yellow-400">
                {user?.totalBalance?.toFixed(2) || '0.00'} HEHE
              </p>
              <p className="text-xs text-gray-400">
                Mining Power: {user?.miningPower?.toFixed(2) || '4.00'}/4h
              </p>
            </div>
            <button
              onClick={handleLogout}
              className="text-gray-400 hover:text-white transition-colors"
            >
              🚪
            </button>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="pb-20">
        {renderScreen()}
      </main>

      {/* Bottom Navigation */}
      <nav className="fixed bottom-0 left-0 right-0 bg-gray-800 border-t border-gray-700">
        <div className="flex items-center justify-around py-2">
          <button
            onClick={() => setCurrentScreen('mining')}
            className={`flex flex-col items-center py-2 px-3 rounded-lg transition-colors ${
              currentScreen === 'mining'
                ? 'text-yellow-400 bg-gray-700'
                : 'text-gray-400 hover:text-white'
            }`}
          >
            <span className="text-xl mb-1">⛏️</span>
            <span className="text-xs">Mine</span>
          </button>
          
          <button
            onClick={() => setCurrentScreen('tasks')}
            className={`flex flex-col items-center py-2 px-3 rounded-lg transition-colors ${
              currentScreen === 'tasks'
                ? 'text-yellow-400 bg-gray-700'
                : 'text-gray-400 hover:text-white'
            }`}
          >
            <span className="text-xl mb-1">📋</span>
            <span className="text-xs">Tasks</span>
          </button>
          
          <button
            onClick={() => setCurrentScreen('referrals')}
            className={`flex flex-col items-center py-2 px-3 rounded-lg transition-colors ${
              currentScreen === 'referrals'
                ? 'text-yellow-400 bg-gray-700'
                : 'text-gray-400 hover:text-white'
            }`}
          >
            <span className="text-xl mb-1">👥</span>
            <span className="text-xs">Referrals</span>
          </button>
          
          <button
            onClick={() => setCurrentScreen('airdrop')}
            className={`flex flex-col items-center py-2 px-3 rounded-lg transition-colors ${
              currentScreen === 'airdrop'
                ? 'text-yellow-400 bg-gray-700'
                : 'text-gray-400 hover:text-white'
            }`}
          >
            <span className="text-xl mb-1">🪂</span>
            <span className="text-xs">Airdrop</span>
          </button>
          
          <button
            onClick={() => setCurrentScreen('subscription')}
            className={`flex flex-col items-center py-2 px-3 rounded-lg transition-colors ${
              currentScreen === 'subscription'
                ? 'text-yellow-400 bg-gray-700'
                : 'text-gray-400 hover:text-white'
            }`}
          >
            <span className="text-xl mb-1">⚡</span>
            <span className="text-xs">Upgrade</span>
          </button>
        </div>
      </nav>
    </div>
  )
}
