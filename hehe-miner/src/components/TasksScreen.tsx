'use client'

import { useState, useEffect } from 'react'
import { useAuth } from '@/contexts/AuthContext'
import { apiClient } from '@/lib/api'

interface Task {
  id: string
  title: string
  description: string
  reward: number
  link?: string
  attachment?: string
  isCompleted: boolean
  isRewardClaimed: boolean
}

export default function TasksScreen() {
  const { updateUser } = useAuth()
  const [tasks, setTasks] = useState<Task[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [completingTask, setCompletingTask] = useState<string | null>(null)
  const [error, setError] = useState('')
  const [success, setSuccess] = useState('')

  useEffect(() => {
    fetchTasks()
  }, [])

  const fetchTasks = async () => {
    try {
      const response = await apiClient.getTasks()
      if (response.success) {
        setTasks(response.tasks)
      } else {
        setError(response.error || 'Failed to fetch tasks')
      }
    } catch (error) {
      setError('Network error occurred')
    } finally {
      setIsLoading(false)
    }
  }

  const handleCompleteTask = async (task: Task) => {
    if (task.link) {
      // Open the link in a new tab
      window.open(task.link, '_blank')
    }

    setCompletingTask(task.id)
    setError('')
    setSuccess('')

    try {
      const response = await apiClient.completeTask(task.id)
      
      if (response.success) {
        setSuccess(`Task completed! Earned ${response.reward} HEHE tokens`)
        
        // Update the task in the list
        setTasks(prevTasks => 
          prevTasks.map(t => 
            t.id === task.id 
              ? { ...t, isCompleted: true, isRewardClaimed: true }
              : t
          )
        )

        // Update user balance
        updateUser({ totalBalance: response.newBalance })
      } else {
        setError(response.error || 'Failed to complete task')
      }
    } catch (error) {
      setError('Network error occurred')
    } finally {
      setCompletingTask(null)
    }
  }

  if (isLoading) {
    return (
      <div className="p-6 max-w-md mx-auto">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-yellow-400 mx-auto mb-4"></div>
          <p className="text-gray-400">Loading tasks...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="p-6 max-w-md mx-auto">
      <div className="text-center mb-6">
        <h2 className="text-2xl font-bold text-white mb-2">Tasks</h2>
        <p className="text-gray-400">Complete tasks to earn bonus HEHE tokens</p>
      </div>

      {/* Status Messages */}
      {error && (
        <div className="bg-red-500/10 border border-red-500/20 rounded-lg p-3 mb-4">
          <p className="text-red-400 text-sm">{error}</p>
        </div>
      )}
      
      {success && (
        <div className="bg-green-500/10 border border-green-500/20 rounded-lg p-3 mb-4">
          <p className="text-green-400 text-sm">{success}</p>
        </div>
      )}

      {/* Tasks List */}
      <div className="space-y-4">
        {tasks.length === 0 ? (
          <div className="text-center py-8">
            <p className="text-gray-400">No tasks available at the moment</p>
          </div>
        ) : (
          tasks.map((task) => (
            <div
              key={task.id}
              className={`bg-gray-800 rounded-lg p-4 border ${
                task.isCompleted 
                  ? 'border-green-500/30 bg-green-500/5' 
                  : 'border-gray-700'
              }`}
            >
              <div className="flex items-start justify-between mb-3">
                <div className="flex-1">
                  <h3 className="text-white font-semibold mb-1">{task.title}</h3>
                  <p className="text-gray-400 text-sm mb-2">{task.description}</p>
                  <div className="flex items-center space-x-2">
                    <span className="text-yellow-400 font-semibold">
                      +{task.reward} HEHE
                    </span>
                    {task.isCompleted && (
                      <span className="text-green-400 text-sm">✓ Completed</span>
                    )}
                  </div>
                </div>
                
                <div className="ml-4">
                  {task.isCompleted ? (
                    <div className="w-10 h-10 bg-green-500 rounded-full flex items-center justify-center">
                      <span className="text-white text-sm">✓</span>
                    </div>
                  ) : (
                    <button
                      onClick={() => handleCompleteTask(task)}
                      disabled={completingTask === task.id}
                      className="bg-blue-600 hover:bg-blue-700 disabled:bg-blue-800 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors"
                    >
                      {completingTask === task.id ? 'Completing...' : 'Do Task'}
                    </button>
                  )}
                </div>
              </div>
              
              {task.link && !task.isCompleted && (
                <div className="text-xs text-gray-500 mt-2">
                  💡 Clicking "Do Task" will open the link and mark the task as complete
                </div>
              )}
            </div>
          ))
        )}
      </div>

      {/* Stats */}
      <div className="mt-8 bg-gray-800 rounded-lg p-4 border border-gray-700">
        <h3 className="text-white font-semibold mb-2">Task Statistics</h3>
        <div className="grid grid-cols-2 gap-4 text-center">
          <div>
            <p className="text-gray-400 text-sm">Completed</p>
            <p className="text-white text-xl font-semibold">
              {tasks.filter(t => t.isCompleted).length}
            </p>
          </div>
          <div>
            <p className="text-gray-400 text-sm">Available</p>
            <p className="text-white text-xl font-semibold">
              {tasks.filter(t => !t.isCompleted).length}
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}
