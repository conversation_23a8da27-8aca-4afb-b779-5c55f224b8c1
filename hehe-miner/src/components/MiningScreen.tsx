'use client'

import { useState, useEffect } from 'react'
import { useAuth } from '@/contexts/AuthContext'
import { apiClient } from '@/lib/api'

export default function MiningScreen() {
  const { user, updateUser } = useAuth()
  const [isLoading, setIsLoading] = useState(false)
  const [timeRemaining, setTimeRemaining] = useState(0)
  const [canClaim, setCanClaim] = useState(false)
  const [error, setError] = useState('')
  const [success, setSuccess] = useState('')

  useEffect(() => {
    if (user?.currentMiningSession) {
      const interval = setInterval(() => {
        updateMiningTimer()
      }, 1000)

      return () => clearInterval(interval)
    }
  }, [user?.currentMiningSession])

  const updateMiningTimer = () => {
    if (!user?.currentMiningSession) return

    const startTime = new Date(user.currentMiningSession.startTime).getTime()
    const miningDuration = 4 * 60 * 60 * 1000 // 4 hours
    const endTime = startTime + miningDuration
    const now = Date.now()
    const remaining = endTime - now

    if (remaining <= 0) {
      setTimeRemaining(0)
      setCanClaim(true)
    } else {
      setTimeRemaining(remaining)
      setCanClaim(false)
    }
  }

  const formatTime = (milliseconds: number) => {
    const hours = Math.floor(milliseconds / (1000 * 60 * 60))
    const minutes = Math.floor((milliseconds % (1000 * 60 * 60)) / (1000 * 60))
    const seconds = Math.floor((milliseconds % (1000 * 60)) / 1000)
    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`
  }

  const handleStartMining = async () => {
    if (!user?.hasBasicPlan) {
      setError('You need to purchase the basic plan first!')
      return
    }

    setIsLoading(true)
    setError('')
    setSuccess('')

    try {
      const response = await apiClient.startMining()
      
      if (response.success) {
        setSuccess('Mining started successfully!')
        // Refresh user profile
        const profileResponse = await apiClient.getUserProfile()
        if (profileResponse.success) {
          updateUser(profileResponse.user)
        }
      } else {
        setError(response.error || 'Failed to start mining')
      }
    } catch (error) {
      setError('Network error occurred')
    } finally {
      setIsLoading(false)
    }
  }

  const handleClaimTokens = async () => {
    if (!user?.currentMiningSession) return

    setIsLoading(true)
    setError('')
    setSuccess('')

    try {
      const response = await apiClient.claimMining(user.currentMiningSession.id)
      
      if (response.success) {
        setSuccess(`Claimed ${response.tokensEarned} HEHE tokens!`)
        // Refresh user profile
        const profileResponse = await apiClient.getUserProfile()
        if (profileResponse.success) {
          updateUser(profileResponse.user)
        }
      } else {
        setError(response.error || 'Failed to claim tokens')
      }
    } catch (error) {
      setError('Network error occurred')
    } finally {
      setIsLoading(false)
    }
  }

  const getMiningProgress = () => {
    if (!user?.currentMiningSession || timeRemaining <= 0) return 100
    
    const startTime = new Date(user.currentMiningSession.startTime).getTime()
    const miningDuration = 4 * 60 * 60 * 1000 // 4 hours
    const elapsed = Date.now() - startTime
    return Math.min((elapsed / miningDuration) * 100, 100)
  }

  return (
    <div className="p-6 max-w-md mx-auto">
      {/* Status Messages */}
      {error && (
        <div className="bg-red-500/10 border border-red-500/20 rounded-lg p-3 mb-4">
          <p className="text-red-400 text-sm">{error}</p>
        </div>
      )}
      
      {success && (
        <div className="bg-green-500/10 border border-green-500/20 rounded-lg p-3 mb-4">
          <p className="text-green-400 text-sm">{success}</p>
        </div>
      )}

      {/* Mining Status Card */}
      <div className="bg-gray-800 rounded-2xl p-6 mb-6 border border-gray-700">
        <div className="text-center">
          <div className="w-24 h-24 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-full mx-auto mb-4 flex items-center justify-center">
            <span className="text-3xl">⛏️</span>
          </div>
          
          <h2 className="text-2xl font-bold text-white mb-2">Mining Status</h2>
          
          {!user?.hasBasicPlan ? (
            <div className="text-center">
              <p className="text-gray-400 mb-4">Purchase the basic plan to start mining!</p>
              <div className="bg-yellow-500/10 border border-yellow-500/20 rounded-lg p-4">
                <p className="text-yellow-400 text-sm">
                  💡 Basic Plan: $1 - Unlock mining 4 HEHE tokens every 4 hours
                </p>
              </div>
            </div>
          ) : user?.currentMiningSession && timeRemaining > 0 ? (
            <div>
              <p className="text-gray-400 mb-4">Mining in progress...</p>
              <div className="mb-4">
                <div className="bg-gray-700 rounded-full h-3 mb-2">
                  <div 
                    className="bg-gradient-to-r from-yellow-400 to-orange-500 h-3 rounded-full transition-all duration-1000"
                    style={{ width: `${getMiningProgress()}%` }}
                  ></div>
                </div>
                <p className="text-sm text-gray-400">
                  Progress: {getMiningProgress().toFixed(1)}%
                </p>
              </div>
              <div className="text-3xl font-mono text-yellow-400 mb-2">
                {formatTime(timeRemaining)}
              </div>
              <p className="text-gray-400 text-sm">Time remaining</p>
              <p className="text-white mt-2">
                Earning: {user.currentMiningSession.tokensEarned} HEHE
              </p>
            </div>
          ) : canClaim && user?.currentMiningSession ? (
            <div>
              <p className="text-green-400 mb-4 text-lg font-semibold">Mining Complete! 🎉</p>
              <p className="text-white mb-4">
                Ready to claim: {user.currentMiningSession.tokensEarned} HEHE
              </p>
              <button
                onClick={handleClaimTokens}
                disabled={isLoading}
                className="w-full bg-green-600 hover:bg-green-700 disabled:bg-green-800 text-white font-semibold py-3 px-4 rounded-lg transition-colors"
              >
                {isLoading ? 'Claiming...' : 'Claim Tokens'}
              </button>
            </div>
          ) : (
            <div>
              <p className="text-gray-400 mb-4">Ready to mine!</p>
              <p className="text-white mb-4">
                Mining Power: {user?.miningPower} HEHE per 4 hours
              </p>
              <button
                onClick={handleStartMining}
                disabled={isLoading}
                className="w-full bg-yellow-600 hover:bg-yellow-700 disabled:bg-yellow-800 text-white font-semibold py-3 px-4 rounded-lg transition-colors"
              >
                {isLoading ? 'Starting...' : 'Start Mining'}
              </button>
            </div>
          )}
        </div>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-2 gap-4">
        <div className="bg-gray-800 rounded-lg p-4 border border-gray-700">
          <p className="text-gray-400 text-sm">Total Balance</p>
          <p className="text-white text-xl font-semibold">
            {user?.totalBalance?.toFixed(2) || '0.00'} HEHE
          </p>
        </div>
        <div className="bg-gray-800 rounded-lg p-4 border border-gray-700">
          <p className="text-gray-400 text-sm">Mining Power</p>
          <p className="text-white text-xl font-semibold">
            {user?.miningPower?.toFixed(2) || '4.00'}/4h
          </p>
        </div>
      </div>
    </div>
  )
}
