(()=>{var e={};e.id=492,e.ids=[492],e.modules={846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1092:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,6346,23)),Promise.resolve().then(r.t.bind(r,7924,23)),Promise.resolve().then(r.t.bind(r,5656,23)),Promise.resolve().then(r.t.bind(r,99,23)),Promise.resolve().then(r.t.bind(r,8243,23)),Promise.resolve().then(r.t.bind(r,8827,23)),Promise.resolve().then(r.t.bind(r,2763,23)),Promise.resolve().then(r.t.bind(r,7173,23))},1135:()=>{},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3213:(e,t,r)=>{"use strict";r.d(t,{A:()=>a,AuthProvider:()=>i});var n=r(687),o=r(3210);let s=(0,o.createContext)(void 0);function i({children:e}){let[t,r]=(0,o.useState)(null),[i,a]=(0,o.useState)(null),[l,d]=(0,o.useState)(!0);return(0,n.jsx)(s.Provider,{value:{user:t,token:i,login:(e,t)=>{r(e),a(t),localStorage.setItem("auth_token",t),localStorage.setItem("user_data",JSON.stringify(e))},logout:()=>{r(null),a(null),localStorage.removeItem("auth_token"),localStorage.removeItem("user_data")},updateUser:e=>{if(t){let n={...t,...e};r(n),localStorage.setItem("user_data",JSON.stringify(n))}},isLoading:l},children:e})}function a(){let e=(0,o.useContext)(s);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3560:(e,t,r)=>{Promise.resolve().then(r.bind(r,9131))},3873:e=>{"use strict";e.exports=require("path")},4431:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>u,metadata:()=>d});var n=r(7413),o=r(2202),s=r.n(o),i=r(4988),a=r.n(i);r(1135);var l=r(9131);let d={title:"Hehe Miner - Telegram Mining Game",description:"Mine Hehe tokens in this fun Telegram-based mining game"};function u({children:e}){return(0,n.jsx)("html",{lang:"en",children:(0,n.jsx)("body",{className:`${s().variable} ${a().variable} antialiased bg-gray-900 text-white min-h-screen`,children:(0,n.jsx)(l.AuthProvider,{children:e})})})}},4708:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,6444,23)),Promise.resolve().then(r.t.bind(r,6042,23)),Promise.resolve().then(r.t.bind(r,8170,23)),Promise.resolve().then(r.t.bind(r,9477,23)),Promise.resolve().then(r.t.bind(r,9345,23)),Promise.resolve().then(r.t.bind(r,2089,23)),Promise.resolve().then(r.t.bind(r,6577,23)),Promise.resolve().then(r.t.bind(r,1307,23))},7112:(e,t,r)=>{Promise.resolve().then(r.bind(r,3213))},9093:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>h,pages:()=>u,routeModule:()=>c,tree:()=>d});var n=r(5239),o=r(8088),s=r(8170),i=r.n(s),a=r(893),l={};for(let e in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>a[e]);r.d(t,l);let d={children:["",{children:["/_not-found",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.t.bind(r,7398,23)),"next/dist/client/components/not-found-error"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,4431)),"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,5284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,u=[],h={require:r,loadChunk:()=>Promise.resolve()},c=new n.AppPageRouteModule({definition:{kind:o.RouteKind.APP_PAGE,page:"/_not-found/page",pathname:"/_not-found",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9131:(e,t,r)=>{"use strict";r.d(t,{AuthProvider:()=>o});var n=r(2907);let o=(0,n.registerClientReference)(function(){throw Error("Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/contexts/AuthContext.tsx","AuthProvider");(0,n.registerClientReference)(function(){throw Error("Attempted to call useAuth() from the server but useAuth is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/contexts/AuthContext.tsx","useAuth")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[447,169],()=>r(9093));module.exports=n})();