(()=>{var e={};e.id=151,e.ids=[151],e.modules={703:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>x,routeModule:()=>d,serverHooks:()=>g,workAsyncStorage:()=>l,workUnitAsyncStorage:()=>m});var s={};t.r(s),t.d(s,{POST:()=>p});var n=t(6559),i=t(8088),a=t(7719),o=t(2190),u=t(4747),c=t(2909);async function p(e){try{let r=(0,c.rL)(e);if(!r)return o.NextResponse.json({success:!1,error:"Unauthorized"},{status:401});let{sessionId:t}=await e.json(),s=await u.z.miningSession.findFirst({where:{id:t,userId:r.id,isCompleted:!1}});if(!s)return o.NextResponse.json({success:!1,error:"Mining session not found or already completed"},{status:404});let n=new Date(s.startTime.getTime()+144e5),i=new Date;if(i<n)return o.NextResponse.json({success:!1,error:"Mining not completed yet",timeRemaining:n.getTime()-i.getTime()},{status:400});let[a,p]=await u.z.$transaction([u.z.miningSession.update({where:{id:t},data:{isCompleted:!0,endTime:i}}),u.z.user.update({where:{id:r.id},data:{totalBalance:{increment:s.tokensEarned}}})]);return o.NextResponse.json({success:!0,tokensEarned:s.tokensEarned,newBalance:p.totalBalance})}catch(e){return console.error("Mining claim error:",e),o.NextResponse.json({success:!1,error:"Internal server error"},{status:500})}}let d=new n.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/mining/claim/route",pathname:"/api/mining/claim",filename:"route",bundlePath:"app/api/mining/claim/route"},resolvedPagePath:"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/app/api/mining/claim/route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:l,workUnitAsyncStorage:m,serverHooks:g}=d;function x(){return(0,a.patchFetch)({workAsyncStorage:l,workUnitAsyncStorage:m})}},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},2909:(e,r,t)=>{"use strict";t.d(r,{HU:()=>a,rL:()=>o});var s=t(3205),n=t.n(s);let i=process.env.JWT_SECRET||"fallback-secret";function a(e){return n().sign(e,i,{expiresIn:"7d"})}function o(e){let r=e.headers.get("authorization");if(!r?.startsWith("Bearer "))return null;var t=r.substring(7);try{return n().verify(t,i)}catch{return null}}},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4747:(e,r,t)=>{"use strict";t.d(r,{z:()=>n});let s=require("@prisma/client"),n=globalThis.prisma??new s.PrismaClient},4870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},5511:e=>{"use strict";e.exports=require("crypto")},6487:()=>{},7910:e=>{"use strict";e.exports=require("stream")},8335:()=>{},8354:e=>{"use strict";e.exports=require("util")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9428:e=>{"use strict";e.exports=require("buffer")}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[447,580,205],()=>t(703));module.exports=s})();