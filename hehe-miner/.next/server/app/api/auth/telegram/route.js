/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/auth/telegram/route";
exports.ids = ["app/api/auth/telegram/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fauth%2Ftelegram%2Froute&page=%2Fapi%2Fauth%2Ftelegram%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Ftelegram%2Froute.ts&appDir=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FHehe-coin%2Fhehe-miner%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FHehe-coin%2Fhehe-miner&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fauth%2Ftelegram%2Froute&page=%2Fapi%2Fauth%2Ftelegram%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Ftelegram%2Froute.ts&appDir=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FHehe-coin%2Fhehe-miner%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FHehe-coin%2Fhehe-miner&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _Users_tahafarooqui_Documents_augment_projects_Hehe_coin_hehe_miner_src_app_api_auth_telegram_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/auth/telegram/route.ts */ \"(rsc)/./src/app/api/auth/telegram/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/auth/telegram/route\",\n        pathname: \"/api/auth/telegram\",\n        filename: \"route\",\n        bundlePath: \"app/api/auth/telegram/route\"\n    },\n    resolvedPagePath: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/app/api/auth/telegram/route.ts\",\n    nextConfigOutput,\n    userland: _Users_tahafarooqui_Documents_augment_projects_Hehe_coin_hehe_miner_src_app_api_auth_telegram_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fauth%2Ftelegram%2Froute&page=%2Fapi%2Fauth%2Ftelegram%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Ftelegram%2Froute.ts&appDir=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FHehe-coin%2Fhehe-miner%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FHehe-coin%2Fhehe-miner&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/auth/telegram/route.ts":
/*!********************************************!*\
  !*** ./src/app/api/auth/telegram/route.ts ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_prisma__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/prisma */ \"(rsc)/./src/lib/prisma.ts\");\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/auth */ \"(rsc)/./src/lib/auth.ts\");\n/* harmony import */ var _lib_telegram__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/telegram */ \"(rsc)/./src/lib/telegram.ts\");\n\n\n\n\nasync function POST(request) {\n    try {\n        const body = await request.json();\n        // For local development, allow mock authentication\n        if (process.env.ENABLE_MOCK_AUTH === 'true' && body.mock) {\n            let user = await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.user.findUnique({\n                where: {\n                    telegramId: 'mock-telegram-id'\n                }\n            });\n            if (!user) {\n                user = await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.user.create({\n                    data: {\n                        telegramId: 'mock-telegram-id',\n                        username: 'mock_user',\n                        firstName: 'Mock',\n                        lastName: 'User',\n                        hasBasicPlan: true // Give mock user the basic plan\n                    }\n                });\n            }\n            const token = (0,_lib_auth__WEBPACK_IMPORTED_MODULE_2__.generateToken)({\n                id: user.id,\n                telegramId: user.telegramId,\n                username: user.username || undefined\n            });\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: true,\n                token,\n                user: {\n                    id: user.id,\n                    telegramId: user.telegramId,\n                    username: user.username,\n                    firstName: user.firstName,\n                    lastName: user.lastName,\n                    totalBalance: user.totalBalance,\n                    miningPower: user.miningPower,\n                    hasBasicPlan: user.hasBasicPlan\n                }\n            });\n        }\n        const telegramData = body;\n        // Validate Telegram authentication\n        if (!(0,_lib_telegram__WEBPACK_IMPORTED_MODULE_3__.validateTelegramAuth)(telegramData)) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'Invalid Telegram authentication'\n            }, {\n                status: 401\n            });\n        }\n        // Check if auth is expired\n        if ((0,_lib_telegram__WEBPACK_IMPORTED_MODULE_3__.isTelegramAuthExpired)(telegramData.auth_date)) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'Authentication expired'\n            }, {\n                status: 401\n            });\n        }\n        // Find or create user\n        let user = await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.user.findUnique({\n            where: {\n                telegramId: telegramData.id.toString()\n            }\n        });\n        if (!user) {\n            user = await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.user.create({\n                data: {\n                    telegramId: telegramData.id.toString(),\n                    username: telegramData.username,\n                    firstName: telegramData.first_name,\n                    lastName: telegramData.last_name\n                }\n            });\n        }\n        const token = (0,_lib_auth__WEBPACK_IMPORTED_MODULE_2__.generateToken)({\n            id: user.id,\n            telegramId: user.telegramId,\n            username: user.username || undefined\n        });\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            token,\n            user: {\n                id: user.id,\n                telegramId: user.telegramId,\n                username: user.username,\n                firstName: user.firstName,\n                lastName: user.lastName,\n                totalBalance: user.totalBalance,\n                miningPower: user.miningPower,\n                hasBasicPlan: user.hasBasicPlan\n            }\n        });\n    } catch (error) {\n        console.error('Auth error:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'Internal server error'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/auth/telegram/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/auth.ts":
/*!*************************!*\
  !*** ./src/lib/auth.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createMockUser: () => (/* binding */ createMockUser),\n/* harmony export */   generateToken: () => (/* binding */ generateToken),\n/* harmony export */   getUserFromRequest: () => (/* binding */ getUserFromRequest),\n/* harmony export */   verifyToken: () => (/* binding */ verifyToken)\n/* harmony export */ });\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! jsonwebtoken */ \"(rsc)/./node_modules/jsonwebtoken/index.js\");\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(jsonwebtoken__WEBPACK_IMPORTED_MODULE_0__);\n\nconst JWT_SECRET = process.env.JWT_SECRET || 'fallback-secret';\nfunction generateToken(user) {\n    return jsonwebtoken__WEBPACK_IMPORTED_MODULE_0___default().sign(user, JWT_SECRET, {\n        expiresIn: '7d'\n    });\n}\nfunction verifyToken(token) {\n    try {\n        return jsonwebtoken__WEBPACK_IMPORTED_MODULE_0___default().verify(token, JWT_SECRET);\n    } catch  {\n        return null;\n    }\n}\nfunction getUserFromRequest(request) {\n    const authHeader = request.headers.get('authorization');\n    if (!authHeader?.startsWith('Bearer ')) {\n        return null;\n    }\n    const token = authHeader.substring(7);\n    return verifyToken(token);\n}\n// Mock authentication for local development\nfunction createMockUser() {\n    return {\n        id: 'mock-user-id',\n        telegramId: 'mock-telegram-id',\n        username: 'mock_user'\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/auth.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/prisma.ts":
/*!***************************!*\
  !*** ./src/lib/prisma.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   prisma: () => (/* binding */ prisma)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\nconst globalForPrisma = globalThis;\nconst prisma = globalForPrisma.prisma ?? new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient();\nif (true) globalForPrisma.prisma = prisma;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL3ByaXNtYS50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBNkM7QUFFN0MsTUFBTUMsa0JBQWtCQztBQUlqQixNQUFNQyxTQUFTRixnQkFBZ0JFLE1BQU0sSUFBSSxJQUFJSCx3REFBWUEsR0FBRTtBQUVsRSxJQUFJSSxJQUFxQyxFQUFFSCxnQkFBZ0JFLE1BQU0sR0FBR0EiLCJzb3VyY2VzIjpbIi9Vc2Vycy90YWhhZmFyb29xdWkvRG9jdW1lbnRzL2F1Z21lbnQtcHJvamVjdHMvSGVoZS1jb2luL2hlaGUtbWluZXIvc3JjL2xpYi9wcmlzbWEudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgUHJpc21hQ2xpZW50IH0gZnJvbSAnQHByaXNtYS9jbGllbnQnXG5cbmNvbnN0IGdsb2JhbEZvclByaXNtYSA9IGdsb2JhbFRoaXMgYXMgdW5rbm93biBhcyB7XG4gIHByaXNtYTogUHJpc21hQ2xpZW50IHwgdW5kZWZpbmVkXG59XG5cbmV4cG9ydCBjb25zdCBwcmlzbWEgPSBnbG9iYWxGb3JQcmlzbWEucHJpc21hID8/IG5ldyBQcmlzbWFDbGllbnQoKVxuXG5pZiAocHJvY2Vzcy5lbnYuTk9ERV9FTlYgIT09ICdwcm9kdWN0aW9uJykgZ2xvYmFsRm9yUHJpc21hLnByaXNtYSA9IHByaXNtYVxuIl0sIm5hbWVzIjpbIlByaXNtYUNsaWVudCIsImdsb2JhbEZvclByaXNtYSIsImdsb2JhbFRoaXMiLCJwcmlzbWEiLCJwcm9jZXNzIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/prisma.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/telegram.ts":
/*!*****************************!*\
  !*** ./src/lib/telegram.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getTelegramUser: () => (/* binding */ getTelegramUser),\n/* harmony export */   getTelegramWebApp: () => (/* binding */ getTelegramWebApp),\n/* harmony export */   initTelegramWebApp: () => (/* binding */ initTelegramWebApp),\n/* harmony export */   isTelegramAuthExpired: () => (/* binding */ isTelegramAuthExpired),\n/* harmony export */   isTelegramWebApp: () => (/* binding */ isTelegramWebApp),\n/* harmony export */   triggerHapticFeedback: () => (/* binding */ triggerHapticFeedback),\n/* harmony export */   validateTelegramAuth: () => (/* binding */ validateTelegramAuth)\n/* harmony export */ });\n/* harmony import */ var crypto__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! crypto */ \"crypto\");\n/* harmony import */ var crypto__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(crypto__WEBPACK_IMPORTED_MODULE_0__);\n\nfunction validateTelegramAuth(data) {\n    const botToken = process.env.TELEGRAM_BOT_TOKEN;\n    if (!botToken) {\n        console.warn('TELEGRAM_BOT_TOKEN not set, skipping validation');\n        return true // Allow in development\n        ;\n    }\n    const { hash, ...userData } = data;\n    const dataCheckString = Object.keys(userData).sort().map((key)=>`${key}=${userData[key]}`).join('\\n');\n    const secretKey = crypto__WEBPACK_IMPORTED_MODULE_0___default().createHash('sha256').update(botToken).digest();\n    const calculatedHash = crypto__WEBPACK_IMPORTED_MODULE_0___default().createHmac('sha256', secretKey).update(dataCheckString).digest('hex');\n    return calculatedHash === hash;\n}\nfunction isTelegramAuthExpired(authDate) {\n    const now = Math.floor(Date.now() / 1000);\n    return now - authDate > 86400 // 24 hours\n    ;\n}\n/**\n * Gets Telegram Web App instance if available\n */ function getTelegramWebApp() {\n    if (false) {}\n    return null;\n}\n/**\n * Initializes Telegram Web App\n */ function initTelegramWebApp() {\n    const webApp = getTelegramWebApp();\n    if (webApp) {\n        webApp.ready();\n        webApp.expand();\n        return webApp;\n    }\n    return null;\n}\n/**\n * Triggers haptic feedback\n */ function triggerHapticFeedback(type, style) {\n    const webApp = getTelegramWebApp();\n    if (webApp?.HapticFeedback) {\n        if (type === 'impact') {\n            webApp.HapticFeedback.impactOccurred(style || 'medium');\n        } else {\n            webApp.HapticFeedback.notificationOccurred(style || 'success');\n        }\n    }\n}\n/**\n * Checks if running inside Telegram Web App\n */ function isTelegramWebApp() {\n    return  false && 0;\n}\n/**\n * Gets user data from Telegram Web App\n */ function getTelegramUser() {\n    const webApp = getTelegramWebApp();\n    return webApp?.initDataUnsafe?.user || null;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL3RlbGVncmFtLnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7OztBQUEyQjtBQVlwQixTQUFTQyxxQkFBcUJDLElBQWtCO0lBQ3JELE1BQU1DLFdBQVdDLFFBQVFDLEdBQUcsQ0FBQ0Msa0JBQWtCO0lBQy9DLElBQUksQ0FBQ0gsVUFBVTtRQUNiSSxRQUFRQyxJQUFJLENBQUM7UUFDYixPQUFPLEtBQUssdUJBQXVCOztJQUNyQztJQUVBLE1BQU0sRUFBRUMsSUFBSSxFQUFFLEdBQUdDLFVBQVUsR0FBR1I7SUFDOUIsTUFBTVMsa0JBQWtCQyxPQUFPQyxJQUFJLENBQUNILFVBQ2pDSSxJQUFJLEdBQ0pDLEdBQUcsQ0FBQ0MsQ0FBQUEsTUFBTyxHQUFHQSxJQUFJLENBQUMsRUFBRU4sUUFBUSxDQUFDTSxJQUE2QixFQUFFLEVBQzdEQyxJQUFJLENBQUM7SUFFUixNQUFNQyxZQUFZbEIsd0RBQWlCLENBQUMsVUFBVW9CLE1BQU0sQ0FBQ2pCLFVBQVVrQixNQUFNO0lBQ3JFLE1BQU1DLGlCQUFpQnRCLHdEQUNWLENBQUMsVUFBVWtCLFdBQ3JCRSxNQUFNLENBQUNULGlCQUNQVSxNQUFNLENBQUM7SUFFVixPQUFPQyxtQkFBbUJiO0FBQzVCO0FBRU8sU0FBU2Usc0JBQXNCQyxRQUFnQjtJQUNwRCxNQUFNQyxNQUFNQyxLQUFLQyxLQUFLLENBQUNDLEtBQUtILEdBQUcsS0FBSztJQUNwQyxPQUFPQSxNQUFNRCxXQUFXLE1BQU0sV0FBVzs7QUFDM0M7QUE0Q0E7O0NBRUMsR0FDTSxTQUFTSztJQUNkLElBQUksS0FBd0RHLEVBQUUsRUFFN0Q7SUFDRCxPQUFPO0FBQ1Q7QUFFQTs7Q0FFQyxHQUNNLFNBQVNDO0lBQ2QsTUFBTUMsU0FBU0w7SUFFZixJQUFJSyxRQUFRO1FBQ1ZBLE9BQU9DLEtBQUs7UUFDWkQsT0FBT0UsTUFBTTtRQUNiLE9BQU9GO0lBQ1Q7SUFFQSxPQUFPO0FBQ1Q7QUFFQTs7Q0FFQyxHQUNNLFNBQVNHLHNCQUNkQyxJQUErQixFQUMvQkMsS0FBc0U7SUFFdEUsTUFBTUwsU0FBU0w7SUFFZixJQUFJSyxRQUFRTSxnQkFBZ0I7UUFDMUIsSUFBSUYsU0FBUyxVQUFVO1lBQ3JCSixPQUFPTSxjQUFjLENBQUNDLGNBQWMsQ0FBQ0YsU0FBZ0I7UUFDdkQsT0FBTztZQUNMTCxPQUFPTSxjQUFjLENBQUNFLG9CQUFvQixDQUFDSCxTQUFnQjtRQUM3RDtJQUNGO0FBQ0Y7QUFFQTs7Q0FFQyxHQUNNLFNBQVNJO0lBQ2QsT0FBTyxNQUE2QixJQUFJLENBQXlCWDtBQUNuRTtBQUVBOztDQUVDLEdBQ00sU0FBU1k7SUFDZCxNQUFNVixTQUFTTDtJQUNmLE9BQU9LLFFBQVFXLGdCQUFnQkMsUUFBUTtBQUN6QyIsInNvdXJjZXMiOlsiL1VzZXJzL3RhaGFmYXJvb3F1aS9Eb2N1bWVudHMvYXVnbWVudC1wcm9qZWN0cy9IZWhlLWNvaW4vaGVoZS1taW5lci9zcmMvbGliL3RlbGVncmFtLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBjcnlwdG8gZnJvbSAnY3J5cHRvJ1xuXG5leHBvcnQgaW50ZXJmYWNlIFRlbGVncmFtVXNlciB7XG4gIGlkOiBudW1iZXJcbiAgZmlyc3RfbmFtZTogc3RyaW5nXG4gIGxhc3RfbmFtZT86IHN0cmluZ1xuICB1c2VybmFtZT86IHN0cmluZ1xuICBwaG90b191cmw/OiBzdHJpbmdcbiAgYXV0aF9kYXRlOiBudW1iZXJcbiAgaGFzaDogc3RyaW5nXG59XG5cbmV4cG9ydCBmdW5jdGlvbiB2YWxpZGF0ZVRlbGVncmFtQXV0aChkYXRhOiBUZWxlZ3JhbVVzZXIpOiBib29sZWFuIHtcbiAgY29uc3QgYm90VG9rZW4gPSBwcm9jZXNzLmVudi5URUxFR1JBTV9CT1RfVE9LRU5cbiAgaWYgKCFib3RUb2tlbikge1xuICAgIGNvbnNvbGUud2FybignVEVMRUdSQU1fQk9UX1RPS0VOIG5vdCBzZXQsIHNraXBwaW5nIHZhbGlkYXRpb24nKVxuICAgIHJldHVybiB0cnVlIC8vIEFsbG93IGluIGRldmVsb3BtZW50XG4gIH1cblxuICBjb25zdCB7IGhhc2gsIC4uLnVzZXJEYXRhIH0gPSBkYXRhXG4gIGNvbnN0IGRhdGFDaGVja1N0cmluZyA9IE9iamVjdC5rZXlzKHVzZXJEYXRhKVxuICAgIC5zb3J0KClcbiAgICAubWFwKGtleSA9PiBgJHtrZXl9PSR7dXNlckRhdGFba2V5IGFzIGtleW9mIHR5cGVvZiB1c2VyRGF0YV19YClcbiAgICAuam9pbignXFxuJylcblxuICBjb25zdCBzZWNyZXRLZXkgPSBjcnlwdG8uY3JlYXRlSGFzaCgnc2hhMjU2JykudXBkYXRlKGJvdFRva2VuKS5kaWdlc3QoKVxuICBjb25zdCBjYWxjdWxhdGVkSGFzaCA9IGNyeXB0b1xuICAgIC5jcmVhdGVIbWFjKCdzaGEyNTYnLCBzZWNyZXRLZXkpXG4gICAgLnVwZGF0ZShkYXRhQ2hlY2tTdHJpbmcpXG4gICAgLmRpZ2VzdCgnaGV4JylcblxuICByZXR1cm4gY2FsY3VsYXRlZEhhc2ggPT09IGhhc2hcbn1cblxuZXhwb3J0IGZ1bmN0aW9uIGlzVGVsZWdyYW1BdXRoRXhwaXJlZChhdXRoRGF0ZTogbnVtYmVyKTogYm9vbGVhbiB7XG4gIGNvbnN0IG5vdyA9IE1hdGguZmxvb3IoRGF0ZS5ub3coKSAvIDEwMDApXG4gIHJldHVybiBub3cgLSBhdXRoRGF0ZSA+IDg2NDAwIC8vIDI0IGhvdXJzXG59XG5cbi8vIFRlbGVncmFtIFdlYiBBcHAgdHlwZXMgYW5kIGZ1bmN0aW9uc1xuZXhwb3J0IGludGVyZmFjZSBUZWxlZ3JhbVdlYkFwcCB7XG4gIGluaXREYXRhOiBzdHJpbmdcbiAgaW5pdERhdGFVbnNhZmU6IHtcbiAgICB1c2VyPzogVGVsZWdyYW1Vc2VyXG4gICAgY2hhdF9pbnN0YW5jZT86IHN0cmluZ1xuICAgIHN0YXJ0X3BhcmFtPzogc3RyaW5nXG4gICAgYXV0aF9kYXRlPzogbnVtYmVyXG4gICAgaGFzaD86IHN0cmluZ1xuICB9XG4gIHZlcnNpb246IHN0cmluZ1xuICBwbGF0Zm9ybTogc3RyaW5nXG4gIGNvbG9yU2NoZW1lOiAnbGlnaHQnIHwgJ2RhcmsnXG4gIGlzRXhwYW5kZWQ6IGJvb2xlYW5cbiAgdmlld3BvcnRIZWlnaHQ6IG51bWJlclxuICByZWFkeTogKCkgPT4gdm9pZFxuICBleHBhbmQ6ICgpID0+IHZvaWRcbiAgY2xvc2U6ICgpID0+IHZvaWRcbiAgc2hvd1BvcHVwOiAocGFyYW1zOiB7XG4gICAgdGl0bGU/OiBzdHJpbmdcbiAgICBtZXNzYWdlOiBzdHJpbmdcbiAgICBidXR0b25zPzogQXJyYXk8e1xuICAgICAgaWQ/OiBzdHJpbmdcbiAgICAgIHR5cGU/OiAnZGVmYXVsdCcgfCAnb2snIHwgJ2Nsb3NlJyB8ICdjYW5jZWwnIHwgJ2Rlc3RydWN0aXZlJ1xuICAgICAgdGV4dD86IHN0cmluZ1xuICAgIH0+XG4gIH0sIGNhbGxiYWNrPzogKGJ1dHRvbklkOiBzdHJpbmcpID0+IHZvaWQpID0+IHZvaWRcbiAgc2hvd0FsZXJ0OiAobWVzc2FnZTogc3RyaW5nLCBjYWxsYmFjaz86ICgpID0+IHZvaWQpID0+IHZvaWRcbiAgSGFwdGljRmVlZGJhY2s6IHtcbiAgICBpbXBhY3RPY2N1cnJlZDogKHN0eWxlOiAnbGlnaHQnIHwgJ21lZGl1bScgfCAnaGVhdnknKSA9PiB2b2lkXG4gICAgbm90aWZpY2F0aW9uT2NjdXJyZWQ6ICh0eXBlOiAnZXJyb3InIHwgJ3N1Y2Nlc3MnIHwgJ3dhcm5pbmcnKSA9PiB2b2lkXG4gIH1cbn1cblxuZGVjbGFyZSBnbG9iYWwge1xuICBpbnRlcmZhY2UgV2luZG93IHtcbiAgICBUZWxlZ3JhbT86IHtcbiAgICAgIFdlYkFwcDogVGVsZWdyYW1XZWJBcHBcbiAgICB9XG4gIH1cbn1cblxuLyoqXG4gKiBHZXRzIFRlbGVncmFtIFdlYiBBcHAgaW5zdGFuY2UgaWYgYXZhaWxhYmxlXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBnZXRUZWxlZ3JhbVdlYkFwcCgpOiBUZWxlZ3JhbVdlYkFwcCB8IG51bGwge1xuICBpZiAodHlwZW9mIHdpbmRvdyAhPT0gJ3VuZGVmaW5lZCcgJiYgd2luZG93LlRlbGVncmFtPy5XZWJBcHApIHtcbiAgICByZXR1cm4gd2luZG93LlRlbGVncmFtLldlYkFwcFxuICB9XG4gIHJldHVybiBudWxsXG59XG5cbi8qKlxuICogSW5pdGlhbGl6ZXMgVGVsZWdyYW0gV2ViIEFwcFxuICovXG5leHBvcnQgZnVuY3Rpb24gaW5pdFRlbGVncmFtV2ViQXBwKCk6IFRlbGVncmFtV2ViQXBwIHwgbnVsbCB7XG4gIGNvbnN0IHdlYkFwcCA9IGdldFRlbGVncmFtV2ViQXBwKClcblxuICBpZiAod2ViQXBwKSB7XG4gICAgd2ViQXBwLnJlYWR5KClcbiAgICB3ZWJBcHAuZXhwYW5kKClcbiAgICByZXR1cm4gd2ViQXBwXG4gIH1cblxuICByZXR1cm4gbnVsbFxufVxuXG4vKipcbiAqIFRyaWdnZXJzIGhhcHRpYyBmZWVkYmFja1xuICovXG5leHBvcnQgZnVuY3Rpb24gdHJpZ2dlckhhcHRpY0ZlZWRiYWNrKFxuICB0eXBlOiAnaW1wYWN0JyB8ICdub3RpZmljYXRpb24nLFxuICBzdHlsZT86ICdsaWdodCcgfCAnbWVkaXVtJyB8ICdoZWF2eScgfCAnZXJyb3InIHwgJ3N1Y2Nlc3MnIHwgJ3dhcm5pbmcnXG4pIHtcbiAgY29uc3Qgd2ViQXBwID0gZ2V0VGVsZWdyYW1XZWJBcHAoKVxuXG4gIGlmICh3ZWJBcHA/LkhhcHRpY0ZlZWRiYWNrKSB7XG4gICAgaWYgKHR5cGUgPT09ICdpbXBhY3QnKSB7XG4gICAgICB3ZWJBcHAuSGFwdGljRmVlZGJhY2suaW1wYWN0T2NjdXJyZWQoc3R5bGUgYXMgYW55IHx8ICdtZWRpdW0nKVxuICAgIH0gZWxzZSB7XG4gICAgICB3ZWJBcHAuSGFwdGljRmVlZGJhY2subm90aWZpY2F0aW9uT2NjdXJyZWQoc3R5bGUgYXMgYW55IHx8ICdzdWNjZXNzJylcbiAgICB9XG4gIH1cbn1cblxuLyoqXG4gKiBDaGVja3MgaWYgcnVubmluZyBpbnNpZGUgVGVsZWdyYW0gV2ViIEFwcFxuICovXG5leHBvcnQgZnVuY3Rpb24gaXNUZWxlZ3JhbVdlYkFwcCgpOiBib29sZWFuIHtcbiAgcmV0dXJuIHR5cGVvZiB3aW5kb3cgIT09ICd1bmRlZmluZWQnICYmICEhd2luZG93LlRlbGVncmFtPy5XZWJBcHBcbn1cblxuLyoqXG4gKiBHZXRzIHVzZXIgZGF0YSBmcm9tIFRlbGVncmFtIFdlYiBBcHBcbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIGdldFRlbGVncmFtVXNlcigpOiBUZWxlZ3JhbVVzZXIgfCBudWxsIHtcbiAgY29uc3Qgd2ViQXBwID0gZ2V0VGVsZWdyYW1XZWJBcHAoKVxuICByZXR1cm4gd2ViQXBwPy5pbml0RGF0YVVuc2FmZT8udXNlciB8fCBudWxsXG59XG4iXSwibmFtZXMiOlsiY3J5cHRvIiwidmFsaWRhdGVUZWxlZ3JhbUF1dGgiLCJkYXRhIiwiYm90VG9rZW4iLCJwcm9jZXNzIiwiZW52IiwiVEVMRUdSQU1fQk9UX1RPS0VOIiwiY29uc29sZSIsIndhcm4iLCJoYXNoIiwidXNlckRhdGEiLCJkYXRhQ2hlY2tTdHJpbmciLCJPYmplY3QiLCJrZXlzIiwic29ydCIsIm1hcCIsImtleSIsImpvaW4iLCJzZWNyZXRLZXkiLCJjcmVhdGVIYXNoIiwidXBkYXRlIiwiZGlnZXN0IiwiY2FsY3VsYXRlZEhhc2giLCJjcmVhdGVIbWFjIiwiaXNUZWxlZ3JhbUF1dGhFeHBpcmVkIiwiYXV0aERhdGUiLCJub3ciLCJNYXRoIiwiZmxvb3IiLCJEYXRlIiwiZ2V0VGVsZWdyYW1XZWJBcHAiLCJ3aW5kb3ciLCJUZWxlZ3JhbSIsIldlYkFwcCIsImluaXRUZWxlZ3JhbVdlYkFwcCIsIndlYkFwcCIsInJlYWR5IiwiZXhwYW5kIiwidHJpZ2dlckhhcHRpY0ZlZWRiYWNrIiwidHlwZSIsInN0eWxlIiwiSGFwdGljRmVlZGJhY2siLCJpbXBhY3RPY2N1cnJlZCIsIm5vdGlmaWNhdGlvbk9jY3VycmVkIiwiaXNUZWxlZ3JhbVdlYkFwcCIsImdldFRlbGVncmFtVXNlciIsImluaXREYXRhVW5zYWZlIiwidXNlciJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/telegram.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("@prisma/client");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/semver","vendor-chunks/jsonwebtoken","vendor-chunks/jws","vendor-chunks/ecdsa-sig-formatter","vendor-chunks/safe-buffer","vendor-chunks/ms","vendor-chunks/lodash.once","vendor-chunks/lodash.isstring","vendor-chunks/lodash.isplainobject","vendor-chunks/lodash.isnumber","vendor-chunks/lodash.isinteger","vendor-chunks/lodash.isboolean","vendor-chunks/lodash.includes","vendor-chunks/jwa","vendor-chunks/buffer-equal-constant-time"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fauth%2Ftelegram%2Froute&page=%2Fapi%2Fauth%2Ftelegram%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Ftelegram%2Froute.ts&appDir=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FHehe-coin%2Fhehe-miner%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FHehe-coin%2Fhehe-miner&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();