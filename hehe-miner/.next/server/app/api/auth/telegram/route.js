(()=>{var e={};e.id=58,e.ids=[58],e.modules={846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},2909:(e,r,t)=>{"use strict";t.d(r,{HU:()=>i,rL:()=>u});var a=t(3205),s=t.n(a);let n=process.env.JWT_SECRET||"fallback-secret";function i(e){return s().sign(e,n,{expiresIn:"7d"})}function u(e){let r=e.headers.get("authorization");if(!r?.startsWith("Bearer "))return null;var t=r.substring(7);try{return s().verify(t,n)}catch{return null}}},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4747:(e,r,t)=>{"use strict";t.d(r,{z:()=>s});let a=require("@prisma/client"),s=globalThis.prisma??new a.PrismaClient},4870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},5511:e=>{"use strict";e.exports=require("crypto")},6487:()=>{},7910:e=>{"use strict";e.exports=require("stream")},8335:()=>{},8354:e=>{"use strict";e.exports=require("util")},8691:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>x,routeModule:()=>p,serverHooks:()=>f,workAsyncStorage:()=>g,workUnitAsyncStorage:()=>h});var a={};t.r(a),t.d(a,{POST:()=>d});var s=t(6559),n=t(8088),i=t(7719),u=t(2190),o=t(4747),l=t(2909),c=t(5511),m=t.n(c);async function d(e){try{var r;let t=await e.json();if("true"===process.env.ENABLE_MOCK_AUTH&&t.mock){let e=await o.z.user.findUnique({where:{telegramId:"mock-telegram-id"}});e||(e=await o.z.user.create({data:{telegramId:"mock-telegram-id",username:"mock_user",firstName:"Mock",lastName:"User",hasBasicPlan:!0}}));let r=(0,l.HU)({id:e.id,telegramId:e.telegramId,username:e.username||void 0});return u.NextResponse.json({success:!0,token:r,user:{id:e.id,telegramId:e.telegramId,username:e.username,firstName:e.firstName,lastName:e.lastName,totalBalance:e.totalBalance,miningPower:e.miningPower,hasBasicPlan:e.hasBasicPlan}})}if(!function(e){let r=process.env.TELEGRAM_BOT_TOKEN;if(!r)return console.warn("TELEGRAM_BOT_TOKEN not set, skipping validation"),!0;let{hash:t,...a}=e,s=Object.keys(a).sort().map(e=>`${e}=${a[e]}`).join("\n"),n=m().createHash("sha256").update(r).digest();return m().createHmac("sha256",n).update(s).digest("hex")===t}(t))return u.NextResponse.json({success:!1,error:"Invalid Telegram authentication"},{status:401});if(r=t.auth_date,Math.floor(Date.now()/1e3)-r>86400)return u.NextResponse.json({success:!1,error:"Authentication expired"},{status:401});let a=await o.z.user.findUnique({where:{telegramId:t.id.toString()}});a||(a=await o.z.user.create({data:{telegramId:t.id.toString(),username:t.username,firstName:t.first_name,lastName:t.last_name}}));let s=(0,l.HU)({id:a.id,telegramId:a.telegramId,username:a.username||void 0});return u.NextResponse.json({success:!0,token:s,user:{id:a.id,telegramId:a.telegramId,username:a.username,firstName:a.firstName,lastName:a.lastName,totalBalance:a.totalBalance,miningPower:a.miningPower,hasBasicPlan:a.hasBasicPlan}})}catch(e){return console.error("Auth error:",e),u.NextResponse.json({success:!1,error:"Internal server error"},{status:500})}}let p=new s.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/auth/telegram/route",pathname:"/api/auth/telegram",filename:"route",bundlePath:"app/api/auth/telegram/route"},resolvedPagePath:"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/app/api/auth/telegram/route.ts",nextConfigOutput:"",userland:a}),{workAsyncStorage:g,workUnitAsyncStorage:h,serverHooks:f}=p;function x(){return(0,i.patchFetch)({workAsyncStorage:g,workUnitAsyncStorage:h})}},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9428:e=>{"use strict";e.exports=require("buffer")}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),a=r.X(0,[447,580,205],()=>t(8691));module.exports=a})();