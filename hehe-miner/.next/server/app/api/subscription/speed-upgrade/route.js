(()=>{var e={};e.id=874,e.ids=[874],e.modules={846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},2909:(e,r,s)=>{"use strict";s.d(r,{HU:()=>a,rL:()=>u});var t=s(3205),i=s.n(t);let n=process.env.JWT_SECRET||"fallback-secret";function a(e){return i().sign(e,n,{expiresIn:"7d"})}function u(e){let r=e.headers.get("authorization");if(!r?.startsWith("Bearer "))return null;var s=r.substring(7);try{return i().verify(s,n)}catch{return null}}},3020:(e,r,s)=>{"use strict";s.r(r),s.d(r,{patchFetch:()=>x,routeModule:()=>c,serverHooks:()=>m,workAsyncStorage:()=>l,workUnitAsyncStorage:()=>g});var t={};s.r(t),s.d(t,{POST:()=>d});var i=s(6559),n=s(8088),a=s(7719),u=s(2190),o=s(4747),p=s(2909);async function d(e){try{let r=(0,p.rL)(e);if(!r)return u.NextResponse.json({success:!1,error:"Unauthorized"},{status:401});let s=await o.z.user.findUnique({where:{id:r.id},include:{miningSessions:{where:{isCompleted:!1},orderBy:{startTime:"desc"},take:1}}});if(!s)return u.NextResponse.json({success:!1,error:"User not found"},{status:404});if(!s.hasBasicPlan)return u.NextResponse.json({success:!1,error:"Basic plan required for speed upgrades"},{status:403});let t=s.miningSessions[0];if(t){let e=new Date(t.startTime.getTime()+144e5),r=new Date;if(r<e)return u.NextResponse.json({success:!1,error:"Cannot purchase upgrades during active mining session. Please wait for mining to complete or claim your tokens first.",timeUntilMiningComplete:e.getTime()-r.getTime()},{status:400})}let i=s.miningPower+.25,n=s.speedUpgrades+1,a=await o.z.user.update({where:{id:r.id},data:{miningPower:i,speedUpgrades:n}});return await o.z.subscription.create({data:{userId:r.id,upgradeType:"speed",amount:1,upgradeValue:.25}}),u.NextResponse.json({success:!0,message:"Speed upgrade purchased successfully",user:{id:a.id,miningPower:a.miningPower,speedUpgrades:a.speedUpgrades}})}catch(e){return console.error("Speed upgrade purchase error:",e),u.NextResponse.json({success:!1,error:"Internal server error"},{status:500})}}let c=new i.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/subscription/speed-upgrade/route",pathname:"/api/subscription/speed-upgrade",filename:"route",bundlePath:"app/api/subscription/speed-upgrade/route"},resolvedPagePath:"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/app/api/subscription/speed-upgrade/route.ts",nextConfigOutput:"",userland:t}),{workAsyncStorage:l,workUnitAsyncStorage:g,serverHooks:m}=c;function x(){return(0,a.patchFetch)({workAsyncStorage:l,workUnitAsyncStorage:g})}},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4747:(e,r,s)=>{"use strict";s.d(r,{z:()=>i});let t=require("@prisma/client"),i=globalThis.prisma??new t.PrismaClient},4870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},5511:e=>{"use strict";e.exports=require("crypto")},6487:()=>{},7910:e=>{"use strict";e.exports=require("stream")},8335:()=>{},8354:e=>{"use strict";e.exports=require("util")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9428:e=>{"use strict";e.exports=require("buffer")}};var r=require("../../../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),t=r.X(0,[447,580,205],()=>s(3020));module.exports=t})();