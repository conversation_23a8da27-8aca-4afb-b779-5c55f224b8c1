/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/subscription/speed-upgrade/route";
exports.ids = ["app/api/subscription/speed-upgrade/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fsubscription%2Fspeed-upgrade%2Froute&page=%2Fapi%2Fsubscription%2Fspeed-upgrade%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fsubscription%2Fspeed-upgrade%2Froute.ts&appDir=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FHehe-coin%2Fhehe-miner%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FHehe-coin%2Fhehe-miner&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fsubscription%2Fspeed-upgrade%2Froute&page=%2Fapi%2Fsubscription%2Fspeed-upgrade%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fsubscription%2Fspeed-upgrade%2Froute.ts&appDir=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FHehe-coin%2Fhehe-miner%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FHehe-coin%2Fhehe-miner&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _Users_tahafarooqui_Documents_augment_projects_Hehe_coin_hehe_miner_src_app_api_subscription_speed_upgrade_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/subscription/speed-upgrade/route.ts */ \"(rsc)/./src/app/api/subscription/speed-upgrade/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/subscription/speed-upgrade/route\",\n        pathname: \"/api/subscription/speed-upgrade\",\n        filename: \"route\",\n        bundlePath: \"app/api/subscription/speed-upgrade/route\"\n    },\n    resolvedPagePath: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/app/api/subscription/speed-upgrade/route.ts\",\n    nextConfigOutput,\n    userland: _Users_tahafarooqui_Documents_augment_projects_Hehe_coin_hehe_miner_src_app_api_subscription_speed_upgrade_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fsubscription%2Fspeed-upgrade%2Froute&page=%2Fapi%2Fsubscription%2Fspeed-upgrade%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fsubscription%2Fspeed-upgrade%2Froute.ts&appDir=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FHehe-coin%2Fhehe-miner%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FHehe-coin%2Fhehe-miner&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/subscription/speed-upgrade/route.ts":
/*!*********************************************************!*\
  !*** ./src/app/api/subscription/speed-upgrade/route.ts ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_prisma__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/prisma */ \"(rsc)/./src/lib/prisma.ts\");\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/auth */ \"(rsc)/./src/lib/auth.ts\");\n\n\n\nasync function POST(request) {\n    try {\n        const user = (0,_lib_auth__WEBPACK_IMPORTED_MODULE_2__.getUserFromRequest)(request);\n        if (!user) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'Unauthorized'\n            }, {\n                status: 401\n            });\n        }\n        const userProfile = await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.user.findUnique({\n            where: {\n                id: user.id\n            },\n            include: {\n                miningSessions: {\n                    where: {\n                        isCompleted: false\n                    },\n                    orderBy: {\n                        startTime: 'desc'\n                    },\n                    take: 1\n                }\n            }\n        });\n        if (!userProfile) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'User not found'\n            }, {\n                status: 404\n            });\n        }\n        if (!userProfile.hasBasicPlan) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'Basic plan required for speed upgrades'\n            }, {\n                status: 403\n            });\n        }\n        // Check if user has an active mining session\n        const activeMiningSession = userProfile.miningSessions[0];\n        if (activeMiningSession) {\n            const miningDuration = 4 * 60 * 60 * 1000 // 4 hours in milliseconds\n            ;\n            const endTime = new Date(activeMiningSession.startTime.getTime() + miningDuration);\n            const now = new Date();\n            if (now < endTime) {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    success: false,\n                    error: 'Cannot purchase upgrades during active mining session. Please wait for mining to complete or claim your tokens first.',\n                    timeUntilMiningComplete: endTime.getTime() - now.getTime()\n                }, {\n                    status: 400\n                });\n            }\n        }\n        // Each speed upgrade costs $1 and increases mining power by 0.25\n        const speedUpgradeValue = 0.25;\n        const newMiningPower = userProfile.miningPower + speedUpgradeValue;\n        const newSpeedUpgrades = userProfile.speedUpgrades + 1;\n        // In a real app, you would integrate with a payment processor here\n        // For now, we'll simulate the purchase\n        const updatedUser = await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.user.update({\n            where: {\n                id: user.id\n            },\n            data: {\n                miningPower: newMiningPower,\n                speedUpgrades: newSpeedUpgrades\n            }\n        });\n        // Create subscription record\n        await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.subscription.create({\n            data: {\n                userId: user.id,\n                upgradeType: 'speed',\n                amount: 1.0,\n                upgradeValue: speedUpgradeValue\n            }\n        });\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            message: 'Speed upgrade purchased successfully',\n            user: {\n                id: updatedUser.id,\n                miningPower: updatedUser.miningPower,\n                speedUpgrades: updatedUser.speedUpgrades\n            }\n        });\n    } catch (error) {\n        console.error('Speed upgrade purchase error:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'Internal server error'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/subscription/speed-upgrade/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/auth.ts":
/*!*************************!*\
  !*** ./src/lib/auth.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createMockUser: () => (/* binding */ createMockUser),\n/* harmony export */   generateToken: () => (/* binding */ generateToken),\n/* harmony export */   getUserFromRequest: () => (/* binding */ getUserFromRequest),\n/* harmony export */   verifyToken: () => (/* binding */ verifyToken)\n/* harmony export */ });\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! jsonwebtoken */ \"(rsc)/./node_modules/jsonwebtoken/index.js\");\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(jsonwebtoken__WEBPACK_IMPORTED_MODULE_0__);\n\nconst JWT_SECRET = process.env.JWT_SECRET || 'fallback-secret';\nfunction generateToken(user) {\n    return jsonwebtoken__WEBPACK_IMPORTED_MODULE_0___default().sign(user, JWT_SECRET, {\n        expiresIn: '7d'\n    });\n}\nfunction verifyToken(token) {\n    try {\n        return jsonwebtoken__WEBPACK_IMPORTED_MODULE_0___default().verify(token, JWT_SECRET);\n    } catch  {\n        return null;\n    }\n}\nfunction getUserFromRequest(request) {\n    const authHeader = request.headers.get('authorization');\n    if (!authHeader?.startsWith('Bearer ')) {\n        return null;\n    }\n    const token = authHeader.substring(7);\n    return verifyToken(token);\n}\n// Mock authentication for local development\nfunction createMockUser() {\n    return {\n        id: 'mock-user-id',\n        telegramId: 'mock-telegram-id',\n        username: 'mock_user'\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/auth.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/prisma.ts":
/*!***************************!*\
  !*** ./src/lib/prisma.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   prisma: () => (/* binding */ prisma)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\nconst globalForPrisma = globalThis;\nconst prisma = globalForPrisma.prisma ?? new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient();\nif (true) globalForPrisma.prisma = prisma;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL3ByaXNtYS50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBNkM7QUFFN0MsTUFBTUMsa0JBQWtCQztBQUlqQixNQUFNQyxTQUFTRixnQkFBZ0JFLE1BQU0sSUFBSSxJQUFJSCx3REFBWUEsR0FBRTtBQUVsRSxJQUFJSSxJQUFxQyxFQUFFSCxnQkFBZ0JFLE1BQU0sR0FBR0EiLCJzb3VyY2VzIjpbIi9Vc2Vycy90YWhhZmFyb29xdWkvRG9jdW1lbnRzL2F1Z21lbnQtcHJvamVjdHMvSGVoZS1jb2luL2hlaGUtbWluZXIvc3JjL2xpYi9wcmlzbWEudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgUHJpc21hQ2xpZW50IH0gZnJvbSAnQHByaXNtYS9jbGllbnQnXG5cbmNvbnN0IGdsb2JhbEZvclByaXNtYSA9IGdsb2JhbFRoaXMgYXMgdW5rbm93biBhcyB7XG4gIHByaXNtYTogUHJpc21hQ2xpZW50IHwgdW5kZWZpbmVkXG59XG5cbmV4cG9ydCBjb25zdCBwcmlzbWEgPSBnbG9iYWxGb3JQcmlzbWEucHJpc21hID8/IG5ldyBQcmlzbWFDbGllbnQoKVxuXG5pZiAocHJvY2Vzcy5lbnYuTk9ERV9FTlYgIT09ICdwcm9kdWN0aW9uJykgZ2xvYmFsRm9yUHJpc21hLnByaXNtYSA9IHByaXNtYVxuIl0sIm5hbWVzIjpbIlByaXNtYUNsaWVudCIsImdsb2JhbEZvclByaXNtYSIsImdsb2JhbFRoaXMiLCJwcmlzbWEiLCJwcm9jZXNzIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/prisma.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("@prisma/client");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/semver","vendor-chunks/jsonwebtoken","vendor-chunks/lodash.includes","vendor-chunks/jws","vendor-chunks/lodash.once","vendor-chunks/jwa","vendor-chunks/lodash.isinteger","vendor-chunks/ecdsa-sig-formatter","vendor-chunks/lodash.isplainobject","vendor-chunks/ms","vendor-chunks/lodash.isstring","vendor-chunks/lodash.isnumber","vendor-chunks/lodash.isboolean","vendor-chunks/safe-buffer","vendor-chunks/buffer-equal-constant-time"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fsubscription%2Fspeed-upgrade%2Froute&page=%2Fapi%2Fsubscription%2Fspeed-upgrade%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fsubscription%2Fspeed-upgrade%2Froute.ts&appDir=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FHehe-coin%2Fhehe-miner%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FHehe-coin%2Fhehe-miner&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();