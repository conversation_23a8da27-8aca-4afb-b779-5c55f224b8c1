(()=>{var e={};e.id=546,e.ids=[546],e.modules={846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1985:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>k,routeModule:()=>p,serverHooks:()=>x,workAsyncStorage:()=>l,workUnitAsyncStorage:()=>m});var s={};t.r(s),t.d(s,{POST:()=>d});var a=t(6559),n=t(8088),i=t(7719),u=t(2190),o=t(4747),c=t(2909);async function d(e){try{let r=(0,c.rL)(e);if(!r)return u.NextResponse.json({success:!1,error:"Unauthorized"},{status:401});let{taskId:t}=await e.json(),s=await o.z.task.findUnique({where:{id:t,isActive:!0}});if(!s)return u.NextResponse.json({success:!1,error:"Task not found"},{status:404});let a=await o.z.userTask.findUnique({where:{userId_taskId:{userId:r.id,taskId:t}}});if(a&&a.completedAt)return u.NextResponse.json({success:!1,error:"Task already completed"},{status:400});let[n,i]=await o.z.$transaction([o.z.userTask.upsert({where:{userId_taskId:{userId:r.id,taskId:t}},update:{completedAt:new Date,rewardClaimed:!0},create:{userId:r.id,taskId:t,completedAt:new Date,rewardClaimed:!0}}),o.z.user.update({where:{id:r.id},data:{totalBalance:{increment:s.reward}}})]);return u.NextResponse.json({success:!0,message:"Task completed successfully",reward:s.reward,newBalance:i.totalBalance})}catch(e){return console.error("Task completion error:",e),u.NextResponse.json({success:!1,error:"Internal server error"},{status:500})}}let p=new a.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/tasks/complete/route",pathname:"/api/tasks/complete",filename:"route",bundlePath:"app/api/tasks/complete/route"},resolvedPagePath:"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/app/api/tasks/complete/route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:l,workUnitAsyncStorage:m,serverHooks:x}=p;function k(){return(0,i.patchFetch)({workAsyncStorage:l,workUnitAsyncStorage:m})}},2909:(e,r,t)=>{"use strict";t.d(r,{HU:()=>i,rL:()=>u});var s=t(3205),a=t.n(s);let n=process.env.JWT_SECRET||"fallback-secret";function i(e){return a().sign(e,n,{expiresIn:"7d"})}function u(e){let r=e.headers.get("authorization");if(!r?.startsWith("Bearer "))return null;var t=r.substring(7);try{return a().verify(t,n)}catch{return null}}},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4747:(e,r,t)=>{"use strict";t.d(r,{z:()=>a});let s=require("@prisma/client"),a=globalThis.prisma??new s.PrismaClient},4870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},5511:e=>{"use strict";e.exports=require("crypto")},6487:()=>{},7910:e=>{"use strict";e.exports=require("stream")},8335:()=>{},8354:e=>{"use strict";e.exports=require("util")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9428:e=>{"use strict";e.exports=require("buffer")}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[447,580,205],()=>t(1985));module.exports=s})();