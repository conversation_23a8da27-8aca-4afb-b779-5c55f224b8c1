(()=>{var e={};e.id=456,e.ids=[456],e.modules={846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},2358:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>m,routeModule:()=>l,serverHooks:()=>k,workAsyncStorage:()=>x,workUnitAsyncStorage:()=>h});var s={};t.r(s),t.d(s,{GET:()=>p,POST:()=>d});var a=t(6559),n=t(8088),i=t(7719),o=t(2190),u=t(4747),c=t(2909);async function p(e){try{let r=(0,c.rL)(e);if(!r)return o.NextResponse.json({success:!1,error:"Unauthorized"},{status:401});let t=(await u.z.task.findMany({where:{isActive:!0},include:{userTasks:{where:{userId:r.id}}},orderBy:{createdAt:"desc"}})).map(e=>({id:e.id,title:e.title,description:e.description,reward:e.reward,link:e.link,attachment:e.attachment,isCompleted:e.userTasks.length>0&&null!==e.userTasks[0].completedAt,isRewardClaimed:e.userTasks.length>0&&e.userTasks[0].rewardClaimed}));return o.NextResponse.json({success:!0,tasks:t})}catch(e){return console.error("Tasks fetch error:",e),o.NextResponse.json({success:!1,error:"Internal server error"},{status:500})}}async function d(e){try{let{title:r,description:t,reward:s,link:a,attachment:n}=await e.json(),i=await u.z.task.create({data:{title:r,description:t,reward:s,link:a,attachment:n}});return o.NextResponse.json({success:!0,task:i})}catch(e){return console.error("Task creation error:",e),o.NextResponse.json({success:!1,error:"Internal server error"},{status:500})}}let l=new a.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/tasks/route",pathname:"/api/tasks",filename:"route",bundlePath:"app/api/tasks/route"},resolvedPagePath:"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/app/api/tasks/route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:x,workUnitAsyncStorage:h,serverHooks:k}=l;function m(){return(0,i.patchFetch)({workAsyncStorage:x,workUnitAsyncStorage:h})}},2909:(e,r,t)=>{"use strict";t.d(r,{HU:()=>i,rL:()=>o});var s=t(3205),a=t.n(s);let n=process.env.JWT_SECRET||"fallback-secret";function i(e){return a().sign(e,n,{expiresIn:"7d"})}function o(e){let r=e.headers.get("authorization");if(!r?.startsWith("Bearer "))return null;var t=r.substring(7);try{return a().verify(t,n)}catch{return null}}},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4747:(e,r,t)=>{"use strict";t.d(r,{z:()=>a});let s=require("@prisma/client"),a=globalThis.prisma??new s.PrismaClient},4870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},5511:e=>{"use strict";e.exports=require("crypto")},6487:()=>{},7910:e=>{"use strict";e.exports=require("stream")},8335:()=>{},8354:e=>{"use strict";e.exports=require("util")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9428:e=>{"use strict";e.exports=require("buffer")}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[447,580,205],()=>t(2358));module.exports=s})();