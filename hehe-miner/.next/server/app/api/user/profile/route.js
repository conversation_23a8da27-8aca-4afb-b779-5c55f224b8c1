(()=>{var e={};e.id=397,e.ids=[397],e.modules={846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},2909:(e,r,t)=>{"use strict";t.d(r,{HU:()=>a,rL:()=>o});var s=t(3205),i=t.n(s);let n=process.env.JWT_SECRET||"fallback-secret";function a(e){return i().sign(e,n,{expiresIn:"7d"})}function o(e){let r=e.headers.get("authorization");if(!r?.startsWith("Bearer "))return null;var t=r.substring(7);try{return i().verify(t,n)}catch{return null}}},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4549:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>g,routeModule:()=>l,serverHooks:()=>f,workAsyncStorage:()=>d,workUnitAsyncStorage:()=>m});var s={};t.r(s),t.d(s,{GET:()=>c});var i=t(6559),n=t(8088),a=t(7719),o=t(2190),u=t(4747),p=t(2909);async function c(e){try{let r=(0,p.rL)(e);if(!r)return o.NextResponse.json({success:!1,error:"Unauthorized"},{status:401});let t=await u.z.user.findUnique({where:{id:r.id},include:{miningSessions:{where:{isCompleted:!1},orderBy:{startTime:"desc"},take:1}}});if(!t)return o.NextResponse.json({success:!1,error:"User not found"},{status:404});let s=t.miningSessions[0],i=!0,n=0;if(s){let e=new Date(s.startTime.getTime()+144e5),r=new Date;r<e&&(i=!1,n=e.getTime()-r.getTime())}return o.NextResponse.json({success:!0,user:{id:t.id,telegramId:t.telegramId,username:t.username,firstName:t.firstName,lastName:t.lastName,totalBalance:t.totalBalance,miningPower:t.miningPower,hasBasicPlan:t.hasBasicPlan,speedUpgrades:t.speedUpgrades,canMine:i,timeUntilNextMining:n,currentMiningSession:s?{id:s.id,startTime:s.startTime,tokensEarned:s.tokensEarned}:null}})}catch(e){return console.error("Profile error:",e),o.NextResponse.json({success:!1,error:"Internal server error"},{status:500})}}let l=new i.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/user/profile/route",pathname:"/api/user/profile",filename:"route",bundlePath:"app/api/user/profile/route"},resolvedPagePath:"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/app/api/user/profile/route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:d,workUnitAsyncStorage:m,serverHooks:f}=l;function g(){return(0,a.patchFetch)({workAsyncStorage:d,workUnitAsyncStorage:m})}},4747:(e,r,t)=>{"use strict";t.d(r,{z:()=>i});let s=require("@prisma/client"),i=globalThis.prisma??new s.PrismaClient},4870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},5511:e=>{"use strict";e.exports=require("crypto")},6487:()=>{},7910:e=>{"use strict";e.exports=require("stream")},8335:()=>{},8354:e=>{"use strict";e.exports=require("util")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9428:e=>{"use strict";e.exports=require("buffer")}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[447,580,205],()=>t(4549));module.exports=s})();