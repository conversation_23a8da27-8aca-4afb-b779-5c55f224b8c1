(()=>{var e={};e.id=396,e.ids=[396],e.modules={846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},2909:(e,r,t)=>{"use strict";t.d(r,{HU:()=>i,rL:()=>u});var s=t(3205),a=t.n(s);let n=process.env.JWT_SECRET||"fallback-secret";function i(e){return a().sign(e,n,{expiresIn:"7d"})}function u(e){let r=e.headers.get("authorization");if(!r?.startsWith("Bearer "))return null;var t=r.substring(7);try{return a().verify(t,n)}catch{return null}}},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4747:(e,r,t)=>{"use strict";t.d(r,{z:()=>a});let s=require("@prisma/client"),a=globalThis.prisma??new s.PrismaClient},4870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},5511:e=>{"use strict";e.exports=require("crypto")},6487:()=>{},7130:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>h,routeModule:()=>p,serverHooks:()=>x,workAsyncStorage:()=>f,workUnitAsyncStorage:()=>m});var s={};t.r(s),t.d(s,{GET:()=>d,POST:()=>l});var a=t(6559),n=t(8088),i=t(7719),u=t(2190),o=t(4747),c=t(2909);async function d(e){try{let r=(0,c.rL)(e);if(!r)return u.NextResponse.json({success:!1,error:"Unauthorized"},{status:401});let t=await o.z.referral.findMany({where:{referrerId:r.id},include:{referred:{select:{username:!0,firstName:!0,lastName:!0,createdAt:!0}}},orderBy:{createdAt:"desc"}}),s=t.length,a=t.reduce((e,r)=>e+r.reward,0);return u.NextResponse.json({success:!0,referrals:t.map(e=>({id:e.id,reward:e.reward,createdAt:e.createdAt,referred:{username:e.referred.username,firstName:e.referred.firstName,lastName:e.referred.lastName,joinedAt:e.referred.createdAt}})),stats:{totalReferrals:s,totalRewards:a},referralLink:`https://hehe-miner.vercel.app?ref=${r.telegramId}`})}catch(e){return console.error("Referrals fetch error:",e),u.NextResponse.json({success:!1,error:"Internal server error"},{status:500})}}async function l(e){try{let r=(0,c.rL)(e);if(!r)return u.NextResponse.json({success:!1,error:"Unauthorized"},{status:401});let{referredTelegramId:t}=await e.json(),s=await o.z.user.findUnique({where:{telegramId:t}});if(!s)return u.NextResponse.json({success:!1,error:"Referred user not found"},{status:404});if(s.id===r.id)return u.NextResponse.json({success:!1,error:"Cannot refer yourself"},{status:400});if(await o.z.referral.findUnique({where:{referrerId_referredId:{referrerId:r.id,referredId:s.id}}}))return u.NextResponse.json({success:!1,error:"User already referred"},{status:400});let[a,n]=await o.z.$transaction([o.z.referral.create({data:{referrerId:r.id,referredId:s.id,reward:.5}}),o.z.user.update({where:{id:r.id},data:{totalBalance:{increment:.5}}})]);return u.NextResponse.json({success:!0,message:"Referral successful",reward:.5,newBalance:n.totalBalance})}catch(e){return console.error("Referral creation error:",e),u.NextResponse.json({success:!1,error:"Internal server error"},{status:500})}}let p=new a.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/referrals/route",pathname:"/api/referrals",filename:"route",bundlePath:"app/api/referrals/route"},resolvedPagePath:"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/app/api/referrals/route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:f,workUnitAsyncStorage:m,serverHooks:x}=p;function h(){return(0,i.patchFetch)({workAsyncStorage:f,workUnitAsyncStorage:m})}},7910:e=>{"use strict";e.exports=require("stream")},8335:()=>{},8354:e=>{"use strict";e.exports=require("util")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9428:e=>{"use strict";e.exports=require("buffer")}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[447,580,205],()=>t(7130));module.exports=s})();