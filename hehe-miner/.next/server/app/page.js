/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FHehe-coin%2Fhehe-miner%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FHehe-coin%2Fhehe-miner&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FHehe-coin%2Fhehe-miner%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FHehe-coin%2Fhehe-miner&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(rsc)/./src/app/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/app/page.tsx\"],\n          metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n        }]\n      },\n        {\n        'layout': [module0, \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/app/layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/app/page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FHehe-coin%2Fhehe-miner%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FHehe-coin%2Fhehe-miner&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FHehe-coin%2Fhehe-miner%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FHehe-coin%2Fhehe-miner%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FHehe-coin%2Fhehe-miner%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FHehe-coin%2Fhehe-miner%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FHehe-coin%2Fhehe-miner%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FHehe-coin%2Fhehe-miner%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FHehe-coin%2Fhehe-miner%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FHehe-coin%2Fhehe-miner%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FHehe-coin%2Fhehe-miner%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FHehe-coin%2Fhehe-miner%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FHehe-coin%2Fhehe-miner%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FHehe-coin%2Fhehe-miner%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FHehe-coin%2Fhehe-miner%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FHehe-coin%2Fhehe-miner%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FHehe-coin%2Fhehe-miner%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FHehe-coin%2Fhehe-miner%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGdGFoYWZhcm9vcXVpJTJGRG9jdW1lbnRzJTJGYXVnbWVudC1wcm9qZWN0cyUyRkhlaGUtY29pbiUyRmhlaGUtbWluZXIlMkZub2RlX21vZHVsZXMlMkZuZXh0JTJGZGlzdCUyRmNsaWVudCUyRmNvbXBvbmVudHMlMkZjbGllbnQtcGFnZS5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjIlMkZVc2VycyUyRnRhaGFmYXJvb3F1aSUyRkRvY3VtZW50cyUyRmF1Z21lbnQtcHJvamVjdHMlMkZIZWhlLWNvaW4lMkZoZWhlLW1pbmVyJTJGbm9kZV9tb2R1bGVzJTJGbmV4dCUyRmRpc3QlMkZjbGllbnQlMkZjb21wb25lbnRzJTJGY2xpZW50LXNlZ21lbnQuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyJTJGVXNlcnMlMkZ0YWhhZmFyb29xdWklMkZEb2N1bWVudHMlMkZhdWdtZW50LXByb2plY3RzJTJGSGVoZS1jb2luJTJGaGVoZS1taW5lciUyRm5vZGVfbW9kdWxlcyUyRm5leHQlMkZkaXN0JTJGY2xpZW50JTJGY29tcG9uZW50cyUyRmVycm9yLWJvdW5kYXJ5LmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGdGFoYWZhcm9vcXVpJTJGRG9jdW1lbnRzJTJGYXVnbWVudC1wcm9qZWN0cyUyRkhlaGUtY29pbiUyRmhlaGUtbWluZXIlMkZub2RlX21vZHVsZXMlMkZuZXh0JTJGZGlzdCUyRmNsaWVudCUyRmNvbXBvbmVudHMlMkZodHRwLWFjY2Vzcy1mYWxsYmFjayUyRmVycm9yLWJvdW5kYXJ5LmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGdGFoYWZhcm9vcXVpJTJGRG9jdW1lbnRzJTJGYXVnbWVudC1wcm9qZWN0cyUyRkhlaGUtY29pbiUyRmhlaGUtbWluZXIlMkZub2RlX21vZHVsZXMlMkZuZXh0JTJGZGlzdCUyRmNsaWVudCUyRmNvbXBvbmVudHMlMkZsYXlvdXQtcm91dGVyLmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGdGFoYWZhcm9vcXVpJTJGRG9jdW1lbnRzJTJGYXVnbWVudC1wcm9qZWN0cyUyRkhlaGUtY29pbiUyRmhlaGUtbWluZXIlMkZub2RlX21vZHVsZXMlMkZuZXh0JTJGZGlzdCUyRmNsaWVudCUyRmNvbXBvbmVudHMlMkZtZXRhZGF0YSUyRmFzeW5jLW1ldGFkYXRhLmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGdGFoYWZhcm9vcXVpJTJGRG9jdW1lbnRzJTJGYXVnbWVudC1wcm9qZWN0cyUyRkhlaGUtY29pbiUyRmhlaGUtbWluZXIlMkZub2RlX21vZHVsZXMlMkZuZXh0JTJGZGlzdCUyRmNsaWVudCUyRmNvbXBvbmVudHMlMkZtZXRhZGF0YSUyRm1ldGFkYXRhLWJvdW5kYXJ5LmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGdGFoYWZhcm9vcXVpJTJGRG9jdW1lbnRzJTJGYXVnbWVudC1wcm9qZWN0cyUyRkhlaGUtY29pbiUyRmhlaGUtbWluZXIlMkZub2RlX21vZHVsZXMlMkZuZXh0JTJGZGlzdCUyRmNsaWVudCUyRmNvbXBvbmVudHMlMkZyZW5kZXItZnJvbS10ZW1wbGF0ZS1jb250ZXh0LmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxvT0FBZ0s7QUFDaEs7QUFDQSwwT0FBbUs7QUFDbks7QUFDQSwwT0FBbUs7QUFDbks7QUFDQSxvUkFBd0w7QUFDeEw7QUFDQSx3T0FBa0s7QUFDbEs7QUFDQSw0UEFBNEs7QUFDNUs7QUFDQSxrUUFBK0s7QUFDL0s7QUFDQSxzUUFBaUwiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIi9Vc2Vycy90YWhhZmFyb29xdWkvRG9jdW1lbnRzL2F1Z21lbnQtcHJvamVjdHMvSGVoZS1jb2luL2hlaGUtbWluZXIvbm9kZV9tb2R1bGVzL25leHQvZGlzdC9jbGllbnQvY29tcG9uZW50cy9jbGllbnQtcGFnZS5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL1VzZXJzL3RhaGFmYXJvb3F1aS9Eb2N1bWVudHMvYXVnbWVudC1wcm9qZWN0cy9IZWhlLWNvaW4vaGVoZS1taW5lci9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL2NsaWVudC1zZWdtZW50LmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvVXNlcnMvdGFoYWZhcm9vcXVpL0RvY3VtZW50cy9hdWdtZW50LXByb2plY3RzL0hlaGUtY29pbi9oZWhlLW1pbmVyL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvZXJyb3ItYm91bmRhcnkuanNcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIi9Vc2Vycy90YWhhZmFyb29xdWkvRG9jdW1lbnRzL2F1Z21lbnQtcHJvamVjdHMvSGVoZS1jb2luL2hlaGUtbWluZXIvbm9kZV9tb2R1bGVzL25leHQvZGlzdC9jbGllbnQvY29tcG9uZW50cy9odHRwLWFjY2Vzcy1mYWxsYmFjay9lcnJvci1ib3VuZGFyeS5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL1VzZXJzL3RhaGFmYXJvb3F1aS9Eb2N1bWVudHMvYXVnbWVudC1wcm9qZWN0cy9IZWhlLWNvaW4vaGVoZS1taW5lci9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL2xheW91dC1yb3V0ZXIuanNcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIi9Vc2Vycy90YWhhZmFyb29xdWkvRG9jdW1lbnRzL2F1Z21lbnQtcHJvamVjdHMvSGVoZS1jb2luL2hlaGUtbWluZXIvbm9kZV9tb2R1bGVzL25leHQvZGlzdC9jbGllbnQvY29tcG9uZW50cy9tZXRhZGF0YS9hc3luYy1tZXRhZGF0YS5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL1VzZXJzL3RhaGFmYXJvb3F1aS9Eb2N1bWVudHMvYXVnbWVudC1wcm9qZWN0cy9IZWhlLWNvaW4vaGVoZS1taW5lci9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL21ldGFkYXRhL21ldGFkYXRhLWJvdW5kYXJ5LmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvVXNlcnMvdGFoYWZhcm9vcXVpL0RvY3VtZW50cy9hdWdtZW50LXByb2plY3RzL0hlaGUtY29pbi9oZWhlLW1pbmVyL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvcmVuZGVyLWZyb20tdGVtcGxhdGUtY29udGV4dC5qc1wiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FHehe-coin%2Fhehe-miner%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FHehe-coin%2Fhehe-miner%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FHehe-coin%2Fhehe-miner%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FHehe-coin%2Fhehe-miner%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FHehe-coin%2Fhehe-miner%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FHehe-coin%2Fhehe-miner%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FHehe-coin%2Fhehe-miner%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FHehe-coin%2Fhehe-miner%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FHehe-coin%2Fhehe-miner%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FHehe-coin%2Fhehe-miner%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FHehe-coin%2Fhehe-miner%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FHehe-coin%2Fhehe-miner%2Fsrc%2Fcontexts%2FAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FHehe-coin%2Fhehe-miner%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FHehe-coin%2Fhehe-miner%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FHehe-coin%2Fhehe-miner%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FHehe-coin%2Fhehe-miner%2Fsrc%2Fcontexts%2FAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/contexts/AuthContext.tsx */ \"(rsc)/./src/contexts/AuthContext.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FHehe-coin%2Fhehe-miner%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FHehe-coin%2Fhehe-miner%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FHehe-coin%2Fhehe-miner%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FHehe-coin%2Fhehe-miner%2Fsrc%2Fcontexts%2FAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FHehe-coin%2Fhehe-miner%2Fsrc%2Fapp%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FHehe-coin%2Fhehe-miner%2Fsrc%2Fapp%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(rsc)/./src/app/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGdGFoYWZhcm9vcXVpJTJGRG9jdW1lbnRzJTJGYXVnbWVudC1wcm9qZWN0cyUyRkhlaGUtY29pbiUyRmhlaGUtbWluZXIlMkZzcmMlMkZhcHAlMkZwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsZ0pBQXlIIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvVXNlcnMvdGFoYWZhcm9vcXVpL0RvY3VtZW50cy9hdWdtZW50LXByb2plY3RzL0hlaGUtY29pbi9oZWhlLW1pbmVyL3NyYy9hcHAvcGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FHehe-coin%2Fhehe-miner%2Fsrc%2Fapp%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__":
/*!**************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ ***!
  \**************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (async (props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", await props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlci5qcz90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhLi9zcmMvYXBwL2Zhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBLEVBQWlGOztBQUVqRixFQUFFLGlFQUFlO0FBQ2pCLHVCQUF1QjtBQUN2QixxQkFBcUIsOEZBQW1COztBQUV4QztBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wiLCJzb3VyY2VzIjpbIi9Vc2Vycy90YWhhZmFyb29xdWkvRG9jdW1lbnRzL2F1Z21lbnQtcHJvamVjdHMvSGVoZS1jb2luL2hlaGUtbWluZXIvc3JjL2FwcC9mYXZpY29uLmljbz9fX25leHRfbWV0YWRhdGFfXyJdLCJzb3VyY2VzQ29udGVudCI6WyIgIGltcG9ydCB7IGZpbGxNZXRhZGF0YVNlZ21lbnQgfSBmcm9tICduZXh0L2Rpc3QvbGliL21ldGFkYXRhL2dldC1tZXRhZGF0YS1yb3V0ZSdcblxuICBleHBvcnQgZGVmYXVsdCBhc3luYyAocHJvcHMpID0+IHtcbiAgICBjb25zdCBpbWFnZURhdGEgPSB7XCJ0eXBlXCI6XCJpbWFnZS94LWljb25cIixcInNpemVzXCI6XCIxNngxNlwifVxuICAgIGNvbnN0IGltYWdlVXJsID0gZmlsbE1ldGFkYXRhU2VnbWVudChcIi5cIiwgYXdhaXQgcHJvcHMucGFyYW1zLCBcImZhdmljb24uaWNvXCIpXG5cbiAgICByZXR1cm4gW3tcbiAgICAgIC4uLmltYWdlRGF0YSxcbiAgICAgIHVybDogaW1hZ2VVcmwgKyBcIlwiLFxuICAgIH1dXG4gIH0iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"719cb0fc3f63\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyIvVXNlcnMvdGFoYWZhcm9vcXVpL0RvY3VtZW50cy9hdWdtZW50LXByb2plY3RzL0hlaGUtY29pbi9oZWhlLW1pbmVyL3NyYy9hcHAvZ2xvYmFscy5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCI3MTljYjBmYzNmNjNcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src/app/layout.tsx\",\"import\":\"Geist\",\"arguments\":[{\"variable\":\"--font-geist-sans\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistSans\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src/app/layout.tsx\\\",\\\"import\\\":\\\"Geist\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-geist-sans\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"geistSans\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src/app/layout.tsx\",\"import\":\"Geist_Mono\",\"arguments\":[{\"variable\":\"--font-geist-mono\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistMono\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src/app/layout.tsx\\\",\\\"import\\\":\\\"Geist_Mono\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-geist-mono\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"geistMono\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(rsc)/./src/contexts/AuthContext.tsx\");\n\n\n\n\n\nconst metadata = {\n    title: \"Hehe Miner - Telegram Mining Game\",\n    description: \"Mine Hehe tokens in this fun Telegram-based mining game\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_3___default().variable)} ${(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_4___default().variable)} antialiased bg-gray-900 text-white min-h-screen`,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.AuthProvider, {\n                children: children\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/app/layout.tsx\",\n                lineNumber: 31,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/app/layout.tsx\",\n            lineNumber: 28,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/app/layout.tsx\",\n        lineNumber: 27,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/app/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/app/page.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/contexts/AuthContext.tsx":
/*!**************************************!*\
  !*** ./src/contexts/AuthContext.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),
/* harmony export */   useAuth: () => (/* binding */ useAuth)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const AuthProvider = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/contexts/AuthContext.tsx",
"AuthProvider",
);const useAuth = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call useAuth() from the server but useAuth is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/contexts/AuthContext.tsx",
"useAuth",
);

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FHehe-coin%2Fhehe-miner%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FHehe-coin%2Fhehe-miner%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FHehe-coin%2Fhehe-miner%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FHehe-coin%2Fhehe-miner%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FHehe-coin%2Fhehe-miner%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FHehe-coin%2Fhehe-miner%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FHehe-coin%2Fhehe-miner%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FHehe-coin%2Fhehe-miner%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FHehe-coin%2Fhehe-miner%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FHehe-coin%2Fhehe-miner%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FHehe-coin%2Fhehe-miner%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FHehe-coin%2Fhehe-miner%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FHehe-coin%2Fhehe-miner%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FHehe-coin%2Fhehe-miner%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FHehe-coin%2Fhehe-miner%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FHehe-coin%2Fhehe-miner%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FHehe-coin%2Fhehe-miner%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FHehe-coin%2Fhehe-miner%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FHehe-coin%2Fhehe-miner%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FHehe-coin%2Fhehe-miner%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FHehe-coin%2Fhehe-miner%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FHehe-coin%2Fhehe-miner%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FHehe-coin%2Fhehe-miner%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FHehe-coin%2Fhehe-miner%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FHehe-coin%2Fhehe-miner%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FHehe-coin%2Fhehe-miner%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FHehe-coin%2Fhehe-miner%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FHehe-coin%2Fhehe-miner%2Fsrc%2Fcontexts%2FAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FHehe-coin%2Fhehe-miner%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FHehe-coin%2Fhehe-miner%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FHehe-coin%2Fhehe-miner%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FHehe-coin%2Fhehe-miner%2Fsrc%2Fcontexts%2FAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/contexts/AuthContext.tsx */ \"(ssr)/./src/contexts/AuthContext.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FHehe-coin%2Fhehe-miner%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FHehe-coin%2Fhehe-miner%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FHehe-coin%2Fhehe-miner%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FHehe-coin%2Fhehe-miner%2Fsrc%2Fcontexts%2FAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FHehe-coin%2Fhehe-miner%2Fsrc%2Fapp%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FHehe-coin%2Fhehe-miner%2Fsrc%2Fapp%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(ssr)/./src/app/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGdGFoYWZhcm9vcXVpJTJGRG9jdW1lbnRzJTJGYXVnbWVudC1wcm9qZWN0cyUyRkhlaGUtY29pbiUyRmhlaGUtbWluZXIlMkZzcmMlMkZhcHAlMkZwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsZ0pBQXlIIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvVXNlcnMvdGFoYWZhcm9vcXVpL0RvY3VtZW50cy9hdWdtZW50LXByb2plY3RzL0hlaGUtY29pbi9oZWhlLW1pbmVyL3NyYy9hcHAvcGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FHehe-coin%2Fhehe-miner%2Fsrc%2Fapp%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(ssr)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/api */ \"(ssr)/./src/lib/api.ts\");\n/* harmony import */ var _components_AuthScreen__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/AuthScreen */ \"(ssr)/./src/components/AuthScreen.tsx\");\n/* harmony import */ var _components_MainApp__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/MainApp */ \"(ssr)/./src/components/MainApp.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\nfunction Home() {\n    const { user, token, isLoading } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_1__.useAuth)();\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"Home.useEffect\": ()=>{\n            if (token) {\n                _lib_api__WEBPACK_IMPORTED_MODULE_3__.apiClient.setToken(token);\n            }\n        }\n    }[\"Home.useEffect\"], [\n        token\n    ]);\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-yellow-400 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/app/page.tsx\",\n                        lineNumber: 22,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-400\",\n                        children: \"Loading Hehe Miner...\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/app/page.tsx\",\n                        lineNumber: 23,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/app/page.tsx\",\n                lineNumber: 21,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/app/page.tsx\",\n            lineNumber: 20,\n            columnNumber: 7\n        }, this);\n    }\n    if (!user) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AuthScreen__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/app/page.tsx\",\n            lineNumber: 30,\n            columnNumber: 12\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_MainApp__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/app/page.tsx\",\n        lineNumber: 33,\n        columnNumber: 10\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/AirdropScreen.tsx":
/*!******************************************!*\
  !*** ./src/components/AirdropScreen.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AirdropScreen)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(ssr)/./src/contexts/AuthContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction AirdropScreen() {\n    const { user } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_1__.useAuth)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"p-6 max-w-md mx-auto\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-2xl font-bold text-white mb-2\",\n                        children: \"Airdrop\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/AirdropScreen.tsx\",\n                        lineNumber: 11,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-400\",\n                        children: \"Your HEHE token balance and airdrop status\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/AirdropScreen.tsx\",\n                        lineNumber: 12,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/AirdropScreen.tsx\",\n                lineNumber: 10,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gradient-to-r from-purple-600 to-blue-600 rounded-2xl p-6 mb-6 text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-16 h-16 bg-white/20 rounded-full mx-auto flex items-center justify-center mb-3\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-2xl\",\n                                    children: \"\\uD83E\\uDE82\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/AirdropScreen.tsx\",\n                                    lineNumber: 19,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/AirdropScreen.tsx\",\n                                lineNumber: 18,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-white text-lg font-semibold mb-1\",\n                                children: \"Total Balance\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/AirdropScreen.tsx\",\n                                lineNumber: 21,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-white/80 text-sm\",\n                                children: \"Your accumulated HEHE tokens\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/AirdropScreen.tsx\",\n                                lineNumber: 22,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/AirdropScreen.tsx\",\n                        lineNumber: 17,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-4xl font-bold text-white mb-2\",\n                        children: user?.totalBalance?.toFixed(2) || '0.00'\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/AirdropScreen.tsx\",\n                        lineNumber: 25,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-white/80 text-lg\",\n                        children: \"HEHE Tokens\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/AirdropScreen.tsx\",\n                        lineNumber: 28,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/AirdropScreen.tsx\",\n                lineNumber: 16,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gray-800 rounded-lg border border-gray-700 p-6 mb-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-12 h-12 bg-yellow-500/20 rounded-full mx-auto flex items-center justify-center mb-3\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-xl\",\n                                children: \"⏰\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/AirdropScreen.tsx\",\n                                lineNumber: 35,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/AirdropScreen.tsx\",\n                            lineNumber: 34,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-white text-lg font-semibold mb-2\",\n                            children: \"Airdrop Status\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/AirdropScreen.tsx\",\n                            lineNumber: 37,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-yellow-500/10 border border-yellow-500/20 rounded-lg p-4 mb-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-yellow-400 font-semibold text-lg\",\n                                children: \"Coming Soon\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/AirdropScreen.tsx\",\n                                lineNumber: 39,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/AirdropScreen.tsx\",\n                            lineNumber: 38,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-400 text-sm\",\n                            children: \"The HEHE token airdrop is currently in development. Keep mining and completing tasks to maximize your token balance!\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/AirdropScreen.tsx\",\n                            lineNumber: 41,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/AirdropScreen.tsx\",\n                    lineNumber: 33,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/AirdropScreen.tsx\",\n                lineNumber: 32,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gray-800 rounded-lg border border-gray-700 p-4 mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-white font-semibold mb-4\",\n                        children: \"Token Breakdown\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/AirdropScreen.tsx\",\n                        lineNumber: 50,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-lg\",\n                                                children: \"⛏️\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/AirdropScreen.tsx\",\n                                                lineNumber: 54,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gray-300\",\n                                                children: \"Mining Rewards\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/AirdropScreen.tsx\",\n                                                lineNumber: 55,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/AirdropScreen.tsx\",\n                                        lineNumber: 53,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-white font-medium\",\n                                        children: (user?.totalBalance || 0) > 0 ? 'Active' : 'Start Mining'\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/AirdropScreen.tsx\",\n                                        lineNumber: 57,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/AirdropScreen.tsx\",\n                                lineNumber: 52,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-lg\",\n                                                children: \"\\uD83D\\uDCCB\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/AirdropScreen.tsx\",\n                                                lineNumber: 65,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gray-300\",\n                                                children: \"Task Rewards\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/AirdropScreen.tsx\",\n                                                lineNumber: 66,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/AirdropScreen.tsx\",\n                                        lineNumber: 64,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-white font-medium\",\n                                        children: \"Available\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/AirdropScreen.tsx\",\n                                        lineNumber: 68,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/AirdropScreen.tsx\",\n                                lineNumber: 63,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-lg\",\n                                                children: \"\\uD83D\\uDC65\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/AirdropScreen.tsx\",\n                                                lineNumber: 73,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gray-300\",\n                                                children: \"Referral Rewards\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/AirdropScreen.tsx\",\n                                                lineNumber: 74,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/AirdropScreen.tsx\",\n                                        lineNumber: 72,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-white font-medium\",\n                                        children: \"0.5 HEHE each\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/AirdropScreen.tsx\",\n                                        lineNumber: 76,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/AirdropScreen.tsx\",\n                                lineNumber: 71,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/AirdropScreen.tsx\",\n                        lineNumber: 51,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/AirdropScreen.tsx\",\n                lineNumber: 49,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-blue-500/10 border border-blue-500/20 rounded-lg p-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                        className: \"text-blue-400 font-semibold mb-3\",\n                        children: \"Airdrop Information\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/AirdropScreen.tsx\",\n                        lineNumber: 83,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-2 text-blue-300 text-sm\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-start space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-blue-400 mt-0.5\",\n                                        children: \"•\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/AirdropScreen.tsx\",\n                                        lineNumber: 86,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"All HEHE tokens earned through mining, tasks, and referrals will be eligible for the airdrop\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/AirdropScreen.tsx\",\n                                        lineNumber: 87,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/AirdropScreen.tsx\",\n                                lineNumber: 85,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-start space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-blue-400 mt-0.5\",\n                                        children: \"•\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/AirdropScreen.tsx\",\n                                        lineNumber: 90,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"The airdrop will convert your HEHE tokens to the official HEHE cryptocurrency\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/AirdropScreen.tsx\",\n                                        lineNumber: 91,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/AirdropScreen.tsx\",\n                                lineNumber: 89,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-start space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-blue-400 mt-0.5\",\n                                        children: \"•\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/AirdropScreen.tsx\",\n                                        lineNumber: 94,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Keep accumulating tokens to maximize your airdrop allocation\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/AirdropScreen.tsx\",\n                                        lineNumber: 95,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/AirdropScreen.tsx\",\n                                lineNumber: 93,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-start space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-blue-400 mt-0.5\",\n                                        children: \"•\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/AirdropScreen.tsx\",\n                                        lineNumber: 98,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Follow our social media channels for airdrop announcements\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/AirdropScreen.tsx\",\n                                        lineNumber: 99,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/AirdropScreen.tsx\",\n                                lineNumber: 97,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/AirdropScreen.tsx\",\n                        lineNumber: 84,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/AirdropScreen.tsx\",\n                lineNumber: 82,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-6 text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-400 text-sm mb-3\",\n                        children: \"Want to increase your airdrop allocation?\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/AirdropScreen.tsx\",\n                        lineNumber: 106,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-2 gap-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gray-800 rounded-lg p-3 border border-gray-700\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-white font-medium text-sm\",\n                                        children: \"Keep Mining\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/AirdropScreen.tsx\",\n                                        lineNumber: 111,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-400 text-xs\",\n                                        children: \"Earn 4+ HEHE every 4 hours\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/AirdropScreen.tsx\",\n                                        lineNumber: 112,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/AirdropScreen.tsx\",\n                                lineNumber: 110,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gray-800 rounded-lg p-3 border border-gray-700\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-white font-medium text-sm\",\n                                        children: \"Complete Tasks\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/AirdropScreen.tsx\",\n                                        lineNumber: 115,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-400 text-xs\",\n                                        children: \"Bonus tokens available\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/AirdropScreen.tsx\",\n                                        lineNumber: 116,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/AirdropScreen.tsx\",\n                                lineNumber: 114,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/AirdropScreen.tsx\",\n                        lineNumber: 109,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/AirdropScreen.tsx\",\n                lineNumber: 105,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/AirdropScreen.tsx\",\n        lineNumber: 9,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/AirdropScreen.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/AuthScreen.tsx":
/*!***************************************!*\
  !*** ./src/components/AuthScreen.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AuthScreen)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(ssr)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/api */ \"(ssr)/./src/lib/api.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction AuthScreen() {\n    const { login } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const handleMockLogin = async ()=>{\n        setIsLoading(true);\n        setError('');\n        try {\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_3__.apiClient.loginMock();\n            if (response.success) {\n                login(response.user, response.token);\n            } else {\n                setError(response.error || 'Login failed');\n            }\n        } catch (error) {\n            setError('Network error occurred');\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const handleTelegramLogin = ()=>{\n        // In a real Telegram Web App, this would use the Telegram Web App SDK\n        // For now, we'll show instructions\n        alert('This would integrate with Telegram Web App SDK in production. Use Mock Login for testing.');\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen flex items-center justify-center bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-md w-full mx-4\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gray-800 rounded-2xl shadow-2xl p-8 border border-gray-700\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-20 h-20 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-full mx-auto mb-4 flex items-center justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-2xl font-bold text-gray-900\",\n                                    children: \"⛏️\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/AuthScreen.tsx\",\n                                    lineNumber: 44,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/AuthScreen.tsx\",\n                                lineNumber: 43,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-3xl font-bold text-white mb-2\",\n                                children: \"Hehe Miner\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/AuthScreen.tsx\",\n                                lineNumber: 46,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-400\",\n                                children: \"Start mining Hehe tokens today!\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/AuthScreen.tsx\",\n                                lineNumber: 47,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/AuthScreen.tsx\",\n                        lineNumber: 42,\n                        columnNumber: 11\n                    }, this),\n                    error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-red-500/10 border border-red-500/20 rounded-lg p-3 mb-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-red-400 text-sm\",\n                            children: error\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/AuthScreen.tsx\",\n                            lineNumber: 53,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/AuthScreen.tsx\",\n                        lineNumber: 52,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleTelegramLogin,\n                                className: \"w-full bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3 px-4 rounded-lg transition-colors duration-200 flex items-center justify-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"\\uD83D\\uDCF1\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/AuthScreen.tsx\",\n                                        lineNumber: 63,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Login with Telegram\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/AuthScreen.tsx\",\n                                        lineNumber: 64,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/AuthScreen.tsx\",\n                                lineNumber: 59,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-0 flex items-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-full border-t border-gray-600\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/AuthScreen.tsx\",\n                                            lineNumber: 69,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/AuthScreen.tsx\",\n                                        lineNumber: 68,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative flex justify-center text-sm\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"px-2 bg-gray-800 text-gray-400\",\n                                            children: \"or for testing\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/AuthScreen.tsx\",\n                                            lineNumber: 72,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/AuthScreen.tsx\",\n                                        lineNumber: 71,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/AuthScreen.tsx\",\n                                lineNumber: 67,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleMockLogin,\n                                disabled: isLoading,\n                                className: \"w-full bg-yellow-600 hover:bg-yellow-700 disabled:bg-yellow-800 disabled:cursor-not-allowed text-white font-semibold py-3 px-4 rounded-lg transition-colors duration-200 flex items-center justify-center space-x-2\",\n                                children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-white\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/AuthScreen.tsx\",\n                                            lineNumber: 83,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Logging in...\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/AuthScreen.tsx\",\n                                            lineNumber: 84,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"\\uD83D\\uDD27\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/AuthScreen.tsx\",\n                                            lineNumber: 88,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Mock Login (Development)\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/AuthScreen.tsx\",\n                                            lineNumber: 89,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/AuthScreen.tsx\",\n                                lineNumber: 76,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/AuthScreen.tsx\",\n                        lineNumber: 58,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-8 pt-6 border-t border-gray-700\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold text-white mb-4\",\n                                children: \"Features:\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/AuthScreen.tsx\",\n                                lineNumber: 97,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                className: \"space-y-2 text-gray-300 text-sm\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-green-400\",\n                                                children: \"✓\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/AuthScreen.tsx\",\n                                                lineNumber: 100,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Mine 4 Hehe tokens every 4 hours\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/AuthScreen.tsx\",\n                                                lineNumber: 101,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/AuthScreen.tsx\",\n                                        lineNumber: 99,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-green-400\",\n                                                children: \"✓\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/AuthScreen.tsx\",\n                                                lineNumber: 104,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Complete tasks for bonus tokens\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/AuthScreen.tsx\",\n                                                lineNumber: 105,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/AuthScreen.tsx\",\n                                        lineNumber: 103,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-green-400\",\n                                                children: \"✓\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/AuthScreen.tsx\",\n                                                lineNumber: 108,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Refer friends and earn rewards\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/AuthScreen.tsx\",\n                                                lineNumber: 109,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/AuthScreen.tsx\",\n                                        lineNumber: 107,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-green-400\",\n                                                children: \"✓\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/AuthScreen.tsx\",\n                                                lineNumber: 112,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Upgrade mining speed\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/AuthScreen.tsx\",\n                                                lineNumber: 113,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/AuthScreen.tsx\",\n                                        lineNumber: 111,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/AuthScreen.tsx\",\n                                lineNumber: 98,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/AuthScreen.tsx\",\n                        lineNumber: 96,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/AuthScreen.tsx\",\n                lineNumber: 40,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/AuthScreen.tsx\",\n            lineNumber: 39,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/AuthScreen.tsx\",\n        lineNumber: 38,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/AuthScreen.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/MainApp.tsx":
/*!************************************!*\
  !*** ./src/components/MainApp.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ MainApp)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(ssr)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/api */ \"(ssr)/./src/lib/api.ts\");\n/* harmony import */ var _MiningScreen__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./MiningScreen */ \"(ssr)/./src/components/MiningScreen.tsx\");\n/* harmony import */ var _TasksScreen__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./TasksScreen */ \"(ssr)/./src/components/TasksScreen.tsx\");\n/* harmony import */ var _ReferralsScreen__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./ReferralsScreen */ \"(ssr)/./src/components/ReferralsScreen.tsx\");\n/* harmony import */ var _AirdropScreen__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./AirdropScreen */ \"(ssr)/./src/components/AirdropScreen.tsx\");\n/* harmony import */ var _SubscriptionScreen__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./SubscriptionScreen */ \"(ssr)/./src/components/SubscriptionScreen.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\nfunction MainApp() {\n    const { user, updateUser, logout } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const [currentScreen, setCurrentScreen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('mining');\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"MainApp.useEffect\": ()=>{\n            fetchUserProfile();\n        }\n    }[\"MainApp.useEffect\"], []);\n    const fetchUserProfile = async ()=>{\n        try {\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_3__.apiClient.getUserProfile();\n            if (response.success) {\n                updateUser(response.user);\n            }\n        } catch (error) {\n            console.error('Failed to fetch user profile:', error);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const handleLogout = ()=>{\n        logout();\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-yellow-400 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MainApp.tsx\",\n                        lineNumber: 44,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-400\",\n                        children: \"Loading your profile...\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MainApp.tsx\",\n                        lineNumber: 45,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MainApp.tsx\",\n                lineNumber: 43,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MainApp.tsx\",\n            lineNumber: 42,\n            columnNumber: 7\n        }, this);\n    }\n    const renderScreen = ()=>{\n        switch(currentScreen){\n            case 'mining':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MiningScreen__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MainApp.tsx\",\n                    lineNumber: 54,\n                    columnNumber: 16\n                }, this);\n            case 'tasks':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_TasksScreen__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MainApp.tsx\",\n                    lineNumber: 56,\n                    columnNumber: 16\n                }, this);\n            case 'referrals':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ReferralsScreen__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MainApp.tsx\",\n                    lineNumber: 58,\n                    columnNumber: 16\n                }, this);\n            case 'airdrop':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AirdropScreen__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MainApp.tsx\",\n                    lineNumber: 60,\n                    columnNumber: 16\n                }, this);\n            case 'subscription':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_SubscriptionScreen__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {}, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MainApp.tsx\",\n                    lineNumber: 62,\n                    columnNumber: 16\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MiningScreen__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MainApp.tsx\",\n                    lineNumber: 64,\n                    columnNumber: 16\n                }, this);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-900\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"bg-gray-800 border-b border-gray-700 px-4 py-3\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-8 h-8 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-full flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm font-bold text-gray-900\",\n                                        children: \"⛏️\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MainApp.tsx\",\n                                        lineNumber: 75,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MainApp.tsx\",\n                                    lineNumber: 74,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                            className: \"text-lg font-bold text-white\",\n                                            children: \"Hehe Miner\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MainApp.tsx\",\n                                            lineNumber: 78,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-gray-400\",\n                                            children: [\n                                                user?.firstName,\n                                                \" \",\n                                                user?.lastName\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MainApp.tsx\",\n                                            lineNumber: 79,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MainApp.tsx\",\n                                    lineNumber: 77,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MainApp.tsx\",\n                            lineNumber: 73,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-right\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm font-semibold text-yellow-400\",\n                                            children: [\n                                                user?.totalBalance?.toFixed(2) || '0.00',\n                                                \" HEHE\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MainApp.tsx\",\n                                            lineNumber: 86,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-gray-400\",\n                                            children: [\n                                                \"Mining Power: \",\n                                                user?.miningPower?.toFixed(2) || '4.00',\n                                                \"/4h\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MainApp.tsx\",\n                                            lineNumber: 89,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MainApp.tsx\",\n                                    lineNumber: 85,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleLogout,\n                                    className: \"text-gray-400 hover:text-white transition-colors\",\n                                    children: \"\\uD83D\\uDEAA\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MainApp.tsx\",\n                                    lineNumber: 93,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MainApp.tsx\",\n                            lineNumber: 84,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MainApp.tsx\",\n                    lineNumber: 72,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MainApp.tsx\",\n                lineNumber: 71,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"pb-20\",\n                children: renderScreen()\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MainApp.tsx\",\n                lineNumber: 104,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                className: \"fixed bottom-0 left-0 right-0 bg-gray-800 border-t border-gray-700\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-around py-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setCurrentScreen('mining'),\n                            className: `flex flex-col items-center py-2 px-3 rounded-lg transition-colors ${currentScreen === 'mining' ? 'text-yellow-400 bg-gray-700' : 'text-gray-400 hover:text-white'}`,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xl mb-1\",\n                                    children: \"⛏️\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MainApp.tsx\",\n                                    lineNumber: 119,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xs\",\n                                    children: \"Mine\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MainApp.tsx\",\n                                    lineNumber: 120,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MainApp.tsx\",\n                            lineNumber: 111,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setCurrentScreen('tasks'),\n                            className: `flex flex-col items-center py-2 px-3 rounded-lg transition-colors ${currentScreen === 'tasks' ? 'text-yellow-400 bg-gray-700' : 'text-gray-400 hover:text-white'}`,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xl mb-1\",\n                                    children: \"\\uD83D\\uDCCB\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MainApp.tsx\",\n                                    lineNumber: 131,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xs\",\n                                    children: \"Tasks\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MainApp.tsx\",\n                                    lineNumber: 132,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MainApp.tsx\",\n                            lineNumber: 123,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setCurrentScreen('referrals'),\n                            className: `flex flex-col items-center py-2 px-3 rounded-lg transition-colors ${currentScreen === 'referrals' ? 'text-yellow-400 bg-gray-700' : 'text-gray-400 hover:text-white'}`,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xl mb-1\",\n                                    children: \"\\uD83D\\uDC65\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MainApp.tsx\",\n                                    lineNumber: 143,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xs\",\n                                    children: \"Referrals\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MainApp.tsx\",\n                                    lineNumber: 144,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MainApp.tsx\",\n                            lineNumber: 135,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setCurrentScreen('airdrop'),\n                            className: `flex flex-col items-center py-2 px-3 rounded-lg transition-colors ${currentScreen === 'airdrop' ? 'text-yellow-400 bg-gray-700' : 'text-gray-400 hover:text-white'}`,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xl mb-1\",\n                                    children: \"\\uD83E\\uDE82\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MainApp.tsx\",\n                                    lineNumber: 155,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xs\",\n                                    children: \"Airdrop\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MainApp.tsx\",\n                                    lineNumber: 156,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MainApp.tsx\",\n                            lineNumber: 147,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setCurrentScreen('subscription'),\n                            className: `flex flex-col items-center py-2 px-3 rounded-lg transition-colors ${currentScreen === 'subscription' ? 'text-yellow-400 bg-gray-700' : 'text-gray-400 hover:text-white'}`,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xl mb-1\",\n                                    children: \"⚡\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MainApp.tsx\",\n                                    lineNumber: 167,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xs\",\n                                    children: \"Upgrade\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MainApp.tsx\",\n                                    lineNumber: 168,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MainApp.tsx\",\n                            lineNumber: 159,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MainApp.tsx\",\n                    lineNumber: 110,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MainApp.tsx\",\n                lineNumber: 109,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MainApp.tsx\",\n        lineNumber: 69,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/MainApp.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/MiningScreen.tsx":
/*!*****************************************!*\
  !*** ./src/components/MiningScreen.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ MiningScreen)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(ssr)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/api */ \"(ssr)/./src/lib/api.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction MiningScreen() {\n    const { user, updateUser } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [timeRemaining, setTimeRemaining] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [canClaim, setCanClaim] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [success, setSuccess] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"MiningScreen.useEffect\": ()=>{\n            if (user?.currentMiningSession) {\n                const interval = setInterval({\n                    \"MiningScreen.useEffect.interval\": ()=>{\n                        updateMiningTimer();\n                    }\n                }[\"MiningScreen.useEffect.interval\"], 1000);\n                return ({\n                    \"MiningScreen.useEffect\": ()=>clearInterval(interval)\n                })[\"MiningScreen.useEffect\"];\n            }\n        }\n    }[\"MiningScreen.useEffect\"], [\n        user?.currentMiningSession\n    ]);\n    const updateMiningTimer = ()=>{\n        if (!user?.currentMiningSession) return;\n        const startTime = new Date(user.currentMiningSession.startTime).getTime();\n        const miningDuration = 4 * 60 * 60 * 1000 // 4 hours\n        ;\n        const endTime = startTime + miningDuration;\n        const now = Date.now();\n        const remaining = endTime - now;\n        if (remaining <= 0) {\n            setTimeRemaining(0);\n            setCanClaim(true);\n        } else {\n            setTimeRemaining(remaining);\n            setCanClaim(false);\n        }\n    };\n    const formatTime = (milliseconds)=>{\n        const hours = Math.floor(milliseconds / (1000 * 60 * 60));\n        const minutes = Math.floor(milliseconds % (1000 * 60 * 60) / (1000 * 60));\n        const seconds = Math.floor(milliseconds % (1000 * 60) / 1000);\n        return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;\n    };\n    const handleStartMining = async ()=>{\n        if (!user?.hasBasicPlan) {\n            setError('You need to purchase the basic plan first!');\n            return;\n        }\n        setIsLoading(true);\n        setError('');\n        setSuccess('');\n        try {\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_3__.apiClient.startMining();\n            if (response.success) {\n                setSuccess('Mining started successfully!');\n                // Refresh user profile\n                const profileResponse = await _lib_api__WEBPACK_IMPORTED_MODULE_3__.apiClient.getUserProfile();\n                if (profileResponse.success) {\n                    updateUser(profileResponse.user);\n                }\n            } else {\n                setError(response.error || 'Failed to start mining');\n            }\n        } catch (error) {\n            setError('Network error occurred');\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const handleClaimTokens = async ()=>{\n        if (!user?.currentMiningSession) return;\n        setIsLoading(true);\n        setError('');\n        setSuccess('');\n        try {\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_3__.apiClient.claimMining(user.currentMiningSession.id);\n            if (response.success) {\n                setSuccess(`Claimed ${response.tokensEarned} HEHE tokens!`);\n                // Refresh user profile\n                const profileResponse = await _lib_api__WEBPACK_IMPORTED_MODULE_3__.apiClient.getUserProfile();\n                if (profileResponse.success) {\n                    updateUser(profileResponse.user);\n                }\n            } else {\n                setError(response.error || 'Failed to claim tokens');\n            }\n        } catch (error) {\n            setError('Network error occurred');\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const getMiningProgress = ()=>{\n        if (!user?.currentMiningSession || timeRemaining <= 0) return 100;\n        const startTime = new Date(user.currentMiningSession.startTime).getTime();\n        const miningDuration = 4 * 60 * 60 * 1000 // 4 hours\n        ;\n        const elapsed = Date.now() - startTime;\n        return Math.min(elapsed / miningDuration * 100, 100);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"p-6 max-w-md mx-auto\",\n        children: [\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-red-500/10 border border-red-500/20 rounded-lg p-3 mb-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-red-400 text-sm\",\n                    children: error\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                    lineNumber: 121,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                lineNumber: 120,\n                columnNumber: 9\n            }, this),\n            success && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-green-500/10 border border-green-500/20 rounded-lg p-3 mb-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-green-400 text-sm\",\n                    children: success\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                    lineNumber: 127,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                lineNumber: 126,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gray-800 rounded-2xl p-6 mb-6 border border-gray-700\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-24 h-24 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-full mx-auto mb-4 flex items-center justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-3xl\",\n                                children: \"⛏️\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                lineNumber: 135,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                            lineNumber: 134,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-2xl font-bold text-white mb-2\",\n                            children: \"Mining Status\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                            lineNumber: 138,\n                            columnNumber: 11\n                        }, this),\n                        !user?.hasBasicPlan ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-400 mb-4\",\n                                    children: \"Purchase the basic plan to start mining!\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                    lineNumber: 142,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-yellow-500/10 border border-yellow-500/20 rounded-lg p-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-yellow-400 text-sm\",\n                                        children: \"\\uD83D\\uDCA1 Basic Plan: $1 - Unlock mining 4 HEHE tokens every 4 hours\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                        lineNumber: 144,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                    lineNumber: 143,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                            lineNumber: 141,\n                            columnNumber: 13\n                        }, this) : user?.currentMiningSession && timeRemaining > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-400 mb-4\",\n                                    children: \"Mining in progress...\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                    lineNumber: 151,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-gray-700 rounded-full h-3 mb-2\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-gradient-to-r from-yellow-400 to-orange-500 h-3 rounded-full transition-all duration-1000\",\n                                                style: {\n                                                    width: `${getMiningProgress()}%`\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                                lineNumber: 154,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                            lineNumber: 153,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-400\",\n                                            children: [\n                                                \"Progress: \",\n                                                getMiningProgress().toFixed(1),\n                                                \"%\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                            lineNumber: 159,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                    lineNumber: 152,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-3xl font-mono text-yellow-400 mb-2\",\n                                    children: formatTime(timeRemaining)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                    lineNumber: 163,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-400 text-sm\",\n                                    children: \"Time remaining\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                    lineNumber: 166,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-white mt-2\",\n                                    children: [\n                                        \"Earning: \",\n                                        user.currentMiningSession.tokensEarned,\n                                        \" HEHE\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                    lineNumber: 167,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                            lineNumber: 150,\n                            columnNumber: 13\n                        }, this) : canClaim && user?.currentMiningSession ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-green-400 mb-4 text-lg font-semibold\",\n                                    children: \"Mining Complete! \\uD83C\\uDF89\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                    lineNumber: 173,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-white mb-4\",\n                                    children: [\n                                        \"Ready to claim: \",\n                                        user.currentMiningSession.tokensEarned,\n                                        \" HEHE\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                    lineNumber: 174,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleClaimTokens,\n                                    disabled: isLoading,\n                                    className: \"w-full bg-green-600 hover:bg-green-700 disabled:bg-green-800 text-white font-semibold py-3 px-4 rounded-lg transition-colors\",\n                                    children: isLoading ? 'Claiming...' : 'Claim Tokens'\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                    lineNumber: 177,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                            lineNumber: 172,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-400 mb-4\",\n                                    children: \"Ready to mine!\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                    lineNumber: 187,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-white mb-4\",\n                                    children: [\n                                        \"Mining Power: \",\n                                        user?.miningPower,\n                                        \" HEHE per 4 hours\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                    lineNumber: 188,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleStartMining,\n                                    disabled: isLoading,\n                                    className: \"w-full bg-yellow-600 hover:bg-yellow-700 disabled:bg-yellow-800 text-white font-semibold py-3 px-4 rounded-lg transition-colors\",\n                                    children: isLoading ? 'Starting...' : 'Start Mining'\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                    lineNumber: 191,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                            lineNumber: 186,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                    lineNumber: 133,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                lineNumber: 132,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-2 gap-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gray-800 rounded-lg p-4 border border-gray-700\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-400 text-sm\",\n                                children: \"Total Balance\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                lineNumber: 206,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-white text-xl font-semibold\",\n                                children: [\n                                    user?.totalBalance?.toFixed(2) || '0.00',\n                                    \" HEHE\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                lineNumber: 207,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                        lineNumber: 205,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gray-800 rounded-lg p-4 border border-gray-700\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-400 text-sm\",\n                                children: \"Mining Power\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                lineNumber: 212,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-white text-xl font-semibold\",\n                                children: [\n                                    user?.miningPower?.toFixed(2) || '4.00',\n                                    \"/4h\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                lineNumber: 213,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                        lineNumber: 211,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                lineNumber: 204,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n        lineNumber: 117,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/MiningScreen.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ReferralsScreen.tsx":
/*!********************************************!*\
  !*** ./src/components/ReferralsScreen.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ReferralsScreen)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(ssr)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/api */ \"(ssr)/./src/lib/api.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction ReferralsScreen() {\n    const { user } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const [referrals, setReferrals] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        totalReferrals: 0,\n        totalRewards: 0\n    });\n    const [referralLink, setReferralLink] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [copySuccess, setCopySuccess] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ReferralsScreen.useEffect\": ()=>{\n            fetchReferrals();\n        }\n    }[\"ReferralsScreen.useEffect\"], []);\n    const fetchReferrals = async ()=>{\n        try {\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_3__.apiClient.getReferrals();\n            if (response.success) {\n                setReferrals(response.referrals);\n                setStats(response.stats);\n                setReferralLink(response.referralLink);\n            } else {\n                setError(response.error || 'Failed to fetch referrals');\n            }\n        } catch (error) {\n            setError('Network error occurred');\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const handleCopyLink = async ()=>{\n        try {\n            await navigator.clipboard.writeText(referralLink);\n            setCopySuccess(true);\n            setTimeout(()=>setCopySuccess(false), 2000);\n        } catch (error) {\n            // Fallback for browsers that don't support clipboard API\n            const textArea = document.createElement('textarea');\n            textArea.value = referralLink;\n            document.body.appendChild(textArea);\n            textArea.select();\n            document.execCommand('copy');\n            document.body.removeChild(textArea);\n            setCopySuccess(true);\n            setTimeout(()=>setCopySuccess(false), 2000);\n        }\n    };\n    const formatDate = (dateString)=>{\n        return new Date(dateString).toLocaleDateString('en-US', {\n            month: 'short',\n            day: 'numeric',\n            year: 'numeric'\n        });\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"p-6 max-w-md mx-auto\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-yellow-400 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/ReferralsScreen.tsx\",\n                        lineNumber: 84,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-400\",\n                        children: \"Loading referrals...\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/ReferralsScreen.tsx\",\n                        lineNumber: 85,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/ReferralsScreen.tsx\",\n                lineNumber: 83,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/ReferralsScreen.tsx\",\n            lineNumber: 82,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"p-6 max-w-md mx-auto\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-2xl font-bold text-white mb-2\",\n                        children: \"Referrals\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/ReferralsScreen.tsx\",\n                        lineNumber: 94,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-400\",\n                        children: \"Invite friends and earn 0.5 HEHE per referral\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/ReferralsScreen.tsx\",\n                        lineNumber: 95,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/ReferralsScreen.tsx\",\n                lineNumber: 93,\n                columnNumber: 7\n            }, this),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-red-500/10 border border-red-500/20 rounded-lg p-3 mb-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-red-400 text-sm\",\n                    children: error\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/ReferralsScreen.tsx\",\n                    lineNumber: 101,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/ReferralsScreen.tsx\",\n                lineNumber: 100,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gray-800 rounded-lg p-4 border border-gray-700 mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-white font-semibold mb-3\",\n                        children: \"Your Referral Link\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/ReferralsScreen.tsx\",\n                        lineNumber: 107,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"text\",\n                                value: referralLink,\n                                readOnly: true,\n                                className: \"flex-1 bg-gray-700 text-white px-3 py-2 rounded border border-gray-600 text-sm\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/ReferralsScreen.tsx\",\n                                lineNumber: 109,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleCopyLink,\n                                className: `px-4 py-2 rounded text-sm font-medium transition-colors ${copySuccess ? 'bg-green-600 text-white' : 'bg-blue-600 hover:bg-blue-700 text-white'}`,\n                                children: copySuccess ? 'Copied!' : 'Copy'\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/ReferralsScreen.tsx\",\n                                lineNumber: 115,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/ReferralsScreen.tsx\",\n                        lineNumber: 108,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-400 text-xs mt-2\",\n                        children: \"Share this link with friends to earn 0.5 HEHE tokens for each signup\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/ReferralsScreen.tsx\",\n                        lineNumber: 126,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/ReferralsScreen.tsx\",\n                lineNumber: 106,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-2 gap-4 mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gray-800 rounded-lg p-4 border border-gray-700 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-400 text-sm\",\n                                children: \"Total Referrals\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/ReferralsScreen.tsx\",\n                                lineNumber: 134,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-white text-2xl font-bold\",\n                                children: stats.totalReferrals\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/ReferralsScreen.tsx\",\n                                lineNumber: 135,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/ReferralsScreen.tsx\",\n                        lineNumber: 133,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gray-800 rounded-lg p-4 border border-gray-700 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-400 text-sm\",\n                                children: \"Total Earned\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/ReferralsScreen.tsx\",\n                                lineNumber: 138,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-yellow-400 text-2xl font-bold\",\n                                children: stats.totalRewards.toFixed(1)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/ReferralsScreen.tsx\",\n                                lineNumber: 139,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/ReferralsScreen.tsx\",\n                        lineNumber: 137,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/ReferralsScreen.tsx\",\n                lineNumber: 132,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gray-800 rounded-lg border border-gray-700\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-4 border-b border-gray-700\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-white font-semibold\",\n                            children: \"Recent Referrals\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/ReferralsScreen.tsx\",\n                            lineNumber: 146,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/ReferralsScreen.tsx\",\n                        lineNumber: 145,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-h-64 overflow-y-auto\",\n                        children: referrals.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-4 text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-400\",\n                                    children: \"No referrals yet\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/ReferralsScreen.tsx\",\n                                    lineNumber: 152,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-500 text-sm mt-1\",\n                                    children: \"Start sharing your referral link to earn rewards!\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/ReferralsScreen.tsx\",\n                                    lineNumber: 153,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/ReferralsScreen.tsx\",\n                            lineNumber: 151,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"divide-y divide-gray-700\",\n                            children: referrals.map((referral)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-4 flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-white font-medium\",\n                                                    children: [\n                                                        referral.referred.firstName,\n                                                        \" \",\n                                                        referral.referred.lastName,\n                                                        referral.referred.username && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-400 ml-1\",\n                                                            children: [\n                                                                \"(@\",\n                                                                referral.referred.username,\n                                                                \")\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/ReferralsScreen.tsx\",\n                                                            lineNumber: 165,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/ReferralsScreen.tsx\",\n                                                    lineNumber: 162,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-400 text-sm\",\n                                                    children: [\n                                                        \"Joined \",\n                                                        formatDate(referral.referred.joinedAt)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/ReferralsScreen.tsx\",\n                                                    lineNumber: 170,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/ReferralsScreen.tsx\",\n                                            lineNumber: 161,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-right\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-yellow-400 font-semibold\",\n                                                    children: [\n                                                        \"+\",\n                                                        referral.reward,\n                                                        \" HEHE\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/ReferralsScreen.tsx\",\n                                                    lineNumber: 175,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-500 text-xs\",\n                                                    children: formatDate(referral.createdAt)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/ReferralsScreen.tsx\",\n                                                    lineNumber: 178,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/ReferralsScreen.tsx\",\n                                            lineNumber: 174,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, referral.id, true, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/ReferralsScreen.tsx\",\n                                    lineNumber: 160,\n                                    columnNumber: 17\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/ReferralsScreen.tsx\",\n                            lineNumber: 158,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/ReferralsScreen.tsx\",\n                        lineNumber: 149,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/ReferralsScreen.tsx\",\n                lineNumber: 144,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-6 bg-blue-500/10 border border-blue-500/20 rounded-lg p-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                        className: \"text-blue-400 font-semibold mb-2\",\n                        children: \"How Referrals Work\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/ReferralsScreen.tsx\",\n                        lineNumber: 191,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                        className: \"text-blue-300 text-sm space-y-1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: \"• Share your unique referral link\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/ReferralsScreen.tsx\",\n                                lineNumber: 193,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: \"• When someone signs up using your link, you both benefit\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/ReferralsScreen.tsx\",\n                                lineNumber: 194,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: \"• You earn 0.5 HEHE tokens for each successful referral\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/ReferralsScreen.tsx\",\n                                lineNumber: 195,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: \"• No limit on the number of referrals you can make\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/ReferralsScreen.tsx\",\n                                lineNumber: 196,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/ReferralsScreen.tsx\",\n                        lineNumber: 192,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/ReferralsScreen.tsx\",\n                lineNumber: 190,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/ReferralsScreen.tsx\",\n        lineNumber: 92,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ReferralsScreen.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/SubscriptionScreen.tsx":
/*!***********************************************!*\
  !*** ./src/components/SubscriptionScreen.tsx ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SubscriptionScreen)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(ssr)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/api */ \"(ssr)/./src/lib/api.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction SubscriptionScreen() {\n    const { user, updateUser } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [purchasingPlan, setPurchasingPlan] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [success, setSuccess] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const handlePurchaseBasicPlan = async ()=>{\n        setPurchasingPlan('basic');\n        setIsLoading(true);\n        setError('');\n        setSuccess('');\n        try {\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_3__.apiClient.purchaseBasicPlan();\n            if (response.success) {\n                setSuccess('Basic plan purchased successfully! You can now start mining.');\n                updateUser({\n                    hasBasicPlan: true\n                });\n            } else {\n                setError(response.error || 'Failed to purchase basic plan');\n            }\n        } catch (error) {\n            setError('Network error occurred');\n        } finally{\n            setIsLoading(false);\n            setPurchasingPlan(null);\n        }\n    };\n    const handlePurchaseSpeedUpgrade = async ()=>{\n        setPurchasingPlan('speed');\n        setIsLoading(true);\n        setError('');\n        setSuccess('');\n        try {\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_3__.apiClient.purchaseSpeedUpgrade();\n            if (response.success) {\n                setSuccess('Speed upgrade purchased! Your mining power has increased.');\n                updateUser({\n                    miningPower: response.user.miningPower,\n                    speedUpgrades: response.user.speedUpgrades\n                });\n            } else {\n                setError(response.error || 'Failed to purchase speed upgrade');\n            }\n        } catch (error) {\n            setError('Network error occurred');\n        } finally{\n            setIsLoading(false);\n            setPurchasingPlan(null);\n        }\n    };\n    // Check if user has active mining session\n    const hasActiveMining = user?.currentMiningSession && user?.canMine === false;\n    const isUpgradeDisabled = hasActiveMining || isLoading && purchasingPlan === 'speed';\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"p-6 max-w-md mx-auto\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-2xl font-bold text-white mb-2\",\n                        children: \"Upgrades\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/SubscriptionScreen.tsx\",\n                        lineNumber: 70,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-400\",\n                        children: \"Enhance your mining capabilities\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/SubscriptionScreen.tsx\",\n                        lineNumber: 71,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/SubscriptionScreen.tsx\",\n                lineNumber: 69,\n                columnNumber: 7\n            }, this),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-red-500/10 border border-red-500/20 rounded-lg p-3 mb-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-red-400 text-sm\",\n                    children: error\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/SubscriptionScreen.tsx\",\n                    lineNumber: 77,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/SubscriptionScreen.tsx\",\n                lineNumber: 76,\n                columnNumber: 9\n            }, this),\n            success && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-green-500/10 border border-green-500/20 rounded-lg p-3 mb-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-green-400 text-sm\",\n                    children: success\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/SubscriptionScreen.tsx\",\n                    lineNumber: 83,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/SubscriptionScreen.tsx\",\n                lineNumber: 82,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gray-800 rounded-lg border border-gray-700 p-4 mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-white font-semibold mb-3\",\n                        children: \"Current Status\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/SubscriptionScreen.tsx\",\n                        lineNumber: 89,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-gray-300\",\n                                        children: \"Plan Status\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/SubscriptionScreen.tsx\",\n                                        lineNumber: 92,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: `font-medium ${user?.hasBasicPlan ? 'text-green-400' : 'text-red-400'}`,\n                                        children: user?.hasBasicPlan ? 'Basic Plan Active' : 'No Plan'\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/SubscriptionScreen.tsx\",\n                                        lineNumber: 93,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/SubscriptionScreen.tsx\",\n                                lineNumber: 91,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-gray-300\",\n                                        children: \"Mining Power\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/SubscriptionScreen.tsx\",\n                                        lineNumber: 98,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-white font-medium\",\n                                        children: [\n                                            user?.miningPower?.toFixed(2) || '4.00',\n                                            \" HEHE/4h\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/SubscriptionScreen.tsx\",\n                                        lineNumber: 99,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/SubscriptionScreen.tsx\",\n                                lineNumber: 97,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-gray-300\",\n                                        children: \"Speed Upgrades\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/SubscriptionScreen.tsx\",\n                                        lineNumber: 104,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-white font-medium\",\n                                        children: user?.speedUpgrades || 0\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/SubscriptionScreen.tsx\",\n                                        lineNumber: 105,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/SubscriptionScreen.tsx\",\n                                lineNumber: 103,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/SubscriptionScreen.tsx\",\n                        lineNumber: 90,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/SubscriptionScreen.tsx\",\n                lineNumber: 88,\n                columnNumber: 7\n            }, this),\n            !user?.hasBasicPlan && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg p-6 mb-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-12 h-12 bg-white/20 rounded-full mx-auto flex items-center justify-center mb-3\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-xl\",\n                                children: \"⛏️\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/SubscriptionScreen.tsx\",\n                                lineNumber: 117,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/SubscriptionScreen.tsx\",\n                            lineNumber: 116,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-white text-lg font-bold mb-2\",\n                            children: \"Basic Mining Plan\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/SubscriptionScreen.tsx\",\n                            lineNumber: 119,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-white/80 text-sm mb-4\",\n                            children: \"Unlock the ability to mine HEHE tokens\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/SubscriptionScreen.tsx\",\n                            lineNumber: 120,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white/10 rounded-lg p-3 mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-white font-semibold text-lg\",\n                                    children: \"$1.00\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/SubscriptionScreen.tsx\",\n                                    lineNumber: 125,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-white/80 text-sm\",\n                                    children: \"One-time purchase\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/SubscriptionScreen.tsx\",\n                                    lineNumber: 126,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/SubscriptionScreen.tsx\",\n                            lineNumber: 124,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-left mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"text-white font-semibold mb-2\",\n                                    children: \"Includes:\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/SubscriptionScreen.tsx\",\n                                    lineNumber: 130,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"text-white/80 text-sm space-y-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"• Mine 4 HEHE tokens every 4 hours\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/SubscriptionScreen.tsx\",\n                                            lineNumber: 132,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"• Access to all tasks and referrals\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/SubscriptionScreen.tsx\",\n                                            lineNumber: 133,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"• Eligible for future airdrops\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/SubscriptionScreen.tsx\",\n                                            lineNumber: 134,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"• Ability to purchase speed upgrades\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/SubscriptionScreen.tsx\",\n                                            lineNumber: 135,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/SubscriptionScreen.tsx\",\n                                    lineNumber: 131,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/SubscriptionScreen.tsx\",\n                            lineNumber: 129,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: handlePurchaseBasicPlan,\n                            disabled: isLoading && purchasingPlan === 'basic',\n                            className: \"w-full bg-white text-blue-600 font-semibold py-3 px-4 rounded-lg hover:bg-gray-100 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors\",\n                            children: isLoading && purchasingPlan === 'basic' ? 'Processing...' : 'Purchase Basic Plan'\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/SubscriptionScreen.tsx\",\n                            lineNumber: 139,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/SubscriptionScreen.tsx\",\n                    lineNumber: 115,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/SubscriptionScreen.tsx\",\n                lineNumber: 114,\n                columnNumber: 9\n            }, this),\n            user?.hasBasicPlan && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gray-800 rounded-lg border border-gray-700 p-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-12 h-12 bg-yellow-500/20 rounded-full mx-auto flex items-center justify-center mb-3\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-xl\",\n                                children: \"⚡\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/SubscriptionScreen.tsx\",\n                                lineNumber: 155,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/SubscriptionScreen.tsx\",\n                            lineNumber: 154,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-white text-lg font-bold mb-2\",\n                            children: \"Speed Upgrade\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/SubscriptionScreen.tsx\",\n                            lineNumber: 157,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-400 text-sm mb-4\",\n                            children: \"Increase your mining power by 0.25 HEHE per 4 hours\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/SubscriptionScreen.tsx\",\n                            lineNumber: 158,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-yellow-500/10 border border-yellow-500/20 rounded-lg p-3 mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-yellow-400 font-semibold text-lg\",\n                                    children: \"$1.00\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/SubscriptionScreen.tsx\",\n                                    lineNumber: 163,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-yellow-300 text-sm\",\n                                    children: \"Per upgrade\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/SubscriptionScreen.tsx\",\n                                    lineNumber: 164,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/SubscriptionScreen.tsx\",\n                            lineNumber: 162,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-left mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"text-white font-semibold mb-2\",\n                                    children: \"Upgrade Details:\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/SubscriptionScreen.tsx\",\n                                    lineNumber: 168,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"text-gray-300 text-sm space-y-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"• +0.25 HEHE tokens per 4-hour cycle\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/SubscriptionScreen.tsx\",\n                                            lineNumber: 170,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"• Permanent increase to mining power\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/SubscriptionScreen.tsx\",\n                                            lineNumber: 171,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"• No limit on number of upgrades\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/SubscriptionScreen.tsx\",\n                                            lineNumber: 172,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"• Compounds with existing upgrades\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/SubscriptionScreen.tsx\",\n                                            lineNumber: 173,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/SubscriptionScreen.tsx\",\n                                    lineNumber: 169,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/SubscriptionScreen.tsx\",\n                            lineNumber: 167,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gray-700 rounded-lg p-3 mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-300 text-sm\",\n                                    children: [\n                                        \"Current: \",\n                                        user?.miningPower?.toFixed(2),\n                                        \" HEHE/4h\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/SubscriptionScreen.tsx\",\n                                    lineNumber: 178,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-white text-sm\",\n                                    children: [\n                                        \"After upgrade: \",\n                                        ((user?.miningPower || 4) + 0.25).toFixed(2),\n                                        \" HEHE/4h\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/SubscriptionScreen.tsx\",\n                                    lineNumber: 181,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/SubscriptionScreen.tsx\",\n                            lineNumber: 177,\n                            columnNumber: 13\n                        }, this),\n                        hasActiveMining && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-orange-500/10 border border-orange-500/20 rounded-lg p-3 mb-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-orange-400 text-sm\",\n                                children: \"⚠️ Cannot purchase upgrades during active mining session. Complete your current mining first.\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/SubscriptionScreen.tsx\",\n                                lineNumber: 188,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/SubscriptionScreen.tsx\",\n                            lineNumber: 187,\n                            columnNumber: 15\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: handlePurchaseSpeedUpgrade,\n                            disabled: isUpgradeDisabled,\n                            className: `w-full font-semibold py-3 px-4 rounded-lg transition-colors ${isUpgradeDisabled ? 'bg-gray-600 cursor-not-allowed text-gray-400' : 'bg-yellow-600 hover:bg-yellow-700 text-white'}`,\n                            children: isLoading && purchasingPlan === 'speed' ? 'Processing...' : hasActiveMining ? 'Mining in Progress - Upgrade Locked' : 'Purchase Speed Upgrade'\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/SubscriptionScreen.tsx\",\n                            lineNumber: 194,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/SubscriptionScreen.tsx\",\n                    lineNumber: 153,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/SubscriptionScreen.tsx\",\n                lineNumber: 152,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-6 bg-blue-500/10 border border-blue-500/20 rounded-lg p-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                        className: \"text-blue-400 font-semibold mb-2\",\n                        children: \"\\uD83D\\uDCA1 Upgrade Tips\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/SubscriptionScreen.tsx\",\n                        lineNumber: 216,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                        className: \"text-blue-300 text-sm space-y-1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: \"• Speed upgrades stack - buy multiple for even faster mining\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/SubscriptionScreen.tsx\",\n                                lineNumber: 218,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: \"• All purchases are simulated for this demo\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/SubscriptionScreen.tsx\",\n                                lineNumber: 219,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: \"• In production, this would integrate with real payment processors\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/SubscriptionScreen.tsx\",\n                                lineNumber: 220,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: \"• Your upgrades are permanent and apply to all future mining sessions\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/SubscriptionScreen.tsx\",\n                                lineNumber: 221,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/SubscriptionScreen.tsx\",\n                        lineNumber: 217,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/SubscriptionScreen.tsx\",\n                lineNumber: 215,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/SubscriptionScreen.tsx\",\n        lineNumber: 68,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/SubscriptionScreen.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/TasksScreen.tsx":
/*!****************************************!*\
  !*** ./src/components/TasksScreen.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ TasksScreen)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(ssr)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/api */ \"(ssr)/./src/lib/api.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction TasksScreen() {\n    const { updateUser } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const [tasks, setTasks] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [completingTask, setCompletingTask] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [success, setSuccess] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TasksScreen.useEffect\": ()=>{\n            fetchTasks();\n        }\n    }[\"TasksScreen.useEffect\"], []);\n    const fetchTasks = async ()=>{\n        try {\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_3__.apiClient.getTasks();\n            if (response.success) {\n                setTasks(response.tasks);\n            } else {\n                setError(response.error || 'Failed to fetch tasks');\n            }\n        } catch (error) {\n            setError('Network error occurred');\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const handleCompleteTask = async (task)=>{\n        if (task.link) {\n            // Open the link in a new tab\n            window.open(task.link, '_blank');\n        }\n        setCompletingTask(task.id);\n        setError('');\n        setSuccess('');\n        try {\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_3__.apiClient.completeTask(task.id);\n            if (response.success) {\n                setSuccess(`Task completed! Earned ${response.reward} HEHE tokens`);\n                // Update the task in the list\n                setTasks((prevTasks)=>prevTasks.map((t)=>t.id === task.id ? {\n                            ...t,\n                            isCompleted: true,\n                            isRewardClaimed: true\n                        } : t));\n                // Update user balance\n                updateUser({\n                    totalBalance: response.newBalance\n                });\n            } else {\n                setError(response.error || 'Failed to complete task');\n            }\n        } catch (error) {\n            setError('Network error occurred');\n        } finally{\n            setCompletingTask(null);\n        }\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"p-6 max-w-md mx-auto\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-yellow-400 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/TasksScreen.tsx\",\n                        lineNumber: 86,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-400\",\n                        children: \"Loading tasks...\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/TasksScreen.tsx\",\n                        lineNumber: 87,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/TasksScreen.tsx\",\n                lineNumber: 85,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/TasksScreen.tsx\",\n            lineNumber: 84,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"p-6 max-w-md mx-auto\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-2xl font-bold text-white mb-2\",\n                        children: \"Tasks\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/TasksScreen.tsx\",\n                        lineNumber: 96,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-400\",\n                        children: \"Complete tasks to earn bonus HEHE tokens\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/TasksScreen.tsx\",\n                        lineNumber: 97,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/TasksScreen.tsx\",\n                lineNumber: 95,\n                columnNumber: 7\n            }, this),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-red-500/10 border border-red-500/20 rounded-lg p-3 mb-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-red-400 text-sm\",\n                    children: error\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/TasksScreen.tsx\",\n                    lineNumber: 103,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/TasksScreen.tsx\",\n                lineNumber: 102,\n                columnNumber: 9\n            }, this),\n            success && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-green-500/10 border border-green-500/20 rounded-lg p-3 mb-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-green-400 text-sm\",\n                    children: success\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/TasksScreen.tsx\",\n                    lineNumber: 109,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/TasksScreen.tsx\",\n                lineNumber: 108,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-4\",\n                children: tasks.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center py-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-400\",\n                        children: \"No tasks available at the moment\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/TasksScreen.tsx\",\n                        lineNumber: 117,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/TasksScreen.tsx\",\n                    lineNumber: 116,\n                    columnNumber: 11\n                }, this) : tasks.map((task)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: `bg-gray-800 rounded-lg p-4 border ${task.isCompleted ? 'border-green-500/30 bg-green-500/5' : 'border-gray-700'}`,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-start justify-between mb-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-white font-semibold mb-1\",\n                                                children: task.title\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/TasksScreen.tsx\",\n                                                lineNumber: 131,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-400 text-sm mb-2\",\n                                                children: task.description\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/TasksScreen.tsx\",\n                                                lineNumber: 132,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-yellow-400 font-semibold\",\n                                                        children: [\n                                                            \"+\",\n                                                            task.reward,\n                                                            \" HEHE\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/TasksScreen.tsx\",\n                                                        lineNumber: 134,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    task.isCompleted && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-green-400 text-sm\",\n                                                        children: \"✓ Completed\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/TasksScreen.tsx\",\n                                                        lineNumber: 138,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/TasksScreen.tsx\",\n                                                lineNumber: 133,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/TasksScreen.tsx\",\n                                        lineNumber: 130,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"ml-4\",\n                                        children: task.isCompleted ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-10 h-10 bg-green-500 rounded-full flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white text-sm\",\n                                                children: \"✓\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/TasksScreen.tsx\",\n                                                lineNumber: 146,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/TasksScreen.tsx\",\n                                            lineNumber: 145,\n                                            columnNumber: 21\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>handleCompleteTask(task),\n                                            disabled: completingTask === task.id,\n                                            className: \"bg-blue-600 hover:bg-blue-700 disabled:bg-blue-800 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors\",\n                                            children: completingTask === task.id ? 'Completing...' : 'Do Task'\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/TasksScreen.tsx\",\n                                            lineNumber: 149,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/TasksScreen.tsx\",\n                                        lineNumber: 143,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/TasksScreen.tsx\",\n                                lineNumber: 129,\n                                columnNumber: 15\n                            }, this),\n                            task.link && !task.isCompleted && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-xs text-gray-500 mt-2\",\n                                children: '\\uD83D\\uDCA1 Clicking \"Do Task\" will open the link and mark the task as complete'\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/TasksScreen.tsx\",\n                                lineNumber: 161,\n                                columnNumber: 17\n                            }, this)\n                        ]\n                    }, task.id, true, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/TasksScreen.tsx\",\n                        lineNumber: 121,\n                        columnNumber: 13\n                    }, this))\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/TasksScreen.tsx\",\n                lineNumber: 114,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-8 bg-gray-800 rounded-lg p-4 border border-gray-700\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-white font-semibold mb-2\",\n                        children: \"Task Statistics\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/TasksScreen.tsx\",\n                        lineNumber: 172,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-2 gap-4 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-400 text-sm\",\n                                        children: \"Completed\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/TasksScreen.tsx\",\n                                        lineNumber: 175,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-white text-xl font-semibold\",\n                                        children: tasks.filter((t)=>t.isCompleted).length\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/TasksScreen.tsx\",\n                                        lineNumber: 176,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/TasksScreen.tsx\",\n                                lineNumber: 174,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-400 text-sm\",\n                                        children: \"Available\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/TasksScreen.tsx\",\n                                        lineNumber: 181,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-white text-xl font-semibold\",\n                                        children: tasks.filter((t)=>!t.isCompleted).length\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/TasksScreen.tsx\",\n                                        lineNumber: 182,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/TasksScreen.tsx\",\n                                lineNumber: 180,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/TasksScreen.tsx\",\n                        lineNumber: 173,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/TasksScreen.tsx\",\n                lineNumber: 171,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/TasksScreen.tsx\",\n        lineNumber: 94,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/TasksScreen.tsx\n");

/***/ }),

/***/ "(ssr)/./src/contexts/AuthContext.tsx":
/*!**************************************!*\
  !*** ./src/contexts/AuthContext.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ AuthProvider,useAuth auto */ \n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction AuthProvider({ children }) {\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [token, setToken] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            // Check for stored auth data on mount\n            const storedToken = localStorage.getItem('auth_token');\n            const storedUser = localStorage.getItem('user_data');\n            if (storedToken && storedUser) {\n                try {\n                    setToken(storedToken);\n                    setUser(JSON.parse(storedUser));\n                } catch (error) {\n                    console.error('Error parsing stored user data:', error);\n                    localStorage.removeItem('auth_token');\n                    localStorage.removeItem('user_data');\n                }\n            }\n            setIsLoading(false);\n        }\n    }[\"AuthProvider.useEffect\"], []);\n    const login = (userData, authToken)=>{\n        setUser(userData);\n        setToken(authToken);\n        localStorage.setItem('auth_token', authToken);\n        localStorage.setItem('user_data', JSON.stringify(userData));\n    };\n    const logout = ()=>{\n        setUser(null);\n        setToken(null);\n        localStorage.removeItem('auth_token');\n        localStorage.removeItem('user_data');\n    };\n    const updateUser = (userData)=>{\n        if (user) {\n            const updatedUser = {\n                ...user,\n                ...userData\n            };\n            setUser(updatedUser);\n            localStorage.setItem('user_data', JSON.stringify(updatedUser));\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: {\n            user,\n            token,\n            login,\n            logout,\n            updateUser,\n            isLoading\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/contexts/AuthContext.tsx\",\n        lineNumber: 81,\n        columnNumber: 5\n    }, this);\n}\nfunction useAuth() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error('useAuth must be used within an AuthProvider');\n    }\n    return context;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/contexts/AuthContext.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/api.ts":
/*!************************!*\
  !*** ./src/lib/api.ts ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   apiClient: () => (/* binding */ apiClient)\n/* harmony export */ });\nconst API_BASE_URL = \"http://localhost:3000\" || 0;\nclass ApiClient {\n    constructor(baseURL){\n        this.token = null;\n        this.baseURL = baseURL;\n    }\n    setToken(token) {\n        this.token = token;\n    }\n    async request(endpoint, options = {}) {\n        const url = `${this.baseURL}/api${endpoint}`;\n        const headers = {\n            'Content-Type': 'application/json',\n            ...options.headers\n        };\n        if (this.token) {\n            headers.Authorization = `Bearer ${this.token}`;\n        }\n        try {\n            const response = await fetch(url, {\n                ...options,\n                headers\n            });\n            const data = await response.json();\n            return data;\n        } catch (error) {\n            console.error('API request failed:', error);\n            return {\n                success: false,\n                error: 'Network error occurred'\n            };\n        }\n    }\n    async get(endpoint) {\n        return this.request(endpoint, {\n            method: 'GET'\n        });\n    }\n    async post(endpoint, data) {\n        return this.request(endpoint, {\n            method: 'POST',\n            body: data ? JSON.stringify(data) : undefined\n        });\n    }\n    async put(endpoint, data) {\n        return this.request(endpoint, {\n            method: 'PUT',\n            body: data ? JSON.stringify(data) : undefined\n        });\n    }\n    async delete(endpoint) {\n        return this.request(endpoint, {\n            method: 'DELETE'\n        });\n    }\n    // Auth methods\n    async loginWithTelegram(telegramData) {\n        return this.post('/auth/telegram', telegramData);\n    }\n    async loginMock() {\n        return this.post('/auth/telegram', {\n            mock: true\n        });\n    }\n    // User methods\n    async getUserProfile() {\n        return this.get('/user/profile');\n    }\n    // Mining methods\n    async startMining() {\n        return this.post('/mining/start');\n    }\n    async claimMining(sessionId) {\n        return this.post('/mining/claim', {\n            sessionId\n        });\n    }\n    // Subscription methods\n    async purchaseBasicPlan() {\n        return this.post('/subscription/basic-plan');\n    }\n    async purchaseSpeedUpgrade() {\n        return this.post('/subscription/speed-upgrade');\n    }\n    // Tasks methods\n    async getTasks() {\n        return this.get('/tasks');\n    }\n    async completeTask(taskId) {\n        return this.post('/tasks/complete', {\n            taskId\n        });\n    }\n    // Referrals methods\n    async getReferrals() {\n        return this.get('/referrals');\n    }\n    async createReferral(referredTelegramId) {\n        return this.post('/referrals', {\n            referredTelegramId\n        });\n    }\n}\nconst apiClient = new ApiClient(API_BASE_URL);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/api.ts\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FHehe-coin%2Fhehe-miner%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FHehe-coin%2Fhehe-miner&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();