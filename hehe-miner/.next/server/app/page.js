(()=>{var e={};e.id=974,e.ids=[974],e.modules={440:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});var s=r(1658);let n=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},512:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{default:function(){return h},defaultHead:function(){return u}});let s=r(4985),n=r(740),a=r(687),i=n._(r(3210)),l=s._(r(7755)),o=r(4959),c=r(9513),d=r(4604);function u(e){void 0===e&&(e=!1);let t=[(0,a.jsx)("meta",{charSet:"utf-8"},"charset")];return e||t.push((0,a.jsx)("meta",{name:"viewport",content:"width=device-width"},"viewport")),t}function m(e,t){return"string"==typeof t||"number"==typeof t?e:t.type===i.default.Fragment?e.concat(i.default.Children.toArray(t.props.children).reduce((e,t)=>"string"==typeof t||"number"==typeof t?e:e.concat(t),[])):e.concat(t)}r(148);let p=["name","httpEquiv","charSet","itemProp"];function f(e,t){let{inAmpMode:r}=t;return e.reduce(m,[]).reverse().concat(u(r).reverse()).filter(function(){let e=new Set,t=new Set,r=new Set,s={};return n=>{let a=!0,i=!1;if(n.key&&"number"!=typeof n.key&&n.key.indexOf("$")>0){i=!0;let t=n.key.slice(n.key.indexOf("$")+1);e.has(t)?a=!1:e.add(t)}switch(n.type){case"title":case"base":t.has(n.type)?a=!1:t.add(n.type);break;case"meta":for(let e=0,t=p.length;e<t;e++){let t=p[e];if(n.props.hasOwnProperty(t))if("charSet"===t)r.has(t)?a=!1:r.add(t);else{let e=n.props[t],r=s[t]||new Set;("name"!==t||!i)&&r.has(e)?a=!1:(r.add(e),s[t]=r)}}}return a}}()).reverse().map((e,t)=>{let s=e.key||t;if(process.env.__NEXT_OPTIMIZE_FONTS&&!r&&"link"===e.type&&e.props.href&&["https://fonts.googleapis.com/css","https://use.typekit.net/"].some(t=>e.props.href.startsWith(t))){let t={...e.props||{}};return t["data-href"]=t.href,t.href=void 0,t["data-optimized-fonts"]=!0,i.default.cloneElement(e,t)}return i.default.cloneElement(e,{key:s})})}let h=function(e){let{children:t}=e,r=(0,i.useContext)(o.AmpStateContext),s=(0,i.useContext)(c.HeadManagerContext);return(0,a.jsx)(l.default,{reduceComponentsToState:f,headManager:s,inAmpMode:(0,d.isInAmpMode)(r),children:t})};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},554:(e,t)=>{"use strict";function r(e){return e.endsWith("/route")}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isAppRouteRoute",{enumerable:!0,get:function(){return r}})},660:(e,t)=>{"use strict";function r(e){let t=5381;for(let r=0;r<e.length;r++)t=(t<<5)+t+e.charCodeAt(r)|0;return t>>>0}function s(e){return r(e).toString(36).slice(0,5)}Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{djb2Hash:function(){return r},hexHash:function(){return s}})},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},963:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>y});var s=r(687),n=r(3213),a=r(3210);class i{constructor(e){this.token=null,this.baseURL=e}setToken(e){this.token=e}async request(e,t={}){let r=`${this.baseURL}/api${e}`,s={"Content-Type":"application/json",...t.headers};this.token&&(s.Authorization=`Bearer ${this.token}`);try{let e=await fetch(r,{...t,headers:s});return await e.json()}catch(e){return console.error("API request failed:",e),{success:!1,error:"Network error occurred"}}}async get(e){return this.request(e,{method:"GET"})}async post(e,t){return this.request(e,{method:"POST",body:t?JSON.stringify(t):void 0})}async put(e,t){return this.request(e,{method:"PUT",body:t?JSON.stringify(t):void 0})}async delete(e){return this.request(e,{method:"DELETE"})}async loginWithTelegram(e){return this.post("/auth/telegram",e)}async loginMock(){return this.post("/auth/telegram",{mock:!0})}async getUserProfile(){return this.get("/user/profile")}async startMining(){return this.post("/mining/start")}async claimMining(e){return this.post("/mining/claim",{sessionId:e})}async purchaseBasicPlan(){return this.post("/subscription/basic-plan")}async purchaseSpeedUpgrade(){return this.post("/subscription/speed-upgrade")}async getTasks(){return this.get("/tasks")}async completeTask(e){return this.post("/tasks/complete",{taskId:e})}async getReferrals(){return this.get("/referrals")}async createReferral(e){return this.post("/referrals",{referredTelegramId:e})}}let l=new i("https://hehe-miner.vercel.app");var o=r(6180),c=r.n(o);r(5511);function d(e,t){}var u=r(1261),m=r.n(u);function p({onLaunchApp:e}){let{login:t}=(0,n.A)(),[r,i]=(0,a.useState)(!1),[o,d]=(0,a.useState)(""),[u,p]=(0,a.useState)("Initializing..."),f=async()=>{i(!0),d("");try{await new Promise(e=>setTimeout(e,1e3));let r=await l.loginMock();r.success?(t(r.user,r.token),e()):d(r.error||"Authentication failed")}catch(e){d("Network error occurred. Please try again.")}finally{i(!1)}};return r?(0,s.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 flex items-center justify-center relative overflow-hidden",children:[(0,s.jsxs)("div",{className:"absolute inset-0",children:[(0,s.jsx)("div",{className:"absolute top-20 left-20 w-32 h-32 bg-yellow-400 rounded-full opacity-20 animate-pulse"}),(0,s.jsx)("div",{className:"absolute top-40 right-32 w-24 h-24 bg-orange-400 rounded-full opacity-20 animate-pulse delay-300"}),(0,s.jsx)("div",{className:"absolute bottom-32 left-32 w-40 h-40 bg-yellow-300 rounded-full opacity-20 animate-pulse delay-700"}),(0,s.jsx)("div",{className:"absolute bottom-20 right-20 w-28 h-28 bg-orange-300 rounded-full opacity-20 animate-pulse delay-500"})]}),(0,s.jsxs)("div",{className:"text-center z-10",children:[(0,s.jsx)("div",{className:"mb-8 animate-bounce",children:(0,s.jsx)(m(),{src:"/hehe-logo.svg",alt:"Hehe Miner",width:120,height:120,className:"mx-auto drop-shadow-2xl"})}),(0,s.jsxs)("div",{className:"relative mb-6",children:[(0,s.jsx)("div",{className:"w-16 h-16 border-4 border-yellow-400 border-t-transparent rounded-full animate-spin mx-auto"}),(0,s.jsx)("div",{className:"absolute inset-0 w-16 h-16 border-4 border-orange-400 border-b-transparent rounded-full animate-spin mx-auto",style:{animationDirection:"reverse",animationDuration:"1.5s"}})]}),(0,s.jsx)("h2",{className:"text-2xl font-bold text-white mb-2 animate-pulse",children:u}),(0,s.jsx)("div",{className:"w-64 h-2 bg-gray-700 rounded-full mx-auto overflow-hidden",children:(0,s.jsx)("div",{className:"h-full bg-gradient-to-r from-yellow-400 to-orange-400 rounded-full animate-pulse"})}),(0,s.jsx)("p",{className:"text-gray-300 mt-4 text-sm",children:"Please wait while we prepare your mining experience..."})]})]}):(0,s.jsxs)("div",{className:"jsx-6bd0f39f565b36fe min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 flex items-center justify-center relative overflow-hidden",children:[(0,s.jsxs)("div",{className:"jsx-6bd0f39f565b36fe absolute inset-0",children:[(0,s.jsx)("div",{className:"jsx-6bd0f39f565b36fe absolute top-20 left-20 w-32 h-32 bg-yellow-400 rounded-full opacity-20 animate-float"}),(0,s.jsx)("div",{className:"jsx-6bd0f39f565b36fe absolute top-40 right-32 w-24 h-24 bg-orange-400 rounded-full opacity-20 animate-float delay-300"}),(0,s.jsx)("div",{className:"jsx-6bd0f39f565b36fe absolute bottom-32 left-32 w-40 h-40 bg-yellow-300 rounded-full opacity-20 animate-float delay-700"}),(0,s.jsx)("div",{className:"jsx-6bd0f39f565b36fe absolute bottom-20 right-20 w-28 h-28 bg-orange-300 rounded-full opacity-20 animate-float delay-500"})]}),(0,s.jsxs)("div",{className:"jsx-6bd0f39f565b36fe text-center z-10 max-w-md mx-auto px-6",children:[(0,s.jsx)("div",{className:"jsx-6bd0f39f565b36fe mb-8 animate-bounce",children:(0,s.jsx)(m(),{src:"/hehe-logo.svg",alt:"Hehe Miner",width:150,height:150,className:"mx-auto drop-shadow-2xl"})}),(0,s.jsx)("h1",{className:"jsx-6bd0f39f565b36fe text-5xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-yellow-400 to-orange-400 mb-4 animate-pulse",children:"HEHE MINER"}),(0,s.jsx)("p",{className:"jsx-6bd0f39f565b36fe text-xl text-gray-300 mb-8 leading-relaxed",children:"Start your crypto mining journey with the most fun and rewarding mining game!"}),(0,s.jsxs)("div",{className:"jsx-6bd0f39f565b36fe mb-8 space-y-3",children:[(0,s.jsxs)("div",{className:"jsx-6bd0f39f565b36fe flex items-center justify-center text-gray-300",children:[(0,s.jsx)("span",{className:"jsx-6bd0f39f565b36fe text-yellow-400 mr-2",children:"⚡"}),(0,s.jsx)("span",{className:"jsx-6bd0f39f565b36fe",children:"Mine HEHE tokens every 4 hours"})]}),(0,s.jsxs)("div",{className:"jsx-6bd0f39f565b36fe flex items-center justify-center text-gray-300",children:[(0,s.jsx)("span",{className:"jsx-6bd0f39f565b36fe text-yellow-400 mr-2",children:"\uD83D\uDE80"}),(0,s.jsx)("span",{className:"jsx-6bd0f39f565b36fe",children:"Upgrade your mining power"})]}),(0,s.jsxs)("div",{className:"jsx-6bd0f39f565b36fe flex items-center justify-center text-gray-300",children:[(0,s.jsx)("span",{className:"jsx-6bd0f39f565b36fe text-yellow-400 mr-2",children:"\uD83D\uDC65"}),(0,s.jsx)("span",{className:"jsx-6bd0f39f565b36fe",children:"Refer friends and earn rewards"})]})]}),(0,s.jsx)("button",{onClick:f,disabled:r,className:"jsx-6bd0f39f565b36fe w-full bg-gradient-to-r from-yellow-400 to-orange-400 text-black font-bold py-4 px-8 rounded-xl text-lg shadow-2xl transform transition-all duration-300 hover:scale-105 hover:shadow-yellow-400/50 disabled:opacity-50 disabled:cursor-not-allowed animate-pulse",children:"\uD83D\uDE80 Launch App"}),o&&(0,s.jsx)("div",{className:"jsx-6bd0f39f565b36fe mt-4 p-3 bg-red-500/20 border border-red-500 rounded-lg text-red-300 text-sm",children:o}),(0,s.jsx)("p",{className:"jsx-6bd0f39f565b36fe text-gray-400 text-xs mt-6",children:"Powered by Telegram • Secure • Decentralized"})]}),(0,s.jsx)(c(),{id:"6bd0f39f565b36fe",children:"@-webkit-keyframes float{0%,100%{-webkit-transform:translatey(0px)rotate(0deg);transform:translatey(0px)rotate(0deg)}50%{-webkit-transform:translatey(-20px)rotate(180deg);transform:translatey(-20px)rotate(180deg)}}@-moz-keyframes float{0%,100%{-moz-transform:translatey(0px)rotate(0deg);transform:translatey(0px)rotate(0deg)}50%{-moz-transform:translatey(-20px)rotate(180deg);transform:translatey(-20px)rotate(180deg)}}@-o-keyframes float{0%,100%{-o-transform:translatey(0px)rotate(0deg);transform:translatey(0px)rotate(0deg)}50%{-o-transform:translatey(-20px)rotate(180deg);transform:translatey(-20px)rotate(180deg)}}@keyframes float{0%,100%{-webkit-transform:translatey(0px)rotate(0deg);-moz-transform:translatey(0px)rotate(0deg);-o-transform:translatey(0px)rotate(0deg);transform:translatey(0px)rotate(0deg)}50%{-webkit-transform:translatey(-20px)rotate(180deg);-moz-transform:translatey(-20px)rotate(180deg);-o-transform:translatey(-20px)rotate(180deg);transform:translatey(-20px)rotate(180deg)}}.animate-float.jsx-6bd0f39f565b36fe{-webkit-animation:float 6s ease-in-out infinite;-moz-animation:float 6s ease-in-out infinite;-o-animation:float 6s ease-in-out infinite;animation:float 6s ease-in-out infinite}"})]})}function f(){let{user:e,updateUser:t}=(0,n.A)(),[r,i]=(0,a.useState)(!1),[o,c]=(0,a.useState)(0),[d,u]=(0,a.useState)(!1),[p,f]=(0,a.useState)(""),[h,x]=(0,a.useState)(""),g=async()=>{if(!e?.hasBasicPlan)return void f("You need to purchase the basic plan first!");i(!0),f(""),x("");try{let e=await l.startMining();if(e.success){x("Mining started successfully!");let e=await l.getUserProfile();e.success&&t(e.user)}else f(e.error||"Failed to start mining")}catch(e){f("Network error occurred")}finally{i(!1)}},b=async()=>{if(e?.currentMiningSession){i(!0),f(""),x("");try{let r=await l.claimMining(e.currentMiningSession.id);if(r.success){x(`Claimed ${r.tokensEarned} HEHE tokens!`);let e=await l.getUserProfile();e.success&&t(e.user)}else f(r.error||"Failed to claim tokens")}catch(e){f("Network error occurred")}finally{i(!1)}}},v=()=>{if(!e?.currentMiningSession||o<=0)return 100;let t=new Date(e.currentMiningSession.startTime).getTime();return Math.min((Date.now()-t)/144e5*100,100)};return(0,s.jsxs)("div",{className:"p-6 max-w-md mx-auto relative",children:[p&&(0,s.jsx)("div",{className:"bg-red-500/20 border border-red-400/30 rounded-xl p-4 mb-6 glass animate-bounce-in",children:(0,s.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,s.jsx)("span",{className:"text-red-400 text-xl animate-bounce",children:"⚠️"}),(0,s.jsx)("p",{className:"text-red-300 font-medium",children:p})]})}),h&&(0,s.jsx)("div",{className:"bg-green-500/20 border border-green-400/30 rounded-xl p-4 mb-6 glass animate-bounce-in",children:(0,s.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,s.jsx)("span",{className:"text-green-400 text-xl animate-bounce",children:"\uD83C\uDF89"}),(0,s.jsx)("p",{className:"text-green-300 font-medium",children:h})]})}),(0,s.jsxs)("div",{className:"glass-dark rounded-3xl p-8 mb-8 border border-white/20 hover-lift animate-slide-up relative overflow-hidden",children:[(0,s.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-yellow-400/10 via-orange-500/10 to-red-500/10 animate-pulse"}),(0,s.jsxs)("div",{className:"text-center relative z-10",children:[(0,s.jsxs)("div",{className:"relative mb-6",children:[(0,s.jsxs)("div",{className:"w-32 h-32 bg-gradient-to-r from-yellow-400 via-orange-500 to-red-500 rounded-full mx-auto flex items-center justify-center animate-pulse-glow relative",children:[(0,s.jsx)(m(),{src:"/hehe-logo.svg",alt:"Hehe Miner",width:80,height:80,className:"animate-float"}),e?.currentMiningSession&&o>0&&(0,s.jsx)("div",{className:"absolute inset-0 rounded-full border-4 border-transparent border-t-white animate-spin"})]}),d&&(0,s.jsx)("div",{className:"absolute -top-2 -right-2 w-8 h-8 bg-gradient-to-r from-green-400 to-blue-500 rounded-full animate-ping"})]}),(0,s.jsx)("h2",{className:"text-3xl font-bold bg-gradient-to-r from-yellow-400 via-orange-500 to-red-500 bg-clip-text text-transparent mb-4",children:"Mining Status"}),e?.hasBasicPlan?e?.currentMiningSession&&o>0?(0,s.jsxs)("div",{className:"animate-slide-up",children:[(0,s.jsx)("p",{className:"text-gray-300 mb-6 text-lg font-medium",children:"⚡ Mining in progress... ⚡"}),(0,s.jsxs)("div",{className:"mb-6",children:[(0,s.jsx)("div",{className:"bg-gray-700/50 rounded-full h-4 mb-3 overflow-hidden",children:(0,s.jsx)("div",{className:"bg-gradient-to-r from-yellow-400 via-orange-500 to-red-500 h-4 rounded-full transition-all duration-1000 relative",style:{width:`${v()}%`},children:(0,s.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent shimmer"})})}),(0,s.jsxs)("p",{className:"text-white font-semibold",children:["Progress: ",v().toFixed(1),"% ⚡"]})]}),(0,s.jsxs)("div",{className:"bg-gray-800/80 backdrop-blur-sm rounded-2xl p-6 mb-4 border border-yellow-400/30",children:[(0,s.jsx)("div",{className:"text-4xl font-mono text-yellow-400 mb-3 animate-pulse font-bold",children:(e=>{let t=Math.floor(e/36e5),r=Math.floor(e%36e5/6e4),s=Math.floor(e%6e4/1e3);return`${t.toString().padStart(2,"0")}:${r.toString().padStart(2,"0")}:${s.toString().padStart(2,"0")}`})(o)}),(0,s.jsx)("p",{className:"text-white text-lg font-medium",children:"Time remaining ⏰"})]}),(0,s.jsxs)("div",{className:"flex items-center justify-center space-x-2 text-white",children:[(0,s.jsx)("span",{className:"text-2xl animate-coin-flip",children:"\uD83D\uDCB0"}),(0,s.jsxs)("span",{className:"text-lg font-bold",children:["Earning: ",e.currentMiningSession.tokensEarned," HEHE"]}),(0,s.jsx)("span",{className:"text-2xl animate-coin-flip",style:{animationDelay:"0.5s"},children:"\uD83D\uDCB0"})]})]}):d&&e?.currentMiningSession?(0,s.jsxs)("div",{className:"animate-bounce-in",children:[(0,s.jsxs)("div",{className:"mb-6",children:[(0,s.jsx)("p",{className:"text-2xl font-bold bg-gradient-to-r from-green-400 to-blue-500 bg-clip-text text-transparent mb-4",children:"\uD83C\uDF89 Mining Complete! \uD83C\uDF89"}),(0,s.jsxs)("div",{className:"bg-gray-800/90 backdrop-blur-sm rounded-2xl p-6 mb-6 border border-green-400/30",children:[(0,s.jsxs)("div",{className:"flex items-center justify-center space-x-3 mb-3",children:[(0,s.jsx)("span",{className:"text-3xl animate-bounce",children:"\uD83D\uDC8E"}),(0,s.jsxs)("span",{className:"text-2xl font-bold text-white",children:[e.currentMiningSession.tokensEarned," HEHE"]}),(0,s.jsx)("span",{className:"text-3xl animate-bounce",style:{animationDelay:"0.3s"},children:"\uD83D\uDC8E"})]}),(0,s.jsx)("p",{className:"text-white",children:"Ready to claim your rewards!"})]})]}),(0,s.jsxs)("button",{onClick:b,disabled:r,className:"group w-full bg-gradient-to-r from-green-500 via-green-600 to-blue-600 hover:from-green-600 hover:via-green-700 hover:to-blue-700 disabled:from-gray-600 disabled:via-gray-700 disabled:to-gray-800 text-white font-bold py-4 px-6 rounded-xl transition-all duration-300 transform hover:scale-105 disabled:transform-none shadow-lg hover:shadow-green-500/25 relative overflow-hidden",children:[(0,s.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 shimmer"}),(0,s.jsx)("span",{className:"relative z-10 text-lg",children:r?(0,s.jsxs)("div",{className:"flex items-center justify-center space-x-2",children:[(0,s.jsx)("div",{className:"animate-spin rounded-full h-5 w-5 border-2 border-white border-t-transparent"}),(0,s.jsx)("span",{children:"Claiming..."})]}):(0,s.jsxs)("div",{className:"flex items-center justify-center space-x-2",children:[(0,s.jsx)("span",{className:"group-hover:animate-bounce",children:"\uD83D\uDCB0"}),(0,s.jsx)("span",{children:"Claim Tokens"}),(0,s.jsx)("span",{className:"group-hover:animate-bounce",children:"\uD83D\uDCB0"})]})})]})]}):(0,s.jsxs)("div",{className:"animate-slide-up",children:[(0,s.jsx)("p",{className:"text-white mb-6 text-lg font-medium",children:"\uD83D\uDE80 Ready to mine! \uD83D\uDE80"}),(0,s.jsxs)("div",{className:"bg-gray-800/90 backdrop-blur-sm rounded-2xl p-6 mb-6 border border-blue-400/30",children:[(0,s.jsxs)("div",{className:"flex items-center justify-center space-x-3 mb-3",children:[(0,s.jsx)("span",{className:"text-2xl animate-pulse",children:"⚡"}),(0,s.jsxs)("span",{className:"text-lg font-bold text-white",children:["Mining Power: ",e?.miningPower," HEHE/4h"]}),(0,s.jsx)("span",{className:"text-2xl animate-pulse",style:{animationDelay:"0.5s"},children:"⚡"})]}),(0,s.jsx)("p",{className:"text-white",children:"Start your mining session now!"})]}),(0,s.jsxs)("button",{onClick:g,disabled:r,className:"group w-full bg-gradient-to-r from-yellow-500 via-orange-500 to-red-500 hover:from-yellow-600 hover:via-orange-600 hover:to-red-600 disabled:from-gray-600 disabled:via-gray-700 disabled:to-gray-800 text-white font-bold py-4 px-6 rounded-xl transition-all duration-300 transform hover:scale-105 disabled:transform-none shadow-lg hover:shadow-yellow-500/25 relative overflow-hidden",children:[(0,s.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 shimmer"}),(0,s.jsx)("span",{className:"relative z-10 text-lg",children:r?(0,s.jsxs)("div",{className:"flex items-center justify-center space-x-2",children:[(0,s.jsx)("div",{className:"animate-spin rounded-full h-5 w-5 border-2 border-white border-t-transparent"}),(0,s.jsx)("span",{children:"Starting..."})]}):(0,s.jsxs)("div",{className:"flex items-center justify-center space-x-2",children:[(0,s.jsx)("span",{className:"group-hover:animate-bounce",children:"⛏️"}),(0,s.jsx)("span",{children:"Start Mining"}),(0,s.jsx)("span",{className:"group-hover:animate-bounce",children:"⛏️"})]})})]})]}):(0,s.jsxs)("div",{className:"text-center animate-slide-up",children:[(0,s.jsx)("p",{className:"text-gray-300 mb-6 text-lg",children:"Purchase the basic plan to start mining! \uD83D\uDE80"}),(0,s.jsxs)("div",{className:"glass rounded-2xl p-6 border border-yellow-400/30",children:[(0,s.jsxs)("div",{className:"flex items-center justify-center space-x-3 mb-4",children:[(0,s.jsx)("span",{className:"text-3xl animate-bounce",children:"\uD83D\uDCA1"}),(0,s.jsx)("span",{className:"text-2xl animate-bounce",style:{animationDelay:"0.2s"},children:"⛏️"}),(0,s.jsx)("span",{className:"text-3xl animate-bounce",style:{animationDelay:"0.4s"},children:"\uD83D\uDC8E"})]}),(0,s.jsx)("p",{className:"text-yellow-300 font-bold text-lg mb-2",children:"Basic Plan: $1"}),(0,s.jsx)("p",{className:"text-gray-300",children:"Unlock mining 4 HEHE tokens every 4 hours"})]})]})]})]}),(0,s.jsxs)("div",{className:"grid grid-cols-2 gap-4 animate-slide-up",style:{animationDelay:"0.3s"},children:[(0,s.jsxs)("div",{className:"bg-gray-800/90 backdrop-blur-sm rounded-2xl p-6 border border-yellow-400/30 hover-lift group",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-2 mb-2",children:[(0,s.jsx)("span",{className:"text-yellow-400 text-xl group-hover:animate-coin-flip",children:"\uD83D\uDCB0"}),(0,s.jsx)("p",{className:"text-white font-medium",children:"Total Balance"})]}),(0,s.jsxs)("p",{className:"text-2xl font-bold text-yellow-400",children:[e?.totalBalance?.toFixed(2)||"0.00"," HEHE"]})]}),(0,s.jsxs)("div",{className:"bg-gray-800/90 backdrop-blur-sm rounded-2xl p-6 border border-blue-400/30 hover-lift group",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-2 mb-2",children:[(0,s.jsx)("span",{className:"text-blue-400 text-xl group-hover:animate-pulse",children:"⚡"}),(0,s.jsx)("p",{className:"text-white font-medium",children:"Mining Power"})]}),(0,s.jsxs)("p",{className:"text-2xl font-bold text-blue-400",children:[e?.miningPower?.toFixed(2)||"4.00","/4h"]})]})]})]})}function h(){let{updateUser:e}=(0,n.A)(),[t,r]=(0,a.useState)([]),[i,o]=(0,a.useState)(!0),[c,d]=(0,a.useState)(null),[u,m]=(0,a.useState)(""),[p,f]=(0,a.useState)(""),h=async t=>{t.link&&window.open(t.link,"_blank"),d(t.id),m(""),f("");try{let s=await l.completeTask(t.id);s.success?(f(`Task completed! Earned ${s.reward} HEHE tokens`),r(e=>e.map(e=>e.id===t.id?{...e,isCompleted:!0,isRewardClaimed:!0}:e)),e({totalBalance:s.newBalance})):m(s.error||"Failed to complete task")}catch(e){m("Network error occurred")}finally{d(null)}};return i?(0,s.jsx)("div",{className:"p-6 max-w-md mx-auto",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-yellow-400 mx-auto mb-4"}),(0,s.jsx)("p",{className:"text-gray-400",children:"Loading tasks..."})]})}):(0,s.jsxs)("div",{className:"p-6 max-w-md mx-auto",children:[(0,s.jsxs)("div",{className:"text-center mb-8 animate-slide-up",children:[(0,s.jsxs)("div",{className:"flex items-center justify-center space-x-3 mb-4",children:[(0,s.jsx)("span",{className:"text-3xl animate-bounce",children:"\uD83D\uDCCB"}),(0,s.jsx)("h2",{className:"text-3xl font-bold bg-gradient-to-r from-blue-400 via-purple-500 to-pink-500 bg-clip-text text-transparent",children:"Tasks"}),(0,s.jsx)("span",{className:"text-3xl animate-bounce",style:{animationDelay:"0.3s"},children:"✨"})]}),(0,s.jsx)("p",{className:"text-gray-300 text-lg",children:"Complete tasks to earn bonus HEHE tokens! \uD83C\uDFAF"})]}),u&&(0,s.jsx)("div",{className:"bg-red-500/20 border border-red-400/30 rounded-xl p-4 mb-6 glass animate-bounce-in",children:(0,s.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,s.jsx)("span",{className:"text-red-400 text-xl animate-bounce",children:"⚠️"}),(0,s.jsx)("p",{className:"text-red-300 font-medium",children:u})]})}),p&&(0,s.jsx)("div",{className:"bg-green-500/20 border border-green-400/30 rounded-xl p-4 mb-6 glass animate-bounce-in",children:(0,s.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,s.jsx)("span",{className:"text-green-400 text-xl animate-bounce",children:"\uD83C\uDF89"}),(0,s.jsx)("p",{className:"text-green-300 font-medium",children:p})]})}),(0,s.jsx)("div",{className:"space-y-5 animate-slide-up",style:{animationDelay:"0.2s"},children:0===t.length?(0,s.jsxs)("div",{className:"text-center py-12 glass-dark rounded-2xl border border-white/20",children:[(0,s.jsx)("span",{className:"text-6xl mb-4 block animate-float",children:"\uD83D\uDCCB"}),(0,s.jsx)("p",{className:"text-gray-300 text-lg",children:"No tasks available at the moment"}),(0,s.jsx)("p",{className:"text-gray-400 text-sm mt-2",children:"Check back soon for new opportunities! ✨"})]}):t.map((e,t)=>(0,s.jsxs)("div",{className:`glass-dark rounded-2xl p-6 border hover-lift transition-all duration-300 animate-slide-up ${e.isCompleted?"border-green-400/40 bg-green-500/10":"border-white/20 hover:border-blue-400/40"}`,style:{animationDelay:`${.1*t}s`},children:[(0,s.jsxs)("div",{className:"flex items-start justify-between mb-4",children:[(0,s.jsxs)("div",{className:"flex-1",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-2 mb-2",children:[(0,s.jsx)("h3",{className:"text-white font-bold text-lg",children:e.title}),e.isCompleted&&(0,s.jsx)("span",{className:"text-green-400 text-xl animate-bounce",children:"✅"})]}),(0,s.jsx)("p",{className:"text-gray-300 mb-4 leading-relaxed",children:e.description}),(0,s.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-2 glass rounded-lg px-3 py-2",children:[(0,s.jsx)("span",{className:"text-yellow-400 text-lg animate-coin-flip",children:"\uD83D\uDCB0"}),(0,s.jsxs)("span",{className:"text-yellow-300 font-bold",children:["+",e.reward," HEHE"]})]}),e.isCompleted&&(0,s.jsxs)("div",{className:"flex items-center space-x-2 glass rounded-lg px-3 py-2 bg-green-500/20",children:[(0,s.jsx)("span",{className:"text-green-400 animate-pulse",children:"✓"}),(0,s.jsx)("span",{className:"text-green-300 font-medium",children:"Completed"})]})]})]}),(0,s.jsx)("div",{className:"ml-4",children:e.isCompleted?(0,s.jsx)("div",{className:"w-14 h-14 bg-gradient-to-r from-green-400 to-blue-500 rounded-full flex items-center justify-center animate-pulse-glow",children:(0,s.jsx)("span",{className:"text-white text-xl animate-bounce",children:"✓"})}):(0,s.jsxs)("button",{onClick:()=>h(e),disabled:c===e.id,className:"group bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500 hover:from-blue-600 hover:via-purple-600 hover:to-pink-600 disabled:from-gray-600 disabled:via-gray-700 disabled:to-gray-800 text-white px-6 py-3 rounded-xl font-bold transition-all duration-300 transform hover:scale-105 disabled:transform-none shadow-lg hover:shadow-blue-500/25 relative overflow-hidden",children:[(0,s.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 shimmer"}),(0,s.jsx)("span",{className:"relative z-10",children:c===e.id?(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent"}),(0,s.jsx)("span",{children:"Doing..."})]}):(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)("span",{className:"group-hover:animate-bounce",children:"\uD83D\uDE80"}),(0,s.jsx)("span",{children:"Do Task"})]})})]})})]}),e.link&&!e.isCompleted&&(0,s.jsx)("div",{className:"glass rounded-lg p-3 mt-4 border border-blue-400/30",children:(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)("span",{className:"text-blue-400 animate-pulse",children:"\uD83D\uDCA1"}),(0,s.jsx)("p",{className:"text-blue-300 text-sm font-medium",children:'Clicking "Do Task" will open the link and mark the task as complete'})]})})]},e.id))}),(0,s.jsxs)("div",{className:"mt-8 glass-dark rounded-2xl p-6 border border-white/20 animate-slide-up",style:{animationDelay:"0.4s"},children:[(0,s.jsxs)("div",{className:"flex items-center justify-center space-x-2 mb-6",children:[(0,s.jsx)("span",{className:"text-2xl animate-bounce",children:"\uD83D\uDCCA"}),(0,s.jsx)("h3",{className:"text-xl font-bold bg-gradient-to-r from-blue-400 to-purple-500 bg-clip-text text-transparent",children:"Task Statistics"}),(0,s.jsx)("span",{className:"text-2xl animate-bounce",style:{animationDelay:"0.3s"},children:"\uD83D\uDCC8"})]}),(0,s.jsxs)("div",{className:"grid grid-cols-2 gap-6",children:[(0,s.jsxs)("div",{className:"text-center glass rounded-xl p-4 hover-lift",children:[(0,s.jsxs)("div",{className:"flex items-center justify-center space-x-2 mb-2",children:[(0,s.jsx)("span",{className:"text-green-400 text-xl animate-pulse",children:"✅"}),(0,s.jsx)("p",{className:"text-gray-300 font-medium",children:"Completed"})]}),(0,s.jsx)("p",{className:"text-3xl font-bold bg-gradient-to-r from-green-400 to-blue-500 bg-clip-text text-transparent",children:t.filter(e=>e.isCompleted).length})]}),(0,s.jsxs)("div",{className:"text-center glass rounded-xl p-4 hover-lift",children:[(0,s.jsxs)("div",{className:"flex items-center justify-center space-x-2 mb-2",children:[(0,s.jsx)("span",{className:"text-blue-400 text-xl animate-pulse",children:"\uD83C\uDFAF"}),(0,s.jsx)("p",{className:"text-gray-300 font-medium",children:"Available"})]}),(0,s.jsx)("p",{className:"text-3xl font-bold bg-gradient-to-r from-blue-400 to-purple-500 bg-clip-text text-transparent",children:t.filter(e=>!e.isCompleted).length})]})]}),(0,s.jsxs)("div",{className:"mt-6",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,s.jsx)("span",{className:"text-gray-300 font-medium",children:"Progress"}),(0,s.jsxs)("span",{className:"text-gray-300 font-medium",children:[t.length>0?Math.round(t.filter(e=>e.isCompleted).length/t.length*100):0,"%"]})]}),(0,s.jsx)("div",{className:"bg-gray-700/50 rounded-full h-3 overflow-hidden",children:(0,s.jsx)("div",{className:"bg-gradient-to-r from-blue-400 via-purple-500 to-pink-500 h-3 rounded-full transition-all duration-1000 relative",style:{width:`${t.length>0?t.filter(e=>e.isCompleted).length/t.length*100:0}%`},children:(0,s.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent shimmer"})})})]})]})]})}function x(){let{user:e}=(0,n.A)(),[t,r]=(0,a.useState)([]),[i,l]=(0,a.useState)({totalReferrals:0,totalRewards:0}),[o,c]=(0,a.useState)(""),[d,u]=(0,a.useState)(!0),[m,p]=(0,a.useState)(""),[f,h]=(0,a.useState)(!1),x=async()=>{try{await navigator.clipboard.writeText(o),h(!0),setTimeout(()=>h(!1),2e3)}catch(t){let e=document.createElement("textarea");e.value=o,document.body.appendChild(e),e.select(),document.execCommand("copy"),document.body.removeChild(e),h(!0),setTimeout(()=>h(!1),2e3)}},g=e=>new Date(e).toLocaleDateString("en-US",{month:"short",day:"numeric",year:"numeric"});return d?(0,s.jsx)("div",{className:"p-6 max-w-md mx-auto",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-yellow-400 mx-auto mb-4"}),(0,s.jsx)("p",{className:"text-gray-400",children:"Loading referrals..."})]})}):(0,s.jsxs)("div",{className:"p-6 max-w-md mx-auto",children:[(0,s.jsxs)("div",{className:"text-center mb-6",children:[(0,s.jsx)("h2",{className:"text-2xl font-bold text-white mb-2",children:"Referrals"}),(0,s.jsx)("p",{className:"text-gray-400",children:"Invite friends and earn 0.5 HEHE per referral"})]}),m&&(0,s.jsx)("div",{className:"bg-red-500/10 border border-red-500/20 rounded-lg p-3 mb-4",children:(0,s.jsx)("p",{className:"text-red-400 text-sm",children:m})}),(0,s.jsxs)("div",{className:"bg-gray-800 rounded-lg p-4 border border-gray-700 mb-6",children:[(0,s.jsx)("h3",{className:"text-white font-semibold mb-3",children:"Your Referral Link"}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)("input",{type:"text",value:o,readOnly:!0,className:"flex-1 bg-gray-700 text-white px-3 py-2 rounded border border-gray-600 text-sm"}),(0,s.jsx)("button",{onClick:x,className:`px-4 py-2 rounded text-sm font-medium transition-colors ${f?"bg-green-600 text-white":"bg-blue-600 hover:bg-blue-700 text-white"}`,children:f?"Copied!":"Copy"})]}),(0,s.jsx)("p",{className:"text-gray-400 text-xs mt-2",children:"Share this link with friends to earn 0.5 HEHE tokens for each signup"})]}),(0,s.jsxs)("div",{className:"grid grid-cols-2 gap-4 mb-6",children:[(0,s.jsxs)("div",{className:"bg-gray-800 rounded-lg p-4 border border-gray-700 text-center",children:[(0,s.jsx)("p",{className:"text-gray-400 text-sm",children:"Total Referrals"}),(0,s.jsx)("p",{className:"text-white text-2xl font-bold",children:i.totalReferrals})]}),(0,s.jsxs)("div",{className:"bg-gray-800 rounded-lg p-4 border border-gray-700 text-center",children:[(0,s.jsx)("p",{className:"text-gray-400 text-sm",children:"Total Earned"}),(0,s.jsx)("p",{className:"text-yellow-400 text-2xl font-bold",children:i.totalRewards.toFixed(1)})]})]}),(0,s.jsxs)("div",{className:"bg-gray-800 rounded-lg border border-gray-700",children:[(0,s.jsx)("div",{className:"p-4 border-b border-gray-700",children:(0,s.jsx)("h3",{className:"text-white font-semibold",children:"Recent Referrals"})}),(0,s.jsx)("div",{className:"max-h-64 overflow-y-auto",children:0===t.length?(0,s.jsxs)("div",{className:"p-4 text-center",children:[(0,s.jsx)("p",{className:"text-gray-400",children:"No referrals yet"}),(0,s.jsx)("p",{className:"text-gray-500 text-sm mt-1",children:"Start sharing your referral link to earn rewards!"})]}):(0,s.jsx)("div",{className:"divide-y divide-gray-700",children:t.map(e=>(0,s.jsxs)("div",{className:"p-4 flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex-1",children:[(0,s.jsxs)("p",{className:"text-white font-medium",children:[e.referred.firstName," ",e.referred.lastName,e.referred.username&&(0,s.jsxs)("span",{className:"text-gray-400 ml-1",children:["(@",e.referred.username,")"]})]}),(0,s.jsxs)("p",{className:"text-gray-400 text-sm",children:["Joined ",g(e.referred.joinedAt)]})]}),(0,s.jsxs)("div",{className:"text-right",children:[(0,s.jsxs)("p",{className:"text-yellow-400 font-semibold",children:["+",e.reward," HEHE"]}),(0,s.jsx)("p",{className:"text-gray-500 text-xs",children:g(e.createdAt)})]})]},e.id))})})]}),(0,s.jsxs)("div",{className:"mt-6 bg-blue-500/10 border border-blue-500/20 rounded-lg p-4",children:[(0,s.jsx)("h4",{className:"text-blue-400 font-semibold mb-2",children:"How Referrals Work"}),(0,s.jsxs)("ul",{className:"text-blue-300 text-sm space-y-1",children:[(0,s.jsx)("li",{children:"• Share your unique referral link"}),(0,s.jsx)("li",{children:"• When someone signs up using your link, you both benefit"}),(0,s.jsx)("li",{children:"• You earn 0.5 HEHE tokens for each successful referral"}),(0,s.jsx)("li",{children:"• No limit on the number of referrals you can make"})]})]})]})}function g(){let{user:e}=(0,n.A)();return(0,s.jsxs)("div",{className:"p-6 max-w-md mx-auto",children:[(0,s.jsxs)("div",{className:"text-center mb-6",children:[(0,s.jsx)("h2",{className:"text-2xl font-bold text-white mb-2",children:"Airdrop"}),(0,s.jsx)("p",{className:"text-gray-400",children:"Your HEHE token balance and airdrop status"})]}),(0,s.jsxs)("div",{className:"bg-gradient-to-r from-purple-600 to-blue-600 rounded-2xl p-6 mb-6 text-center",children:[(0,s.jsxs)("div",{className:"mb-4",children:[(0,s.jsx)("div",{className:"w-16 h-16 bg-white/20 rounded-full mx-auto flex items-center justify-center mb-3",children:(0,s.jsx)("span",{className:"text-2xl",children:"\uD83E\uDE82"})}),(0,s.jsx)("h3",{className:"text-white text-lg font-semibold mb-1",children:"Total Balance"}),(0,s.jsx)("p",{className:"text-white/80 text-sm",children:"Your accumulated HEHE tokens"})]}),(0,s.jsx)("div",{className:"text-4xl font-bold text-white mb-2",children:e?.totalBalance?.toFixed(2)||"0.00"}),(0,s.jsx)("p",{className:"text-white/80 text-lg",children:"HEHE Tokens"})]}),(0,s.jsx)("div",{className:"bg-gray-800 rounded-lg border border-gray-700 p-6 mb-6",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"w-12 h-12 bg-yellow-500/20 rounded-full mx-auto flex items-center justify-center mb-3",children:(0,s.jsx)("span",{className:"text-xl",children:"⏰"})}),(0,s.jsx)("h3",{className:"text-white text-lg font-semibold mb-2",children:"Airdrop Status"}),(0,s.jsx)("div",{className:"bg-yellow-500/10 border border-yellow-500/20 rounded-lg p-4 mb-4",children:(0,s.jsx)("p",{className:"text-yellow-400 font-semibold text-lg",children:"Coming Soon"})}),(0,s.jsx)("p",{className:"text-gray-400 text-sm",children:"The HEHE token airdrop is currently in development. Keep mining and completing tasks to maximize your token balance!"})]})}),(0,s.jsxs)("div",{className:"bg-gray-800 rounded-lg border border-gray-700 p-4 mb-6",children:[(0,s.jsx)("h3",{className:"text-white font-semibold mb-4",children:"Token Breakdown"}),(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsxs)("div",{className:"flex justify-between items-center",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)("span",{className:"text-lg",children:"⛏️"}),(0,s.jsx)("span",{className:"text-gray-300",children:"Mining Rewards"})]}),(0,s.jsx)("span",{className:"text-white font-medium",children:(e?.totalBalance||0)>0?"Active":"Start Mining"})]}),(0,s.jsxs)("div",{className:"flex justify-between items-center",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)("span",{className:"text-lg",children:"\uD83D\uDCCB"}),(0,s.jsx)("span",{className:"text-gray-300",children:"Task Rewards"})]}),(0,s.jsx)("span",{className:"text-white font-medium",children:"Available"})]}),(0,s.jsxs)("div",{className:"flex justify-between items-center",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)("span",{className:"text-lg",children:"\uD83D\uDC65"}),(0,s.jsx)("span",{className:"text-gray-300",children:"Referral Rewards"})]}),(0,s.jsx)("span",{className:"text-white font-medium",children:"0.5 HEHE each"})]})]})]}),(0,s.jsxs)("div",{className:"bg-blue-500/10 border border-blue-500/20 rounded-lg p-4",children:[(0,s.jsx)("h4",{className:"text-blue-400 font-semibold mb-3",children:"Airdrop Information"}),(0,s.jsxs)("div",{className:"space-y-2 text-blue-300 text-sm",children:[(0,s.jsxs)("div",{className:"flex items-start space-x-2",children:[(0,s.jsx)("span",{className:"text-blue-400 mt-0.5",children:"•"}),(0,s.jsx)("span",{children:"All HEHE tokens earned through mining, tasks, and referrals will be eligible for the airdrop"})]}),(0,s.jsxs)("div",{className:"flex items-start space-x-2",children:[(0,s.jsx)("span",{className:"text-blue-400 mt-0.5",children:"•"}),(0,s.jsx)("span",{children:"The airdrop will convert your HEHE tokens to the official HEHE cryptocurrency"})]}),(0,s.jsxs)("div",{className:"flex items-start space-x-2",children:[(0,s.jsx)("span",{className:"text-blue-400 mt-0.5",children:"•"}),(0,s.jsx)("span",{children:"Keep accumulating tokens to maximize your airdrop allocation"})]}),(0,s.jsxs)("div",{className:"flex items-start space-x-2",children:[(0,s.jsx)("span",{className:"text-blue-400 mt-0.5",children:"•"}),(0,s.jsx)("span",{children:"Follow our social media channels for airdrop announcements"})]})]})]}),(0,s.jsxs)("div",{className:"mt-6 text-center",children:[(0,s.jsx)("p",{className:"text-gray-400 text-sm mb-3",children:"Want to increase your airdrop allocation?"}),(0,s.jsxs)("div",{className:"grid grid-cols-2 gap-3",children:[(0,s.jsxs)("div",{className:"bg-gray-800 rounded-lg p-3 border border-gray-700",children:[(0,s.jsx)("p",{className:"text-white font-medium text-sm",children:"Keep Mining"}),(0,s.jsx)("p",{className:"text-gray-400 text-xs",children:"Earn 4+ HEHE every 4 hours"})]}),(0,s.jsxs)("div",{className:"bg-gray-800 rounded-lg p-3 border border-gray-700",children:[(0,s.jsx)("p",{className:"text-white font-medium text-sm",children:"Complete Tasks"}),(0,s.jsx)("p",{className:"text-gray-400 text-xs",children:"Bonus tokens available"})]})]})]})]})}function b(){let{user:e,updateUser:t}=(0,n.A)(),[r,i]=(0,a.useState)(!1),[o,c]=(0,a.useState)(null),[d,u]=(0,a.useState)(""),[m,p]=(0,a.useState)(""),f=async()=>{c("basic"),i(!0),u(""),p("");try{let e=await l.purchaseBasicPlan();e.success?(p("Basic plan purchased successfully! You can now start mining."),t({hasBasicPlan:!0})):u(e.error||"Failed to purchase basic plan")}catch(e){u("Network error occurred")}finally{i(!1),c(null)}},h=async()=>{c("speed"),i(!0),u(""),p("");try{let e=await l.purchaseSpeedUpgrade();e.success?(p("Speed upgrade purchased! Your mining power has increased."),t({miningPower:e.user.miningPower,speedUpgrades:e.user.speedUpgrades})):u(e.error||"Failed to purchase speed upgrade")}catch(e){u("Network error occurred")}finally{i(!1),c(null)}},x=e?.currentMiningSession&&e?.canMine===!1,g=x||r&&"speed"===o;return(0,s.jsxs)("div",{className:"p-6 max-w-md mx-auto",children:[(0,s.jsxs)("div",{className:"text-center mb-6",children:[(0,s.jsx)("h2",{className:"text-2xl font-bold text-white mb-2",children:"Upgrades"}),(0,s.jsx)("p",{className:"text-gray-400",children:"Enhance your mining capabilities"})]}),d&&(0,s.jsx)("div",{className:"bg-red-500/10 border border-red-500/20 rounded-lg p-3 mb-4",children:(0,s.jsx)("p",{className:"text-red-400 text-sm",children:d})}),m&&(0,s.jsx)("div",{className:"bg-green-500/10 border border-green-500/20 rounded-lg p-3 mb-4",children:(0,s.jsx)("p",{className:"text-green-400 text-sm",children:m})}),(0,s.jsxs)("div",{className:"bg-gray-800 rounded-lg border border-gray-700 p-4 mb-6",children:[(0,s.jsx)("h3",{className:"text-white font-semibold mb-3",children:"Current Status"}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsxs)("div",{className:"flex justify-between items-center",children:[(0,s.jsx)("span",{className:"text-gray-300",children:"Plan Status"}),(0,s.jsx)("span",{className:`font-medium ${e?.hasBasicPlan?"text-green-400":"text-red-400"}`,children:e?.hasBasicPlan?"Basic Plan Active":"No Plan"})]}),(0,s.jsxs)("div",{className:"flex justify-between items-center",children:[(0,s.jsx)("span",{className:"text-gray-300",children:"Mining Power"}),(0,s.jsxs)("span",{className:"text-white font-medium",children:[e?.miningPower?.toFixed(2)||"4.00"," HEHE/4h"]})]}),(0,s.jsxs)("div",{className:"flex justify-between items-center",children:[(0,s.jsx)("span",{className:"text-gray-300",children:"Speed Upgrades"}),(0,s.jsx)("span",{className:"text-white font-medium",children:e?.speedUpgrades||0})]})]})]}),!e?.hasBasicPlan&&(0,s.jsx)("div",{className:"bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg p-6 mb-6",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"w-12 h-12 bg-white/20 rounded-full mx-auto flex items-center justify-center mb-3",children:(0,s.jsx)("span",{className:"text-xl",children:"⛏️"})}),(0,s.jsx)("h3",{className:"text-white text-lg font-bold mb-2",children:"Basic Mining Plan"}),(0,s.jsx)("p",{className:"text-white/80 text-sm mb-4",children:"Unlock the ability to mine HEHE tokens"}),(0,s.jsxs)("div",{className:"bg-white/10 rounded-lg p-3 mb-4",children:[(0,s.jsx)("p",{className:"text-white font-semibold text-lg",children:"$1.00"}),(0,s.jsx)("p",{className:"text-white/80 text-sm",children:"One-time purchase"})]}),(0,s.jsxs)("div",{className:"text-left mb-4",children:[(0,s.jsx)("h4",{className:"text-white font-semibold mb-2",children:"Includes:"}),(0,s.jsxs)("ul",{className:"text-white/80 text-sm space-y-1",children:[(0,s.jsx)("li",{children:"• Mine 4 HEHE tokens every 4 hours"}),(0,s.jsx)("li",{children:"• Access to all tasks and referrals"}),(0,s.jsx)("li",{children:"• Eligible for future airdrops"}),(0,s.jsx)("li",{children:"• Ability to purchase speed upgrades"})]})]}),(0,s.jsx)("button",{onClick:f,disabled:r&&"basic"===o,className:"w-full bg-white text-blue-600 font-semibold py-3 px-4 rounded-lg hover:bg-gray-100 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors",children:r&&"basic"===o?"Processing...":"Purchase Basic Plan"})]})}),e?.hasBasicPlan&&(0,s.jsx)("div",{className:"bg-gray-800 rounded-lg border border-gray-700 p-6",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"w-12 h-12 bg-yellow-500/20 rounded-full mx-auto flex items-center justify-center mb-3",children:(0,s.jsx)("span",{className:"text-xl",children:"⚡"})}),(0,s.jsx)("h3",{className:"text-white text-lg font-bold mb-2",children:"Speed Upgrade"}),(0,s.jsx)("p",{className:"text-gray-400 text-sm mb-4",children:"Increase your mining power by 0.25 HEHE per 4 hours"}),(0,s.jsxs)("div",{className:"bg-yellow-500/10 border border-yellow-500/20 rounded-lg p-3 mb-4",children:[(0,s.jsx)("p",{className:"text-yellow-400 font-semibold text-lg",children:"$1.00"}),(0,s.jsx)("p",{className:"text-yellow-300 text-sm",children:"Per upgrade"})]}),(0,s.jsxs)("div",{className:"text-left mb-4",children:[(0,s.jsx)("h4",{className:"text-white font-semibold mb-2",children:"Upgrade Details:"}),(0,s.jsxs)("ul",{className:"text-gray-300 text-sm space-y-1",children:[(0,s.jsx)("li",{children:"• +0.25 HEHE tokens per 4-hour cycle"}),(0,s.jsx)("li",{children:"• Permanent increase to mining power"}),(0,s.jsx)("li",{children:"• No limit on number of upgrades"}),(0,s.jsx)("li",{children:"• Compounds with existing upgrades"})]})]}),(0,s.jsxs)("div",{className:"bg-gray-700 rounded-lg p-3 mb-4",children:[(0,s.jsxs)("p",{className:"text-gray-300 text-sm",children:["Current: ",e?.miningPower?.toFixed(2)," HEHE/4h"]}),(0,s.jsxs)("p",{className:"text-white text-sm",children:["After upgrade: ",((e?.miningPower||4)+.25).toFixed(2)," HEHE/4h"]})]}),x&&(0,s.jsx)("div",{className:"bg-orange-500/10 border border-orange-500/20 rounded-lg p-3 mb-4",children:(0,s.jsx)("p",{className:"text-orange-400 text-sm",children:"⚠️ Cannot purchase upgrades during active mining session. Complete your current mining first."})}),(0,s.jsx)("button",{onClick:h,disabled:g,className:`w-full font-semibold py-3 px-4 rounded-lg transition-colors ${g?"bg-gray-600 cursor-not-allowed text-gray-400":"bg-yellow-600 hover:bg-yellow-700 text-white"}`,children:r&&"speed"===o?"Processing...":x?"Mining in Progress - Upgrade Locked":"Purchase Speed Upgrade"})]})}),(0,s.jsxs)("div",{className:"mt-6 bg-blue-500/10 border border-blue-500/20 rounded-lg p-4",children:[(0,s.jsx)("h4",{className:"text-blue-400 font-semibold mb-2",children:"\uD83D\uDCA1 Upgrade Tips"}),(0,s.jsxs)("ul",{className:"text-blue-300 text-sm space-y-1",children:[(0,s.jsx)("li",{children:"• Speed upgrades stack - buy multiple for even faster mining"}),(0,s.jsx)("li",{children:"• All purchases are simulated for this demo"}),(0,s.jsx)("li",{children:"• In production, this would integrate with real payment processors"}),(0,s.jsx)("li",{children:"• Your upgrades are permanent and apply to all future mining sessions"})]})]})]})}function v(){let{user:e,updateUser:t,logout:r}=(0,n.A)(),[i,l]=(0,a.useState)("mining"),[o,c]=(0,a.useState)(!0);return o?(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-yellow-400 mx-auto mb-4"}),(0,s.jsx)("p",{className:"text-gray-400",children:"Loading your profile..."})]})}):(0,s.jsxs)("div",{className:"min-h-screen relative overflow-hidden",children:[(0,s.jsxs)("div",{className:"fixed inset-0 bg-gradient-to-br from-indigo-900 via-purple-900 to-pink-900",children:[(0,s.jsx)("div",{className:"absolute inset-0 opacity-30",style:{backgroundImage:"url(\"data:image/svg+xml,%3Csvg width='40' height='40' viewBox='0 0 40 40' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%23ffffff' fill-opacity='0.03'%3E%3Cpath d='M20 20c0-5.5-4.5-10-10-10s-10 4.5-10 10 4.5 10 10 10 10-4.5 10-10zm10 0c0-5.5-4.5-10-10-10s-10 4.5-10 10 4.5 10 10 10 10-4.5 10-10z'/%3E%3C/g%3E%3C/svg%3E\")"}}),(0,s.jsx)("div",{className:"absolute top-20 left-10 w-3 h-3 bg-yellow-400 rounded-full animate-float opacity-60"}),(0,s.jsx)("div",{className:"absolute top-40 right-20 w-2 h-2 bg-blue-400 rounded-full animate-float opacity-40",style:{animationDelay:"1s"}}),(0,s.jsx)("div",{className:"absolute bottom-40 left-20 w-4 h-4 bg-purple-400 rounded-full animate-float opacity-50",style:{animationDelay:"2s"}}),(0,s.jsx)("div",{className:"absolute bottom-60 right-10 w-2 h-2 bg-green-400 rounded-full animate-float opacity-70",style:{animationDelay:"0.5s"}})]}),(0,s.jsx)("header",{className:"relative z-10 glass-dark border-b border-white/10 px-4 py-4 animate-slide-up",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("div",{className:"w-12 h-12 bg-gradient-to-r from-yellow-400 via-orange-500 to-red-500 rounded-full flex items-center justify-center animate-pulse-glow",children:(0,s.jsx)("span",{className:"text-lg animate-float",children:"⛏️"})}),(0,s.jsx)("div",{className:"absolute -top-1 -right-1 w-4 h-4 bg-gradient-to-r from-green-400 to-blue-500 rounded-full animate-ping"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-xl font-bold bg-gradient-to-r from-yellow-400 via-orange-500 to-red-500 bg-clip-text text-transparent",children:"Hehe Miner"}),(0,s.jsxs)("p",{className:"text-sm text-gray-300 font-medium",children:["Welcome, ",e?.firstName," ",e?.lastName," ✨"]})]})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,s.jsxs)("div",{className:"text-right glass rounded-lg p-3 hover-lift",children:[(0,s.jsxs)("p",{className:"text-lg font-bold bg-gradient-to-r from-yellow-400 to-orange-500 bg-clip-text text-transparent animate-coin-flip",children:[e?.totalBalance?.toFixed(2)||"0.00"," HEHE"]}),(0,s.jsxs)("p",{className:"text-xs text-gray-300",children:["⚡ Power: ",e?.miningPower?.toFixed(2)||"4.00","/4h"]})]}),(0,s.jsx)("button",{onClick:()=>{r()},className:"w-10 h-10 glass rounded-full flex items-center justify-center text-gray-300 hover:text-red-400 transition-all duration-300 hover:scale-110 group",children:(0,s.jsx)("span",{className:"group-hover:animate-bounce",children:"\uD83D\uDEAA"})})]})]})}),(0,s.jsx)("main",{className:"relative z-10 pb-24 pt-4",children:(0,s.jsx)("div",{className:"animate-slide-up",children:(()=>{switch(i){case"mining":default:return(0,s.jsx)(f,{});case"tasks":return(0,s.jsx)(h,{});case"referrals":return(0,s.jsx)(x,{});case"airdrop":return(0,s.jsx)(g,{});case"subscription":return(0,s.jsx)(b,{})}})()})}),(0,s.jsx)("nav",{className:"fixed bottom-0 left-0 right-0 z-20 glass-dark border-t border-white/10 backdrop-blur-xl",children:(0,s.jsxs)("div",{className:"flex items-center justify-around py-3 px-2",children:[(0,s.jsxs)("button",{onClick:()=>l("mining"),className:`group flex flex-col items-center py-3 px-4 rounded-xl transition-all duration-300 transform hover:scale-110 ${"mining"===i?"bg-gradient-to-r from-yellow-400 to-orange-500 text-white shadow-lg shadow-yellow-500/25":"text-gray-400 hover:text-white hover:bg-white/10"}`,children:[(0,s.jsx)("span",{className:`text-2xl mb-1 ${"mining"===i?"animate-mining-pulse":"group-hover:animate-bounce"}`,children:"⛏️"}),(0,s.jsx)("span",{className:"text-xs font-semibold",children:"Mine"}),"mining"===i&&(0,s.jsx)("div",{className:"absolute -top-1 -right-1 w-2 h-2 bg-green-400 rounded-full animate-ping"})]}),(0,s.jsxs)("button",{onClick:()=>l("tasks"),className:`group flex flex-col items-center py-3 px-4 rounded-xl transition-all duration-300 transform hover:scale-110 ${"tasks"===i?"bg-gradient-to-r from-blue-400 to-purple-500 text-white shadow-lg shadow-blue-500/25":"text-gray-400 hover:text-white hover:bg-white/10"}`,children:[(0,s.jsx)("span",{className:`text-2xl mb-1 ${"tasks"===i?"animate-pulse":"group-hover:animate-bounce"}`,children:"\uD83D\uDCCB"}),(0,s.jsx)("span",{className:"text-xs font-semibold",children:"Tasks"}),"tasks"===i&&(0,s.jsx)("div",{className:"absolute -top-1 -right-1 w-2 h-2 bg-green-400 rounded-full animate-ping"})]}),(0,s.jsxs)("button",{onClick:()=>l("referrals"),className:`group flex flex-col items-center py-3 px-4 rounded-xl transition-all duration-300 transform hover:scale-110 ${"referrals"===i?"bg-gradient-to-r from-purple-400 to-pink-500 text-white shadow-lg shadow-purple-500/25":"text-gray-400 hover:text-white hover:bg-white/10"}`,children:[(0,s.jsx)("span",{className:`text-2xl mb-1 ${"referrals"===i?"animate-pulse":"group-hover:animate-bounce"}`,children:"\uD83D\uDC65"}),(0,s.jsx)("span",{className:"text-xs font-semibold",children:"Referrals"}),"referrals"===i&&(0,s.jsx)("div",{className:"absolute -top-1 -right-1 w-2 h-2 bg-green-400 rounded-full animate-ping"})]}),(0,s.jsxs)("button",{onClick:()=>l("airdrop"),className:`group flex flex-col items-center py-3 px-4 rounded-xl transition-all duration-300 transform hover:scale-110 ${"airdrop"===i?"bg-gradient-to-r from-green-400 to-blue-500 text-white shadow-lg shadow-green-500/25":"text-gray-400 hover:text-white hover:bg-white/10"}`,children:[(0,s.jsx)("span",{className:`text-2xl mb-1 ${"airdrop"===i?"animate-float":"group-hover:animate-bounce"}`,children:"\uD83E\uDE82"}),(0,s.jsx)("span",{className:"text-xs font-semibold",children:"Airdrop"}),"airdrop"===i&&(0,s.jsx)("div",{className:"absolute -top-1 -right-1 w-2 h-2 bg-green-400 rounded-full animate-ping"})]}),(0,s.jsxs)("button",{onClick:()=>l("subscription"),className:`group flex flex-col items-center py-3 px-4 rounded-xl transition-all duration-300 transform hover:scale-110 ${"subscription"===i?"bg-gradient-to-r from-orange-400 to-red-500 text-white shadow-lg shadow-orange-500/25":"text-gray-400 hover:text-white hover:bg-white/10"}`,children:[(0,s.jsx)("span",{className:`text-2xl mb-1 ${"subscription"===i?"animate-pulse":"group-hover:animate-bounce"}`,children:"⚡"}),(0,s.jsx)("span",{className:"text-xs font-semibold",children:"Upgrade"}),"subscription"===i&&(0,s.jsx)("div",{className:"absolute -top-1 -right-1 w-2 h-2 bg-green-400 rounded-full animate-ping"})]})]})})]})}function y(){let{user:e,token:t,isLoading:r}=(0,n.A)(),[i,l]=(0,a.useState)(!0);return r?(0,s.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 flex items-center justify-center",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-yellow-400 mx-auto mb-4"}),(0,s.jsx)("p",{className:"text-gray-300",children:"Loading Hehe Miner..."})]})}):!e||i?(0,s.jsx)(p,{onLaunchApp:()=>{l(!1)}}):(0,s.jsx)(v,{})}},1092:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,6346,23)),Promise.resolve().then(r.t.bind(r,7924,23)),Promise.resolve().then(r.t.bind(r,5656,23)),Promise.resolve().then(r.t.bind(r,99,23)),Promise.resolve().then(r.t.bind(r,8243,23)),Promise.resolve().then(r.t.bind(r,8827,23)),Promise.resolve().then(r.t.bind(r,2763,23)),Promise.resolve().then(r.t.bind(r,7173,23))},1135:()=>{},1204:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/app/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/app/page.tsx","default")},1261:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{default:function(){return o},getImageProps:function(){return l}});let s=r(4985),n=r(4953),a=r(6533),i=s._(r(1933));function l(e){let{props:t}=(0,n.getImgProps)(e,{defaultLoader:i.default,imgConf:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!1}});for(let[e,r]of Object.entries(t))void 0===r&&delete t[e];return{props:t}}let o=a.Image},1437:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{INTERCEPTION_ROUTE_MARKERS:function(){return n},extractInterceptionRouteInformation:function(){return i},isInterceptionRouteAppPath:function(){return a}});let s=r(4722),n=["(..)(..)","(.)","(..)","(...)"];function a(e){return void 0!==e.split("/").find(e=>n.find(t=>e.startsWith(t)))}function i(e){let t,r,a;for(let s of e.split("/"))if(r=n.find(e=>s.startsWith(e))){[t,a]=e.split(r,2);break}if(!t||!r||!a)throw Object.defineProperty(Error("Invalid interception route: "+e+". Must be in the format /<intercepting route>/(..|...|..)(..)/<intercepted route>"),"__NEXT_ERROR_CODE",{value:"E269",enumerable:!1,configurable:!0});switch(t=(0,s.normalizeAppPath)(t),r){case"(.)":a="/"===t?"/"+a:t+"/"+a;break;case"(..)":if("/"===t)throw Object.defineProperty(Error("Invalid interception route: "+e+". Cannot use (..) marker at the root level, use (.) instead."),"__NEXT_ERROR_CODE",{value:"E207",enumerable:!1,configurable:!0});a=t.split("/").slice(0,-1).concat(a).join("/");break;case"(...)":a="/"+a;break;case"(..)(..)":let i=t.split("/");if(i.length<=2)throw Object.defineProperty(Error("Invalid interception route: "+e+". Cannot use (..)(..) marker at the root level or one level up."),"__NEXT_ERROR_CODE",{value:"E486",enumerable:!1,configurable:!0});a=i.slice(0,-2).concat(a).join("/");break;default:throw Object.defineProperty(Error("Invariant: unexpected marker"),"__NEXT_ERROR_CODE",{value:"E112",enumerable:!1,configurable:!0})}return{interceptingRoute:t,interceptedRoute:a}}},1480:(e,t)=>{"use strict";function r(e){let{widthInt:t,heightInt:r,blurWidth:s,blurHeight:n,blurDataURL:a,objectFit:i}=e,l=s?40*s:t,o=n?40*n:r,c=l&&o?"viewBox='0 0 "+l+" "+o+"'":"";return"%3Csvg xmlns='http://www.w3.org/2000/svg' "+c+"%3E%3Cfilter id='b' color-interpolation-filters='sRGB'%3E%3CfeGaussianBlur stdDeviation='20'/%3E%3CfeColorMatrix values='1 0 0 0 0 0 1 0 0 0 0 0 1 0 0 0 0 0 100 -1' result='s'/%3E%3CfeFlood x='0' y='0' width='100%25' height='100%25'/%3E%3CfeComposite operator='out' in='s'/%3E%3CfeComposite in2='SourceGraphic'/%3E%3CfeGaussianBlur stdDeviation='20'/%3E%3C/filter%3E%3Cimage width='100%25' height='100%25' x='0' y='0' preserveAspectRatio='"+(c?"none":"contain"===i?"xMidYMid":"cover"===i?"xMidYMid slice":"none")+"' style='filter: url(%23b);' href='"+a+"'/%3E%3C/svg%3E"}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getImageBlurSvg",{enumerable:!0,get:function(){return r}})},1658:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{fillMetadataSegment:function(){return m},normalizeMetadataPageToRoute:function(){return f},normalizeMetadataRoute:function(){return p}});let s=r(8304),n=function(e){return e&&e.__esModule?e:{default:e}}(r(8671)),a=r(6341),i=r(4396),l=r(660),o=r(4722),c=r(2958),d=r(5499);function u(e){let t=n.default.dirname(e);if(e.endsWith("/sitemap"))return"";let r="";return t.split("/").some(e=>(0,d.isGroupSegment)(e)||(0,d.isParallelRouteSegment)(e))&&(r=(0,l.djb2Hash)(t).toString(36).slice(0,6)),r}function m(e,t,r){let s=(0,o.normalizeAppPath)(e),l=(0,i.getNamedRouteRegex)(s,{prefixRouteKeys:!1}),d=(0,a.interpolateDynamicPath)(s,t,l),{name:m,ext:p}=n.default.parse(r),f=u(n.default.posix.join(e,m)),h=f?`-${f}`:"";return(0,c.normalizePathSep)(n.default.join(d,`${m}${h}${p}`))}function p(e){if(!(0,s.isMetadataPage)(e))return e;let t=e,r="";if("/robots"===e?t+=".txt":"/manifest"===e?t+=".webmanifest":r=u(e),!t.endsWith("/route")){let{dir:e,name:s,ext:a}=n.default.parse(t);t=n.default.posix.join(e,`${s}${r?`-${r}`:""}${a}`,"route")}return t}function f(e,t){let r=e.endsWith("/route"),s=r?e.slice(0,-6):e,n=s.endsWith("/sitemap")?".xml":"";return(t?`${s}/[__metadata_id__]`:`${s}${n}`)+(r?"/route":"")}},1879:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>u,pages:()=>d,routeModule:()=>m,tree:()=>c});var s=r(5239),n=r(8088),a=r(8170),i=r.n(a),l=r(893),o={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);r.d(t,o);let c={children:["",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,1204)),"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/app/page.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,4431)),"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,5284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/app/page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},m=new s.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/page",pathname:"/",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},1933:(e,t)=>{"use strict";function r(e){var t;let{config:r,src:s,width:n,quality:a}=e,i=a||(null==(t=r.qualities)?void 0:t.reduce((e,t)=>Math.abs(t-75)<Math.abs(e-75)?t:e))||75;return r.path+"?url="+encodeURIComponent(s)+"&w="+n+"&q="+i+(s.startsWith("/_next/static/media/"),"")}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return s}}),r.__next_img_default=!0;let s=r},2437:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getPathMatch",{enumerable:!0,get:function(){return n}});let s=r(5362);function n(e,t){let r=[],n=(0,s.pathToRegexp)(e,r,{delimiter:"/",sensitive:"boolean"==typeof(null==t?void 0:t.sensitive)&&t.sensitive,strict:null==t?void 0:t.strict}),a=(0,s.regexpToFunction)((null==t?void 0:t.regexModifier)?new RegExp(t.regexModifier(n.source),n.flags):n,r);return(e,s)=>{if("string"!=typeof e)return!1;let n=a(e);if(!n)return!1;if(null==t?void 0:t.removeUnnamedParams)for(let e of r)"number"==typeof e.name&&delete n.params[e.name];return{...s,...n.params}}}},2756:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{VALID_LOADERS:function(){return r},imageConfigDefault:function(){return s}});let r=["default","imgix","cloudinary","akamai","custom"],s={deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",loaderFile:"",domains:[],disableStaticImages:!1,minimumCacheTTL:60,formats:["image/webp"],dangerouslyAllowSVG:!1,contentSecurityPolicy:"script-src 'none'; frame-src 'none'; sandbox;",contentDispositionType:"attachment",localPatterns:void 0,remotePatterns:[],qualities:void 0,unoptimized:!1}},2785:(e,t)=>{"use strict";function r(e){let t={};for(let[r,s]of e.entries()){let e=t[r];void 0===e?t[r]=s:Array.isArray(e)?e.push(s):t[r]=[e,s]}return t}function s(e){return"string"==typeof e?e:("number"!=typeof e||isNaN(e))&&"boolean"!=typeof e?"":String(e)}function n(e){let t=new URLSearchParams;for(let[r,n]of Object.entries(e))if(Array.isArray(n))for(let e of n)t.append(r,s(e));else t.set(r,s(n));return t}function a(e){for(var t=arguments.length,r=Array(t>1?t-1:0),s=1;s<t;s++)r[s-1]=arguments[s];for(let t of r){for(let r of t.keys())e.delete(r);for(let[r,s]of t.entries())e.append(r,s)}return e}Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{assign:function(){return a},searchParamsToUrlQuery:function(){return r},urlQueryToSearchParams:function(){return n}})},2958:(e,t)=>{"use strict";function r(e){return e.replace(/\\/g,"/")}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"normalizePathSep",{enumerable:!0,get:function(){return r}})},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3038:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"useMergedRef",{enumerable:!0,get:function(){return n}});let s=r(3210);function n(e,t){let r=(0,s.useRef)(null),n=(0,s.useRef)(null);return(0,s.useCallback)(s=>{if(null===s){let e=r.current;e&&(r.current=null,e());let t=n.current;t&&(n.current=null,t())}else e&&(r.current=a(e,s)),t&&(n.current=a(t,s))},[e,t])}function a(e,t){if("function"!=typeof e)return e.current=t,()=>{e.current=null};{let r=e(t);return"function"==typeof r?r:()=>e(null)}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},3213:(e,t,r)=>{"use strict";r.d(t,{A:()=>l,AuthProvider:()=>i});var s=r(687),n=r(3210);let a=(0,n.createContext)(void 0);function i({children:e}){let[t,r]=(0,n.useState)(null),[i,l]=(0,n.useState)(null),[o,c]=(0,n.useState)(!0);return(0,s.jsx)(a.Provider,{value:{user:t,token:i,login:(e,t)=>{r(e),l(t),localStorage.setItem("auth_token",t),localStorage.setItem("user_data",JSON.stringify(e))},logout:()=>{r(null),l(null),localStorage.removeItem("auth_token"),localStorage.removeItem("user_data")},updateUser:e=>{if(t){let s={...t,...e};r(s),localStorage.setItem("user_data",JSON.stringify(s))}},isLoading:o},children:e})}function l(){let e=(0,n.useContext)(a);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}},3293:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"escapeStringRegexp",{enumerable:!0,get:function(){return n}});let r=/[|\\{}()[\]^$+*?.-]/,s=/[|\\{}()[\]^$+*?.-]/g;function n(e){return r.test(e)?e.replace(s,"\\$&"):e}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3736:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"parseRelativeUrl",{enumerable:!0,get:function(){return n}}),r(4827);let s=r(2785);function n(e,t,r){void 0===r&&(r=!0);let n=new URL("http://n"),a=t?new URL(t,n):e.startsWith(".")?new URL("http://n"):n,{pathname:i,searchParams:l,search:o,hash:c,href:d,origin:u}=new URL(e,a);if(u!==n.origin)throw Object.defineProperty(Error("invariant: invalid relative URL, router received "+e),"__NEXT_ERROR_CODE",{value:"E159",enumerable:!1,configurable:!0});return{pathname:i,query:r?(0,s.searchParamsToUrlQuery)(l):void 0,search:o,hash:c,href:d.slice(u.length)}}},3796:(e,t,r)=>{Promise.resolve().then(r.bind(r,9131))},3873:e=>{"use strict";e.exports=require("path")},4396:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{getNamedMiddlewareRegex:function(){return h},getNamedRouteRegex:function(){return f},getRouteRegex:function(){return u},parseParameter:function(){return o}});let s=r(6143),n=r(1437),a=r(3293),i=r(2887),l=/^([^[]*)\[((?:\[[^\]]*\])|[^\]]+)\](.*)$/;function o(e){let t=e.match(l);return t?c(t[2]):c(e)}function c(e){let t=e.startsWith("[")&&e.endsWith("]");t&&(e=e.slice(1,-1));let r=e.startsWith("...");return r&&(e=e.slice(3)),{key:e,repeat:r,optional:t}}function d(e,t,r){let s={},o=1,d=[];for(let u of(0,i.removeTrailingSlash)(e).slice(1).split("/")){let e=n.INTERCEPTION_ROUTE_MARKERS.find(e=>u.startsWith(e)),i=u.match(l);if(e&&i&&i[2]){let{key:t,optional:r,repeat:n}=c(i[2]);s[t]={pos:o++,repeat:n,optional:r},d.push("/"+(0,a.escapeStringRegexp)(e)+"([^/]+?)")}else if(i&&i[2]){let{key:e,repeat:t,optional:n}=c(i[2]);s[e]={pos:o++,repeat:t,optional:n},r&&i[1]&&d.push("/"+(0,a.escapeStringRegexp)(i[1]));let l=t?n?"(?:/(.+?))?":"/(.+?)":"/([^/]+?)";r&&i[1]&&(l=l.substring(1)),d.push(l)}else d.push("/"+(0,a.escapeStringRegexp)(u));t&&i&&i[3]&&d.push((0,a.escapeStringRegexp)(i[3]))}return{parameterizedRoute:d.join(""),groups:s}}function u(e,t){let{includeSuffix:r=!1,includePrefix:s=!1,excludeOptionalTrailingSlash:n=!1}=void 0===t?{}:t,{parameterizedRoute:a,groups:i}=d(e,r,s),l=a;return n||(l+="(?:/)?"),{re:RegExp("^"+l+"$"),groups:i}}function m(e){let t,{interceptionMarker:r,getSafeRouteKey:s,segment:n,routeKeys:i,keyPrefix:l,backreferenceDuplicateKeys:o}=e,{key:d,optional:u,repeat:m}=c(n),p=d.replace(/\W/g,"");l&&(p=""+l+p);let f=!1;(0===p.length||p.length>30)&&(f=!0),isNaN(parseInt(p.slice(0,1)))||(f=!0),f&&(p=s());let h=p in i;l?i[p]=""+l+d:i[p]=d;let x=r?(0,a.escapeStringRegexp)(r):"";return t=h&&o?"\\k<"+p+">":m?"(?<"+p+">.+?)":"(?<"+p+">[^/]+?)",u?"(?:/"+x+t+")?":"/"+x+t}function p(e,t,r,o,c){let d,u=(d=0,()=>{let e="",t=++d;for(;t>0;)e+=String.fromCharCode(97+(t-1)%26),t=Math.floor((t-1)/26);return e}),p={},f=[];for(let d of(0,i.removeTrailingSlash)(e).slice(1).split("/")){let e=n.INTERCEPTION_ROUTE_MARKERS.some(e=>d.startsWith(e)),i=d.match(l);if(e&&i&&i[2])f.push(m({getSafeRouteKey:u,interceptionMarker:i[1],segment:i[2],routeKeys:p,keyPrefix:t?s.NEXT_INTERCEPTION_MARKER_PREFIX:void 0,backreferenceDuplicateKeys:c}));else if(i&&i[2]){o&&i[1]&&f.push("/"+(0,a.escapeStringRegexp)(i[1]));let e=m({getSafeRouteKey:u,segment:i[2],routeKeys:p,keyPrefix:t?s.NEXT_QUERY_PARAM_PREFIX:void 0,backreferenceDuplicateKeys:c});o&&i[1]&&(e=e.substring(1)),f.push(e)}else f.push("/"+(0,a.escapeStringRegexp)(d));r&&i&&i[3]&&f.push((0,a.escapeStringRegexp)(i[3]))}return{namedParameterizedRoute:f.join(""),routeKeys:p}}function f(e,t){var r,s,n;let a=p(e,t.prefixRouteKeys,null!=(r=t.includeSuffix)&&r,null!=(s=t.includePrefix)&&s,null!=(n=t.backreferenceDuplicateKeys)&&n),i=a.namedParameterizedRoute;return t.excludeOptionalTrailingSlash||(i+="(?:/)?"),{...u(e,t),namedRegex:"^"+i+"$",routeKeys:a.routeKeys}}function h(e,t){let{parameterizedRoute:r}=d(e,!1,!1),{catchAll:s=!0}=t;if("/"===r)return{namedRegex:"^/"+(s?".*":"")+"$"};let{namedParameterizedRoute:n}=p(e,!1,!1,!1,!1);return{namedRegex:"^"+n+(s?"(?:(/.*)?)":"")+"$"}}},4431:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>i,metadata:()=>a});var s=r(7413);r(1135);var n=r(9131);let a={title:"Hehe Miner - Telegram Mining Game",description:"Mine Hehe tokens in this fun Telegram-based mining game"};function i({children:e}){return(0,s.jsx)("html",{lang:"en",children:(0,s.jsx)("body",{className:"antialiased bg-gray-900 text-white min-h-screen font-sans",children:(0,s.jsx)(n.AuthProvider,{children:e})})})}},4574:(e,t,r)=>{Promise.resolve().then(r.bind(r,1204))},4604:(e,t)=>{"use strict";function r(e){let{ampFirst:t=!1,hybrid:r=!1,hasQuery:s=!1}=void 0===e?{}:e;return t||r&&s}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isInAmpMode",{enumerable:!0,get:function(){return r}})},4708:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,6444,23)),Promise.resolve().then(r.t.bind(r,6042,23)),Promise.resolve().then(r.t.bind(r,8170,23)),Promise.resolve().then(r.t.bind(r,9477,23)),Promise.resolve().then(r.t.bind(r,9345,23)),Promise.resolve().then(r.t.bind(r,2089,23)),Promise.resolve().then(r.t.bind(r,6577,23)),Promise.resolve().then(r.t.bind(r,1307,23))},4722:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{normalizeAppPath:function(){return a},normalizeRscURL:function(){return i}});let s=r(5531),n=r(5499);function a(e){return(0,s.ensureLeadingSlash)(e.split("/").reduce((e,t,r,s)=>!t||(0,n.isGroupSegment)(t)||"@"===t[0]||("page"===t||"route"===t)&&r===s.length-1?e:e+"/"+t,""))}function i(e){return e.replace(/\.rsc($|\?)/,"$1")}},4827:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{DecodeError:function(){return f},MiddlewareNotFoundError:function(){return b},MissingStaticPage:function(){return g},NormalizeError:function(){return h},PageNotFoundError:function(){return x},SP:function(){return m},ST:function(){return p},WEB_VITALS:function(){return r},execOnce:function(){return s},getDisplayName:function(){return o},getLocationOrigin:function(){return i},getURL:function(){return l},isAbsoluteUrl:function(){return a},isResSent:function(){return c},loadGetInitialProps:function(){return u},normalizeRepeatedSlashes:function(){return d},stringifyError:function(){return v}});let r=["CLS","FCP","FID","INP","LCP","TTFB"];function s(e){let t,r=!1;return function(){for(var s=arguments.length,n=Array(s),a=0;a<s;a++)n[a]=arguments[a];return r||(r=!0,t=e(...n)),t}}let n=/^[a-zA-Z][a-zA-Z\d+\-.]*?:/,a=e=>n.test(e);function i(){let{protocol:e,hostname:t,port:r}=window.location;return e+"//"+t+(r?":"+r:"")}function l(){let{href:e}=window.location,t=i();return e.substring(t.length)}function o(e){return"string"==typeof e?e:e.displayName||e.name||"Unknown"}function c(e){return e.finished||e.headersSent}function d(e){let t=e.split("?");return t[0].replace(/\\/g,"/").replace(/\/\/+/g,"/")+(t[1]?"?"+t.slice(1).join("?"):"")}async function u(e,t){let r=t.res||t.ctx&&t.ctx.res;if(!e.getInitialProps)return t.ctx&&t.Component?{pageProps:await u(t.Component,t.ctx)}:{};let s=await e.getInitialProps(t);if(r&&c(r))return s;if(!s)throw Object.defineProperty(Error('"'+o(e)+'.getInitialProps()" should resolve to an object. But found "'+s+'" instead.'),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return s}let m="undefined"!=typeof performance,p=m&&["mark","measure","getEntriesByName"].every(e=>"function"==typeof performance[e]);class f extends Error{}class h extends Error{}class x extends Error{constructor(e){super(),this.code="ENOENT",this.name="PageNotFoundError",this.message="Cannot find module for page: "+e}}class g extends Error{constructor(e,t){super(),this.message="Failed to load static file for page: "+e+" "+t}}class b extends Error{constructor(){super(),this.code="ENOENT",this.message="Cannot find the middleware module"}}function v(e){return JSON.stringify({message:e.message,stack:e.stack})}},4953:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getImgProps",{enumerable:!0,get:function(){return o}}),r(148);let s=r(1480),n=r(2756),a=["-moz-initial","fill","none","scale-down",void 0];function i(e){return void 0!==e.default}function l(e){return void 0===e?e:"number"==typeof e?Number.isFinite(e)?e:NaN:"string"==typeof e&&/^[0-9]+$/.test(e)?parseInt(e,10):NaN}function o(e,t){var r,o;let c,d,u,{src:m,sizes:p,unoptimized:f=!1,priority:h=!1,loading:x,className:g,quality:b,width:v,height:y,fill:j=!1,style:N,overrideSrc:w,onLoad:E,onLoadingComplete:_,placeholder:P="empty",blurDataURL:R,fetchPriority:S,decoding:C="async",layout:k,objectFit:A,objectPosition:D,lazyBoundary:O,lazyRoot:T,...M}=e,{imgConf:I,showAltText:z,blurComplete:H,defaultLoader:$}=t,U=I||n.imageConfigDefault;if("allSizes"in U)c=U;else{let e=[...U.deviceSizes,...U.imageSizes].sort((e,t)=>e-t),t=U.deviceSizes.sort((e,t)=>e-t),s=null==(r=U.qualities)?void 0:r.sort((e,t)=>e-t);c={...U,allSizes:e,deviceSizes:t,qualities:s}}if(void 0===$)throw Object.defineProperty(Error("images.loaderFile detected but the file is missing default export.\nRead more: https://nextjs.org/docs/messages/invalid-images-config"),"__NEXT_ERROR_CODE",{value:"E163",enumerable:!1,configurable:!0});let F=M.loader||$;delete M.loader,delete M.srcSet;let L="__next_img_default"in F;if(L){if("custom"===c.loader)throw Object.defineProperty(Error('Image with src "'+m+'" is missing "loader" prop.\nRead more: https://nextjs.org/docs/messages/next-image-missing-loader'),"__NEXT_ERROR_CODE",{value:"E252",enumerable:!1,configurable:!0})}else{let e=F;F=t=>{let{config:r,...s}=t;return e(s)}}if(k){"fill"===k&&(j=!0);let e={intrinsic:{maxWidth:"100%",height:"auto"},responsive:{width:"100%",height:"auto"}}[k];e&&(N={...N,...e});let t={responsive:"100vw",fill:"100vw"}[k];t&&!p&&(p=t)}let q="",B=l(v),W=l(y);if((o=m)&&"object"==typeof o&&(i(o)||void 0!==o.src)){let e=i(m)?m.default:m;if(!e.src)throw Object.defineProperty(Error("An object should only be passed to the image component src parameter if it comes from a static image import. It must include src. Received "+JSON.stringify(e)),"__NEXT_ERROR_CODE",{value:"E460",enumerable:!1,configurable:!0});if(!e.height||!e.width)throw Object.defineProperty(Error("An object should only be passed to the image component src parameter if it comes from a static image import. It must include height and width. Received "+JSON.stringify(e)),"__NEXT_ERROR_CODE",{value:"E48",enumerable:!1,configurable:!0});if(d=e.blurWidth,u=e.blurHeight,R=R||e.blurDataURL,q=e.src,!j)if(B||W){if(B&&!W){let t=B/e.width;W=Math.round(e.height*t)}else if(!B&&W){let t=W/e.height;B=Math.round(e.width*t)}}else B=e.width,W=e.height}let X=!h&&("lazy"===x||void 0===x);(!(m="string"==typeof m?m:q)||m.startsWith("data:")||m.startsWith("blob:"))&&(f=!0,X=!1),c.unoptimized&&(f=!0),L&&!c.dangerouslyAllowSVG&&m.split("?",1)[0].endsWith(".svg")&&(f=!0);let G=l(b),K=Object.assign(j?{position:"absolute",height:"100%",width:"100%",left:0,top:0,right:0,bottom:0,objectFit:A,objectPosition:D}:{},z?{}:{color:"transparent"},N),V=H||"empty"===P?null:"blur"===P?'url("data:image/svg+xml;charset=utf-8,'+(0,s.getImageBlurSvg)({widthInt:B,heightInt:W,blurWidth:d,blurHeight:u,blurDataURL:R||"",objectFit:K.objectFit})+'")':'url("'+P+'")',Y=a.includes(K.objectFit)?"fill"===K.objectFit?"100% 100%":"cover":K.objectFit,Q=V?{backgroundSize:Y,backgroundPosition:K.objectPosition||"50% 50%",backgroundRepeat:"no-repeat",backgroundImage:V}:{},J=function(e){let{config:t,src:r,unoptimized:s,width:n,quality:a,sizes:i,loader:l}=e;if(s)return{src:r,srcSet:void 0,sizes:void 0};let{widths:o,kind:c}=function(e,t,r){let{deviceSizes:s,allSizes:n}=e;if(r){let e=/(^|\s)(1?\d?\d)vw/g,t=[];for(let s;s=e.exec(r);)t.push(parseInt(s[2]));if(t.length){let e=.01*Math.min(...t);return{widths:n.filter(t=>t>=s[0]*e),kind:"w"}}return{widths:n,kind:"w"}}return"number"!=typeof t?{widths:s,kind:"w"}:{widths:[...new Set([t,2*t].map(e=>n.find(t=>t>=e)||n[n.length-1]))],kind:"x"}}(t,n,i),d=o.length-1;return{sizes:i||"w"!==c?i:"100vw",srcSet:o.map((e,s)=>l({config:t,src:r,quality:a,width:e})+" "+("w"===c?e:s+1)+c).join(", "),src:l({config:t,src:r,quality:a,width:o[d]})}}({config:c,src:m,unoptimized:f,width:B,quality:G,sizes:p,loader:F});return{props:{...M,loading:X?"lazy":x,fetchPriority:S,width:B,height:W,decoding:C,className:g,style:{...K,...Q},sizes:J.sizes,srcSet:J.srcSet,src:w||J.src},meta:{unoptimized:f,priority:h,placeholder:P,fill:j}}}},4959:(e,t,r)=>{"use strict";e.exports=r(4041).vendored.contexts.AmpContext},5236:(e,t,r)=>{Promise.resolve().then(r.bind(r,3213))},5362:e=>{(()=>{"use strict";"undefined"!=typeof __nccwpck_require__&&(__nccwpck_require__.ab=__dirname+"/");var t={};(()=>{function e(e,t){void 0===t&&(t={});for(var r=function(e){for(var t=[],r=0;r<e.length;){var s=e[r];if("*"===s||"+"===s||"?"===s){t.push({type:"MODIFIER",index:r,value:e[r++]});continue}if("\\"===s){t.push({type:"ESCAPED_CHAR",index:r++,value:e[r++]});continue}if("{"===s){t.push({type:"OPEN",index:r,value:e[r++]});continue}if("}"===s){t.push({type:"CLOSE",index:r,value:e[r++]});continue}if(":"===s){for(var n="",a=r+1;a<e.length;){var i=e.charCodeAt(a);if(i>=48&&i<=57||i>=65&&i<=90||i>=97&&i<=122||95===i){n+=e[a++];continue}break}if(!n)throw TypeError("Missing parameter name at "+r);t.push({type:"NAME",index:r,value:n}),r=a;continue}if("("===s){var l=1,o="",a=r+1;if("?"===e[a])throw TypeError('Pattern cannot start with "?" at '+a);for(;a<e.length;){if("\\"===e[a]){o+=e[a++]+e[a++];continue}if(")"===e[a]){if(0==--l){a++;break}}else if("("===e[a]&&(l++,"?"!==e[a+1]))throw TypeError("Capturing groups are not allowed at "+a);o+=e[a++]}if(l)throw TypeError("Unbalanced pattern at "+r);if(!o)throw TypeError("Missing pattern at "+r);t.push({type:"PATTERN",index:r,value:o}),r=a;continue}t.push({type:"CHAR",index:r,value:e[r++]})}return t.push({type:"END",index:r,value:""}),t}(e),s=t.prefixes,a=void 0===s?"./":s,i="[^"+n(t.delimiter||"/#?")+"]+?",l=[],o=0,c=0,d="",u=function(e){if(c<r.length&&r[c].type===e)return r[c++].value},m=function(e){var t=u(e);if(void 0!==t)return t;var s=r[c];throw TypeError("Unexpected "+s.type+" at "+s.index+", expected "+e)},p=function(){for(var e,t="";e=u("CHAR")||u("ESCAPED_CHAR");)t+=e;return t};c<r.length;){var f=u("CHAR"),h=u("NAME"),x=u("PATTERN");if(h||x){var g=f||"";-1===a.indexOf(g)&&(d+=g,g=""),d&&(l.push(d),d=""),l.push({name:h||o++,prefix:g,suffix:"",pattern:x||i,modifier:u("MODIFIER")||""});continue}var b=f||u("ESCAPED_CHAR");if(b){d+=b;continue}if(d&&(l.push(d),d=""),u("OPEN")){var g=p(),v=u("NAME")||"",y=u("PATTERN")||"",j=p();m("CLOSE"),l.push({name:v||(y?o++:""),pattern:v&&!y?i:y,prefix:g,suffix:j,modifier:u("MODIFIER")||""});continue}m("END")}return l}function r(e,t){void 0===t&&(t={});var r=a(t),s=t.encode,n=void 0===s?function(e){return e}:s,i=t.validate,l=void 0===i||i,o=e.map(function(e){if("object"==typeof e)return RegExp("^(?:"+e.pattern+")$",r)});return function(t){for(var r="",s=0;s<e.length;s++){var a=e[s];if("string"==typeof a){r+=a;continue}var i=t?t[a.name]:void 0,c="?"===a.modifier||"*"===a.modifier,d="*"===a.modifier||"+"===a.modifier;if(Array.isArray(i)){if(!d)throw TypeError('Expected "'+a.name+'" to not repeat, but got an array');if(0===i.length){if(c)continue;throw TypeError('Expected "'+a.name+'" to not be empty')}for(var u=0;u<i.length;u++){var m=n(i[u],a);if(l&&!o[s].test(m))throw TypeError('Expected all "'+a.name+'" to match "'+a.pattern+'", but got "'+m+'"');r+=a.prefix+m+a.suffix}continue}if("string"==typeof i||"number"==typeof i){var m=n(String(i),a);if(l&&!o[s].test(m))throw TypeError('Expected "'+a.name+'" to match "'+a.pattern+'", but got "'+m+'"');r+=a.prefix+m+a.suffix;continue}if(!c){var p=d?"an array":"a string";throw TypeError('Expected "'+a.name+'" to be '+p)}}return r}}function s(e,t,r){void 0===r&&(r={});var s=r.decode,n=void 0===s?function(e){return e}:s;return function(r){var s=e.exec(r);if(!s)return!1;for(var a=s[0],i=s.index,l=Object.create(null),o=1;o<s.length;o++)!function(e){if(void 0!==s[e]){var r=t[e-1];"*"===r.modifier||"+"===r.modifier?l[r.name]=s[e].split(r.prefix+r.suffix).map(function(e){return n(e,r)}):l[r.name]=n(s[e],r)}}(o);return{path:a,index:i,params:l}}}function n(e){return e.replace(/([.+*?=^!:${}()[\]|/\\])/g,"\\$1")}function a(e){return e&&e.sensitive?"":"i"}function i(e,t,r){void 0===r&&(r={});for(var s=r.strict,i=void 0!==s&&s,l=r.start,o=r.end,c=r.encode,d=void 0===c?function(e){return e}:c,u="["+n(r.endsWith||"")+"]|$",m="["+n(r.delimiter||"/#?")+"]",p=void 0===l||l?"^":"",f=0;f<e.length;f++){var h=e[f];if("string"==typeof h)p+=n(d(h));else{var x=n(d(h.prefix)),g=n(d(h.suffix));if(h.pattern)if(t&&t.push(h),x||g)if("+"===h.modifier||"*"===h.modifier){var b="*"===h.modifier?"?":"";p+="(?:"+x+"((?:"+h.pattern+")(?:"+g+x+"(?:"+h.pattern+"))*)"+g+")"+b}else p+="(?:"+x+"("+h.pattern+")"+g+")"+h.modifier;else p+="("+h.pattern+")"+h.modifier;else p+="(?:"+x+g+")"+h.modifier}}if(void 0===o||o)i||(p+=m+"?"),p+=r.endsWith?"(?="+u+")":"$";else{var v=e[e.length-1],y="string"==typeof v?m.indexOf(v[v.length-1])>-1:void 0===v;i||(p+="(?:"+m+"(?="+u+"))?"),y||(p+="(?="+m+"|"+u+")")}return new RegExp(p,a(r))}function l(t,r,s){if(t instanceof RegExp){if(!r)return t;var n=t.source.match(/\((?!\?)/g);if(n)for(var o=0;o<n.length;o++)r.push({name:o,prefix:"",suffix:"",modifier:"",pattern:""});return t}return Array.isArray(t)?RegExp("(?:"+t.map(function(e){return l(e,r,s).source}).join("|")+")",a(s)):i(e(t,s),r,s)}Object.defineProperty(t,"__esModule",{value:!0}),t.parse=e,t.compile=function(t,s){return r(e(t,s),s)},t.tokensToFunction=r,t.match=function(e,t){var r=[];return s(l(e,r,t),r,t)},t.regexpToFunction=s,t.tokensToRegexp=i,t.pathToRegexp=l})(),e.exports=t})()},5511:e=>{"use strict";e.exports=require("crypto")},5526:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{compileNonPath:function(){return d},matchHas:function(){return c},parseDestination:function(){return u},prepareDestination:function(){return m}});let s=r(5362),n=r(3293),a=r(6759),i=r(1437),l=r(8212);function o(e){return e.replace(/__ESC_COLON_/gi,":")}function c(e,t,r,s){void 0===r&&(r=[]),void 0===s&&(s=[]);let n={},a=r=>{let s,a=r.key;switch(r.type){case"header":a=a.toLowerCase(),s=e.headers[a];break;case"cookie":s="cookies"in e?e.cookies[r.key]:(0,l.getCookieParser)(e.headers)()[r.key];break;case"query":s=t[a];break;case"host":{let{host:t}=(null==e?void 0:e.headers)||{};s=null==t?void 0:t.split(":",1)[0].toLowerCase()}}if(!r.value&&s)return n[function(e){let t="";for(let r=0;r<e.length;r++){let s=e.charCodeAt(r);(s>64&&s<91||s>96&&s<123)&&(t+=e[r])}return t}(a)]=s,!0;if(s){let e=RegExp("^"+r.value+"$"),t=Array.isArray(s)?s.slice(-1)[0].match(e):s.match(e);if(t)return Array.isArray(t)&&(t.groups?Object.keys(t.groups).forEach(e=>{n[e]=t.groups[e]}):"host"===r.type&&t[0]&&(n.host=t[0])),!0}return!1};return!(!r.every(e=>a(e))||s.some(e=>a(e)))&&n}function d(e,t){if(!e.includes(":"))return e;for(let r of Object.keys(t))e.includes(":"+r)&&(e=e.replace(RegExp(":"+r+"\\*","g"),":"+r+"--ESCAPED_PARAM_ASTERISKS").replace(RegExp(":"+r+"\\?","g"),":"+r+"--ESCAPED_PARAM_QUESTION").replace(RegExp(":"+r+"\\+","g"),":"+r+"--ESCAPED_PARAM_PLUS").replace(RegExp(":"+r+"(?!\\w)","g"),"--ESCAPED_PARAM_COLON"+r));return e=e.replace(/(:|\*|\?|\+|\(|\)|\{|\})/g,"\\$1").replace(/--ESCAPED_PARAM_PLUS/g,"+").replace(/--ESCAPED_PARAM_COLON/g,":").replace(/--ESCAPED_PARAM_QUESTION/g,"?").replace(/--ESCAPED_PARAM_ASTERISKS/g,"*"),(0,s.compile)("/"+e,{validate:!1})(t).slice(1)}function u(e){let t=e.destination;for(let r of Object.keys({...e.params,...e.query}))r&&(t=t.replace(RegExp(":"+(0,n.escapeStringRegexp)(r),"g"),"__ESC_COLON_"+r));let r=(0,a.parseUrl)(t),s=r.pathname;s&&(s=o(s));let i=r.href;i&&(i=o(i));let l=r.hostname;l&&(l=o(l));let c=r.hash;return c&&(c=o(c)),{...r,pathname:s,hostname:l,href:i,hash:c}}function m(e){let t,r,n=Object.assign({},e.query),a=u(e),{hostname:l,query:c}=a,m=a.pathname;a.hash&&(m=""+m+a.hash);let p=[],f=[];for(let e of((0,s.pathToRegexp)(m,f),f))p.push(e.name);if(l){let e=[];for(let t of((0,s.pathToRegexp)(l,e),e))p.push(t.name)}let h=(0,s.compile)(m,{validate:!1});for(let[r,n]of(l&&(t=(0,s.compile)(l,{validate:!1})),Object.entries(c)))Array.isArray(n)?c[r]=n.map(t=>d(o(t),e.params)):"string"==typeof n&&(c[r]=d(o(n),e.params));let x=Object.keys(e.params).filter(e=>"nextInternalLocale"!==e);if(e.appendParamsToQuery&&!x.some(e=>p.includes(e)))for(let t of x)t in c||(c[t]=e.params[t]);if((0,i.isInterceptionRouteAppPath)(m))for(let t of m.split("/")){let r=i.INTERCEPTION_ROUTE_MARKERS.find(e=>t.startsWith(e));if(r){"(..)(..)"===r?(e.params["0"]="(..)",e.params["1"]="(..)"):e.params["0"]=r;break}}try{let[s,n]=(r=h(e.params)).split("#",2);t&&(a.hostname=t(e.params)),a.pathname=s,a.hash=(n?"#":"")+(n||""),delete a.search}catch(e){if(e.message.match(/Expected .*? to not repeat, but got an array/))throw Object.defineProperty(Error("To use a multi-match in the destination you must add `*` at the end of the param name to signify it should repeat. https://nextjs.org/docs/messages/invalid-multi-match"),"__NEXT_ERROR_CODE",{value:"E329",enumerable:!1,configurable:!0});throw e}return a.query={...n,...a.query},{newUrl:r,destQuery:c,parsedDestination:a}}},5531:(e,t)=>{"use strict";function r(e){return e.startsWith("/")?e:"/"+e}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"ensureLeadingSlash",{enumerable:!0,get:function(){return r}})},5913:(e,t,r)=>{"use strict";r(6397);var s=r(3210),n=function(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}(s),a="undefined"!=typeof process&&process.env&&!0,i=function(e){return"[object String]"===Object.prototype.toString.call(e)},l=function(){function e(e){var t=void 0===e?{}:e,r=t.name,s=void 0===r?"stylesheet":r,n=t.optimizeForSpeed,l=void 0===n?a:n;o(i(s),"`name` must be a string"),this._name=s,this._deletedRulePlaceholder="#"+s+"-deleted-rule____{}",o("boolean"==typeof l,"`optimizeForSpeed` must be a boolean"),this._optimizeForSpeed=l,this._serverSheet=void 0,this._tags=[],this._injected=!1,this._rulesCount=0,this._nonce=null}var t,r=e.prototype;return r.setOptimizeForSpeed=function(e){o("boolean"==typeof e,"`setOptimizeForSpeed` accepts a boolean"),o(0===this._rulesCount,"optimizeForSpeed cannot be when rules have already been inserted"),this.flush(),this._optimizeForSpeed=e,this.inject()},r.isOptimizeForSpeed=function(){return this._optimizeForSpeed},r.inject=function(){var e=this;o(!this._injected,"sheet already injected"),this._injected=!0,this._serverSheet={cssRules:[],insertRule:function(t,r){return"number"==typeof r?e._serverSheet.cssRules[r]={cssText:t}:e._serverSheet.cssRules.push({cssText:t}),r},deleteRule:function(t){e._serverSheet.cssRules[t]=null}}},r.getSheetForTag=function(e){if(e.sheet)return e.sheet;for(var t=0;t<document.styleSheets.length;t++)if(document.styleSheets[t].ownerNode===e)return document.styleSheets[t]},r.getSheet=function(){return this.getSheetForTag(this._tags[this._tags.length-1])},r.insertRule=function(e,t){return o(i(e),"`insertRule` accepts only strings"),"number"!=typeof t&&(t=this._serverSheet.cssRules.length),this._serverSheet.insertRule(e,t),this._rulesCount++},r.replaceRule=function(e,t){this._optimizeForSpeed;var r=this._serverSheet;if(t.trim()||(t=this._deletedRulePlaceholder),!r.cssRules[e])return e;r.deleteRule(e);try{r.insertRule(t,e)}catch(s){a||console.warn("StyleSheet: illegal rule: \n\n"+t+"\n\nSee https://stackoverflow.com/q/20007992 for more info"),r.insertRule(this._deletedRulePlaceholder,e)}return e},r.deleteRule=function(e){this._serverSheet.deleteRule(e)},r.flush=function(){this._injected=!1,this._rulesCount=0,this._serverSheet.cssRules=[]},r.cssRules=function(){return this._serverSheet.cssRules},r.makeStyleTag=function(e,t,r){t&&o(i(t),"makeStyleTag accepts only strings as second parameter");var s=document.createElement("style");this._nonce&&s.setAttribute("nonce",this._nonce),s.type="text/css",s.setAttribute("data-"+e,""),t&&s.appendChild(document.createTextNode(t));var n=document.head||document.getElementsByTagName("head")[0];return r?n.insertBefore(s,r):n.appendChild(s),s},t=[{key:"length",get:function(){return this._rulesCount}}],function(e,t){for(var r=0;r<t.length;r++){var s=t[r];s.enumerable=s.enumerable||!1,s.configurable=!0,"value"in s&&(s.writable=!0),Object.defineProperty(e,s.key,s)}}(e.prototype,t),e}();function o(e,t){if(!e)throw Error("StyleSheet: "+t+".")}var c=function(e){for(var t=5381,r=e.length;r;)t=33*t^e.charCodeAt(--r);return t>>>0},d={};function u(e,t){if(!t)return"jsx-"+e;var r=String(t),s=e+r;return d[s]||(d[s]="jsx-"+c(e+"-"+r)),d[s]}function m(e,t){var r=e+(t=t.replace(/\/style/gi,"\\/style"));return d[r]||(d[r]=t.replace(/__jsx-style-dynamic-selector/g,e)),d[r]}var p=function(){function e(e){var t=void 0===e?{}:e,r=t.styleSheet,s=void 0===r?null:r,n=t.optimizeForSpeed,a=void 0!==n&&n;this._sheet=s||new l({name:"styled-jsx",optimizeForSpeed:a}),this._sheet.inject(),s&&"boolean"==typeof a&&(this._sheet.setOptimizeForSpeed(a),this._optimizeForSpeed=this._sheet.isOptimizeForSpeed()),this._fromServer=void 0,this._indices={},this._instancesCounts={}}var t=e.prototype;return t.add=function(e){var t=this;void 0===this._optimizeForSpeed&&(this._optimizeForSpeed=Array.isArray(e.children),this._sheet.setOptimizeForSpeed(this._optimizeForSpeed),this._optimizeForSpeed=this._sheet.isOptimizeForSpeed());var r=this.getIdAndRules(e),s=r.styleId,n=r.rules;if(s in this._instancesCounts){this._instancesCounts[s]+=1;return}var a=n.map(function(e){return t._sheet.insertRule(e)}).filter(function(e){return -1!==e});this._indices[s]=a,this._instancesCounts[s]=1},t.remove=function(e){var t=this,r=this.getIdAndRules(e).styleId;if(function(e,t){if(!e)throw Error("StyleSheetRegistry: "+t+".")}(r in this._instancesCounts,"styleId: `"+r+"` not found"),this._instancesCounts[r]-=1,this._instancesCounts[r]<1){var s=this._fromServer&&this._fromServer[r];s?(s.parentNode.removeChild(s),delete this._fromServer[r]):(this._indices[r].forEach(function(e){return t._sheet.deleteRule(e)}),delete this._indices[r]),delete this._instancesCounts[r]}},t.update=function(e,t){this.add(t),this.remove(e)},t.flush=function(){this._sheet.flush(),this._sheet.inject(),this._fromServer=void 0,this._indices={},this._instancesCounts={}},t.cssRules=function(){var e=this,t=this._fromServer?Object.keys(this._fromServer).map(function(t){return[t,e._fromServer[t]]}):[],r=this._sheet.cssRules();return t.concat(Object.keys(this._indices).map(function(t){return[t,e._indices[t].map(function(e){return r[e].cssText}).join(e._optimizeForSpeed?"":"\n")]}).filter(function(e){return!!e[1]}))},t.styles=function(e){var t,r;return t=this.cssRules(),void 0===(r=e)&&(r={}),t.map(function(e){var t=e[0],s=e[1];return n.default.createElement("style",{id:"__"+t,key:"__"+t,nonce:r.nonce?r.nonce:void 0,dangerouslySetInnerHTML:{__html:s}})})},t.getIdAndRules=function(e){var t=e.children,r=e.dynamic,s=e.id;if(r){var n=u(s,r);return{styleId:n,rules:Array.isArray(t)?t.map(function(e){return m(n,e)}):[m(n,t)]}}return{styleId:u(s),rules:Array.isArray(t)?t:[t]}},t.selectFromServer=function(){return Array.prototype.slice.call(document.querySelectorAll('[id^="__jsx-"]')).reduce(function(e,t){return e[t.id.slice(2)]=t,e},{})},e}(),f=s.createContext(null);f.displayName="StyleSheetContext";n.default.useInsertionEffect||n.default.useLayoutEffect;var h=void 0;function x(e){var t=h||s.useContext(f);return t&&t.add(e),null}x.dynamic=function(e){return e.map(function(e){return u(e[0],e[1])}).join(" ")},t.style=x},6180:(e,t,r)=>{"use strict";e.exports=r(5913).style},6341:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{getPreviouslyRevalidatedTags:function(){return g},getUtils:function(){return x},interpolateDynamicPath:function(){return f},normalizeDynamicRouteParams:function(){return h},normalizeVercelUrl:function(){return p}});let s=r(9551),n=r(1959),a=r(2437),i=r(4396),l=r(8034),o=r(5526),c=r(2887),d=r(4722),u=r(6143),m=r(7912);function p(e,t,r){let n=(0,s.parse)(e.url,!0);for(let e of(delete n.search,Object.keys(n.query))){let s=e!==u.NEXT_QUERY_PARAM_PREFIX&&e.startsWith(u.NEXT_QUERY_PARAM_PREFIX),a=e!==u.NEXT_INTERCEPTION_MARKER_PREFIX&&e.startsWith(u.NEXT_INTERCEPTION_MARKER_PREFIX);(s||a||t.includes(e)||r&&Object.keys(r.groups).includes(e))&&delete n.query[e]}e.url=(0,s.format)(n)}function f(e,t,r){if(!r)return e;for(let s of Object.keys(r.groups)){let n,{optional:a,repeat:i}=r.groups[s],l=`[${i?"...":""}${s}]`;a&&(l=`[${l}]`);let o=t[s];n=Array.isArray(o)?o.map(e=>e&&encodeURIComponent(e)).join("/"):o?encodeURIComponent(o):"",e=e.replaceAll(l,n)}return e}function h(e,t,r,s){let n={};for(let a of Object.keys(t.groups)){let i=e[a];"string"==typeof i?i=(0,d.normalizeRscURL)(i):Array.isArray(i)&&(i=i.map(d.normalizeRscURL));let l=r[a],o=t.groups[a].optional;if((Array.isArray(l)?l.some(e=>Array.isArray(i)?i.some(t=>t.includes(e)):null==i?void 0:i.includes(e)):null==i?void 0:i.includes(l))||void 0===i&&!(o&&s))return{params:{},hasValidParams:!1};o&&(!i||Array.isArray(i)&&1===i.length&&("index"===i[0]||i[0]===`[[...${a}]]`))&&(i=void 0,delete e[a]),i&&"string"==typeof i&&t.groups[a].repeat&&(i=i.split("/")),i&&(n[a]=i)}return{params:n,hasValidParams:!0}}function x({page:e,i18n:t,basePath:r,rewrites:s,pageIsDynamic:d,trailingSlash:u,caseSensitive:x}){let g,b,v;return d&&(g=(0,i.getNamedRouteRegex)(e,{prefixRouteKeys:!1}),v=(b=(0,l.getRouteMatcher)(g))(e)),{handleRewrites:function(i,l){let m={},p=l.pathname,f=s=>{let c=(0,a.getPathMatch)(s.source+(u?"(/)?":""),{removeUnnamedParams:!0,strict:!0,sensitive:!!x});if(!l.pathname)return!1;let f=c(l.pathname);if((s.has||s.missing)&&f){let e=(0,o.matchHas)(i,l.query,s.has,s.missing);e?Object.assign(f,e):f=!1}if(f){let{parsedDestination:a,destQuery:i}=(0,o.prepareDestination)({appendParamsToQuery:!0,destination:s.destination,params:f,query:l.query});if(a.protocol)return!0;if(Object.assign(m,i,f),Object.assign(l.query,a.query),delete a.query,Object.assign(l,a),!(p=l.pathname))return!1;if(r&&(p=p.replace(RegExp(`^${r}`),"")||"/"),t){let e=(0,n.normalizeLocalePath)(p,t.locales);p=e.pathname,l.query.nextInternalLocale=e.detectedLocale||f.nextInternalLocale}if(p===e)return!0;if(d&&b){let e=b(p);if(e)return l.query={...l.query,...e},!0}}return!1};for(let e of s.beforeFiles||[])f(e);if(p!==e){let t=!1;for(let e of s.afterFiles||[])if(t=f(e))break;if(!t&&!(()=>{let t=(0,c.removeTrailingSlash)(p||"");return t===(0,c.removeTrailingSlash)(e)||(null==b?void 0:b(t))})()){for(let e of s.fallback||[])if(t=f(e))break}}return m},defaultRouteRegex:g,dynamicRouteMatcher:b,defaultRouteMatches:v,getParamsFromRouteMatches:function(e){if(!g)return null;let{groups:t,routeKeys:r}=g,s=(0,l.getRouteMatcher)({re:{exec:e=>{let s=Object.fromEntries(new URLSearchParams(e));for(let[e,t]of Object.entries(s)){let r=(0,m.normalizeNextQueryParam)(e);r&&(s[r]=t,delete s[e])}let n={};for(let e of Object.keys(r)){let a=r[e];if(!a)continue;let i=t[a],l=s[e];if(!i.optional&&!l)return null;n[i.pos]=l}return n}},groups:t})(e);return s||null},normalizeDynamicRouteParams:(e,t)=>g&&v?h(e,g,v,t):{params:{},hasValidParams:!1},normalizeVercelUrl:(e,t)=>p(e,t,g),interpolateDynamicPath:(e,t)=>f(e,t,g)}}function g(e,t){return"string"==typeof e[u.NEXT_CACHE_REVALIDATED_TAGS_HEADER]&&e[u.NEXT_CACHE_REVALIDATE_TAG_TOKEN_HEADER]===t?e[u.NEXT_CACHE_REVALIDATED_TAGS_HEADER].split(","):[]}},6397:()=>{},6415:e=>{(()=>{"use strict";"undefined"!=typeof __nccwpck_require__&&(__nccwpck_require__.ab=__dirname+"/");var t={};(()=>{t.parse=function(t,r){if("string"!=typeof t)throw TypeError("argument str must be a string");for(var n={},a=t.split(s),i=(r||{}).decode||e,l=0;l<a.length;l++){var o=a[l],c=o.indexOf("=");if(!(c<0)){var d=o.substr(0,c).trim(),u=o.substr(++c,o.length).trim();'"'==u[0]&&(u=u.slice(1,-1)),void 0==n[d]&&(n[d]=function(e,t){try{return t(e)}catch(t){return e}}(u,i))}}return n},t.serialize=function(e,t,s){var a=s||{},i=a.encode||r;if("function"!=typeof i)throw TypeError("option encode is invalid");if(!n.test(e))throw TypeError("argument name is invalid");var l=i(t);if(l&&!n.test(l))throw TypeError("argument val is invalid");var o=e+"="+l;if(null!=a.maxAge){var c=a.maxAge-0;if(isNaN(c)||!isFinite(c))throw TypeError("option maxAge is invalid");o+="; Max-Age="+Math.floor(c)}if(a.domain){if(!n.test(a.domain))throw TypeError("option domain is invalid");o+="; Domain="+a.domain}if(a.path){if(!n.test(a.path))throw TypeError("option path is invalid");o+="; Path="+a.path}if(a.expires){if("function"!=typeof a.expires.toUTCString)throw TypeError("option expires is invalid");o+="; Expires="+a.expires.toUTCString()}if(a.httpOnly&&(o+="; HttpOnly"),a.secure&&(o+="; Secure"),a.sameSite)switch("string"==typeof a.sameSite?a.sameSite.toLowerCase():a.sameSite){case!0:case"strict":o+="; SameSite=Strict";break;case"lax":o+="; SameSite=Lax";break;case"none":o+="; SameSite=None";break;default:throw TypeError("option sameSite is invalid")}return o};var e=decodeURIComponent,r=encodeURIComponent,s=/; */,n=/^[\u0009\u0020-\u007e\u0080-\u00ff]+$/})(),e.exports=t})()},6533:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"Image",{enumerable:!0,get:function(){return y}});let s=r(4985),n=r(740),a=r(687),i=n._(r(3210)),l=s._(r(1215)),o=s._(r(512)),c=r(4953),d=r(2756),u=r(7903);r(148);let m=r(9148),p=s._(r(1933)),f=r(3038),h={deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!1};function x(e,t,r,s,n,a,i){let l=null==e?void 0:e.src;e&&e["data-loaded-src"]!==l&&(e["data-loaded-src"]=l,("decode"in e?e.decode():Promise.resolve()).catch(()=>{}).then(()=>{if(e.parentElement&&e.isConnected){if("empty"!==t&&n(!0),null==r?void 0:r.current){let t=new Event("load");Object.defineProperty(t,"target",{writable:!1,value:e});let s=!1,n=!1;r.current({...t,nativeEvent:t,currentTarget:e,target:e,isDefaultPrevented:()=>s,isPropagationStopped:()=>n,persist:()=>{},preventDefault:()=>{s=!0,t.preventDefault()},stopPropagation:()=>{n=!0,t.stopPropagation()}})}(null==s?void 0:s.current)&&s.current(e)}}))}function g(e){return i.use?{fetchPriority:e}:{fetchpriority:e}}globalThis.__NEXT_IMAGE_IMPORTED=!0;let b=(0,i.forwardRef)((e,t)=>{let{src:r,srcSet:s,sizes:n,height:l,width:o,decoding:c,className:d,style:u,fetchPriority:m,placeholder:p,loading:h,unoptimized:b,fill:v,onLoadRef:y,onLoadingCompleteRef:j,setBlurComplete:N,setShowAltText:w,sizesInput:E,onLoad:_,onError:P,...R}=e,S=(0,i.useCallback)(e=>{e&&(P&&(e.src=e.src),e.complete&&x(e,p,y,j,N,b,E))},[r,p,y,j,N,P,b,E]),C=(0,f.useMergedRef)(t,S);return(0,a.jsx)("img",{...R,...g(m),loading:h,width:o,height:l,decoding:c,"data-nimg":v?"fill":"1",className:d,style:u,sizes:n,srcSet:s,src:r,ref:C,onLoad:e=>{x(e.currentTarget,p,y,j,N,b,E)},onError:e=>{w(!0),"empty"!==p&&N(!0),P&&P(e)}})});function v(e){let{isAppRouter:t,imgAttributes:r}=e,s={as:"image",imageSrcSet:r.srcSet,imageSizes:r.sizes,crossOrigin:r.crossOrigin,referrerPolicy:r.referrerPolicy,...g(r.fetchPriority)};return t&&l.default.preload?(l.default.preload(r.src,s),null):(0,a.jsx)(o.default,{children:(0,a.jsx)("link",{rel:"preload",href:r.srcSet?void 0:r.src,...s},"__nimg-"+r.src+r.srcSet+r.sizes)})}let y=(0,i.forwardRef)((e,t)=>{let r=(0,i.useContext)(m.RouterContext),s=(0,i.useContext)(u.ImageConfigContext),n=(0,i.useMemo)(()=>{var e;let t=h||s||d.imageConfigDefault,r=[...t.deviceSizes,...t.imageSizes].sort((e,t)=>e-t),n=t.deviceSizes.sort((e,t)=>e-t),a=null==(e=t.qualities)?void 0:e.sort((e,t)=>e-t);return{...t,allSizes:r,deviceSizes:n,qualities:a}},[s]),{onLoad:l,onLoadingComplete:o}=e,f=(0,i.useRef)(l);(0,i.useEffect)(()=>{f.current=l},[l]);let x=(0,i.useRef)(o);(0,i.useEffect)(()=>{x.current=o},[o]);let[g,y]=(0,i.useState)(!1),[j,N]=(0,i.useState)(!1),{props:w,meta:E}=(0,c.getImgProps)(e,{defaultLoader:p.default,imgConf:n,blurComplete:g,showAltText:j});return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(b,{...w,unoptimized:E.unoptimized,placeholder:E.placeholder,fill:E.fill,onLoadRef:f,onLoadingCompleteRef:x,setBlurComplete:y,setShowAltText:N,sizesInput:e.sizes,ref:t}),E.priority?(0,a.jsx)(v,{isAppRouter:!r,imgAttributes:w}):null]})});("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},6759:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"parseUrl",{enumerable:!0,get:function(){return a}});let s=r(2785),n=r(3736);function a(e){if(e.startsWith("/"))return(0,n.parseRelativeUrl)(e);let t=new URL(e);return{hash:t.hash,hostname:t.hostname,href:t.href,pathname:t.pathname,port:t.port,protocol:t.protocol,query:(0,s.searchParamsToUrlQuery)(t.searchParams),search:t.search}}},7270:(e,t,r)=>{Promise.resolve().then(r.bind(r,963))},7755:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return i}});let s=r(3210),n=()=>{},a=()=>{};function i(e){var t;let{headManager:r,reduceComponentsToState:i}=e;function l(){if(r&&r.mountedInstances){let t=s.Children.toArray(Array.from(r.mountedInstances).filter(Boolean));r.updateHead(i(t,e))}}return null==r||null==(t=r.mountedInstances)||t.add(e.children),l(),n(()=>{var t;return null==r||null==(t=r.mountedInstances)||t.add(e.children),()=>{var t;null==r||null==(t=r.mountedInstances)||t.delete(e.children)}}),n(()=>(r&&(r._pendingUpdate=l),()=>{r&&(r._pendingUpdate=l)})),a(()=>(r&&r._pendingUpdate&&(r._pendingUpdate(),r._pendingUpdate=null),()=>{r&&r._pendingUpdate&&(r._pendingUpdate(),r._pendingUpdate=null)})),null}},7903:(e,t,r)=>{"use strict";e.exports=r(4041).vendored.contexts.ImageConfigContext},8034:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getRouteMatcher",{enumerable:!0,get:function(){return n}});let s=r(4827);function n(e){let{re:t,groups:r}=e;return e=>{let n=t.exec(e);if(!n)return!1;let a=e=>{try{return decodeURIComponent(e)}catch(e){throw Object.defineProperty(new s.DecodeError("failed to decode param"),"__NEXT_ERROR_CODE",{value:"E528",enumerable:!1,configurable:!0})}},i={};for(let[e,t]of Object.entries(r)){let r=n[t.pos];void 0!==r&&(t.repeat?i[e]=r.split("/").map(e=>a(e)):i[e]=a(r))}return i}}},8212:(e,t,r)=>{"use strict";function s(e){return function(){let{cookie:t}=e;if(!t)return{};let{parse:s}=r(6415);return s(Array.isArray(t)?t.join("; "):t)}}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getCookieParser",{enumerable:!0,get:function(){return s}})},8304:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{DEFAULT_METADATA_ROUTE_EXTENSIONS:function(){return l},STATIC_METADATA_IMAGES:function(){return i},getExtensionRegexString:function(){return o},isMetadataPage:function(){return u},isMetadataRoute:function(){return m},isMetadataRouteFile:function(){return c},isStaticMetadataRoute:function(){return d}});let s=r(2958),n=r(4722),a=r(554),i={icon:{filename:"icon",extensions:["ico","jpg","jpeg","png","svg"]},apple:{filename:"apple-icon",extensions:["jpg","jpeg","png"]},favicon:{filename:"favicon",extensions:["ico"]},openGraph:{filename:"opengraph-image",extensions:["jpg","jpeg","png","gif"]},twitter:{filename:"twitter-image",extensions:["jpg","jpeg","png","gif"]}},l=["js","jsx","ts","tsx"],o=(e,t)=>t&&0!==t.length?`(?:\\.(${e.join("|")})|(\\.(${t.join("|")})))`:`(\\.(?:${e.join("|")}))`;function c(e,t,r){let n=(r?"":"?")+"$",a=`\\d?${r?"":"(-\\w{6})?"}`,l=[RegExp(`^[\\\\/]robots${o(t.concat("txt"),null)}${n}`),RegExp(`^[\\\\/]manifest${o(t.concat("webmanifest","json"),null)}${n}`),RegExp("^[\\\\/]favicon\\.ico$"),RegExp(`[\\\\/]sitemap${o(["xml"],t)}${n}`),RegExp(`[\\\\/]${i.icon.filename}${a}${o(i.icon.extensions,t)}${n}`),RegExp(`[\\\\/]${i.apple.filename}${a}${o(i.apple.extensions,t)}${n}`),RegExp(`[\\\\/]${i.openGraph.filename}${a}${o(i.openGraph.extensions,t)}${n}`),RegExp(`[\\\\/]${i.twitter.filename}${a}${o(i.twitter.extensions,t)}${n}`)],c=(0,s.normalizePathSep)(e);return l.some(e=>e.test(c))}function d(e){let t=e.replace(/\/route$/,"");return(0,a.isAppRouteRoute)(e)&&c(t,[],!0)&&"/robots.txt"!==t&&"/manifest.webmanifest"!==t&&!t.endsWith("/sitemap.xml")}function u(e){return!(0,a.isAppRouteRoute)(e)&&c(e,[],!1)}function m(e){let t=(0,n.normalizeAppPath)(e).replace(/^\/?app\//,"").replace("/[__metadata_id__]","").replace(/\/route$/,"");return"/"!==t[0]&&(t="/"+t),(0,a.isAppRouteRoute)(e)&&c(t,[],!1)}},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9131:(e,t,r)=>{"use strict";r.d(t,{AuthProvider:()=>n});var s=r(2907);let n=(0,s.registerClientReference)(function(){throw Error("Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/contexts/AuthContext.tsx","AuthProvider");(0,s.registerClientReference)(function(){throw Error("Attempted to call useAuth() from the server but useAuth is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/contexts/AuthContext.tsx","useAuth")},9148:(e,t,r)=>{"use strict";e.exports=r(4041).vendored.contexts.RouterContext},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9513:(e,t,r)=>{"use strict";e.exports=r(4041).vendored.contexts.HeadManagerContext},9551:e=>{"use strict";e.exports=require("url")}};var t=require("../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[447,825],()=>r(1879));module.exports=s})();