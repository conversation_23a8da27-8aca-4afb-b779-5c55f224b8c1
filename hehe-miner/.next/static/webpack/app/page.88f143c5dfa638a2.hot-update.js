"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/MiningScreen.tsx":
/*!*****************************************!*\
  !*** ./src/components/MiningScreen.tsx ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ MiningScreen)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./src/lib/api.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction MiningScreen() {\n    var _user_totalBalance, _user_miningPower;\n    _s();\n    const { user, updateUser } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [timeRemaining, setTimeRemaining] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [canClaim, setCanClaim] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [success, setSuccess] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"MiningScreen.useEffect\": ()=>{\n            if (user === null || user === void 0 ? void 0 : user.currentMiningSession) {\n                const interval = setInterval({\n                    \"MiningScreen.useEffect.interval\": ()=>{\n                        updateMiningTimer();\n                    }\n                }[\"MiningScreen.useEffect.interval\"], 1000);\n                return ({\n                    \"MiningScreen.useEffect\": ()=>clearInterval(interval)\n                })[\"MiningScreen.useEffect\"];\n            }\n        }\n    }[\"MiningScreen.useEffect\"], [\n        user === null || user === void 0 ? void 0 : user.currentMiningSession\n    ]);\n    const updateMiningTimer = ()=>{\n        if (!(user === null || user === void 0 ? void 0 : user.currentMiningSession)) return;\n        const startTime = new Date(user.currentMiningSession.startTime).getTime();\n        const miningDuration = 4 * 60 * 60 * 1000 // 4 hours\n        ;\n        const endTime = startTime + miningDuration;\n        const now = Date.now();\n        const remaining = endTime - now;\n        if (remaining <= 0) {\n            setTimeRemaining(0);\n            setCanClaim(true);\n        } else {\n            setTimeRemaining(remaining);\n            setCanClaim(false);\n        }\n    };\n    const formatTime = (milliseconds)=>{\n        const hours = Math.floor(milliseconds / (1000 * 60 * 60));\n        const minutes = Math.floor(milliseconds % (1000 * 60 * 60) / (1000 * 60));\n        const seconds = Math.floor(milliseconds % (1000 * 60) / 1000);\n        return \"\".concat(hours.toString().padStart(2, '0'), \":\").concat(minutes.toString().padStart(2, '0'), \":\").concat(seconds.toString().padStart(2, '0'));\n    };\n    const handleStartMining = async ()=>{\n        if (!(user === null || user === void 0 ? void 0 : user.hasBasicPlan)) {\n            setError('You need to purchase the basic plan first!');\n            return;\n        }\n        setIsLoading(true);\n        setError('');\n        setSuccess('');\n        try {\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_3__.apiClient.startMining();\n            if (response.success) {\n                setSuccess('Mining started successfully!');\n                // Refresh user profile\n                const profileResponse = await _lib_api__WEBPACK_IMPORTED_MODULE_3__.apiClient.getUserProfile();\n                if (profileResponse.success) {\n                    updateUser(profileResponse.user);\n                }\n            } else {\n                setError(response.error || 'Failed to start mining');\n            }\n        } catch (error) {\n            setError('Network error occurred');\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const handleClaimTokens = async ()=>{\n        if (!(user === null || user === void 0 ? void 0 : user.currentMiningSession)) return;\n        setIsLoading(true);\n        setError('');\n        setSuccess('');\n        try {\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_3__.apiClient.claimMining(user.currentMiningSession.id);\n            if (response.success) {\n                setSuccess(\"Claimed \".concat(response.tokensEarned, \" HEHE tokens!\"));\n                // Refresh user profile\n                const profileResponse = await _lib_api__WEBPACK_IMPORTED_MODULE_3__.apiClient.getUserProfile();\n                if (profileResponse.success) {\n                    updateUser(profileResponse.user);\n                }\n            } else {\n                setError(response.error || 'Failed to claim tokens');\n            }\n        } catch (error) {\n            setError('Network error occurred');\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const getMiningProgress = ()=>{\n        if (!(user === null || user === void 0 ? void 0 : user.currentMiningSession) || timeRemaining <= 0) return 100;\n        const startTime = new Date(user.currentMiningSession.startTime).getTime();\n        const miningDuration = 4 * 60 * 60 * 1000 // 4 hours\n        ;\n        const elapsed = Date.now() - startTime;\n        return Math.min(elapsed / miningDuration * 100, 100);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"p-6 max-w-md mx-auto relative\",\n        children: [\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-red-500/20 border border-red-400/30 rounded-xl p-4 mb-6 glass animate-bounce-in\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-red-400 text-xl animate-bounce\",\n                            children: \"⚠️\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                            lineNumber: 122,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-red-300 font-medium\",\n                            children: error\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                            lineNumber: 123,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                    lineNumber: 121,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                lineNumber: 120,\n                columnNumber: 9\n            }, this),\n            success && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-green-500/20 border border-green-400/30 rounded-xl p-4 mb-6 glass animate-bounce-in\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-green-400 text-xl animate-bounce\",\n                            children: \"\\uD83C\\uDF89\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                            lineNumber: 131,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-green-300 font-medium\",\n                            children: success\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                            lineNumber: 132,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                    lineNumber: 130,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                lineNumber: 129,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"glass-dark rounded-3xl p-8 mb-8 border border-white/20 hover-lift animate-slide-up relative overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-gradient-to-r from-yellow-400/10 via-orange-500/10 to-red-500/10 animate-pulse\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                        lineNumber: 140,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center relative z-10\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-32 h-32 bg-gradient-to-r from-yellow-400 via-orange-500 to-red-500 rounded-full mx-auto flex items-center justify-center animate-pulse-glow relative\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-4xl animate-float\",\n                                                children: \"⛏️\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                                lineNumber: 145,\n                                                columnNumber: 15\n                                            }, this),\n                                            (user === null || user === void 0 ? void 0 : user.currentMiningSession) && timeRemaining > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute inset-0 rounded-full border-4 border-transparent border-t-white animate-spin\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                                lineNumber: 147,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                        lineNumber: 144,\n                                        columnNumber: 13\n                                    }, this),\n                                    canClaim && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute -top-2 -right-2 w-8 h-8 bg-gradient-to-r from-green-400 to-blue-500 rounded-full animate-ping\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                        lineNumber: 151,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                lineNumber: 143,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-3xl font-bold bg-gradient-to-r from-yellow-400 via-orange-500 to-red-500 bg-clip-text text-transparent mb-4\",\n                                children: \"Mining Status\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                lineNumber: 155,\n                                columnNumber: 11\n                            }, this),\n                            !(user === null || user === void 0 ? void 0 : user.hasBasicPlan) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center animate-slide-up\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-300 mb-6 text-lg\",\n                                        children: \"Purchase the basic plan to start mining! \\uD83D\\uDE80\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                        lineNumber: 161,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"glass rounded-2xl p-6 border border-yellow-400/30\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-center space-x-3 mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-3xl animate-bounce\",\n                                                        children: \"\\uD83D\\uDCA1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                                        lineNumber: 164,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-2xl animate-bounce\",\n                                                        style: {\n                                                            animationDelay: '0.2s'\n                                                        },\n                                                        children: \"⛏️\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                                        lineNumber: 165,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-3xl animate-bounce\",\n                                                        style: {\n                                                            animationDelay: '0.4s'\n                                                        },\n                                                        children: \"\\uD83D\\uDC8E\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                                        lineNumber: 166,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                                lineNumber: 163,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-yellow-300 font-bold text-lg mb-2\",\n                                                children: \"Basic Plan: $1\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                                lineNumber: 168,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-300\",\n                                                children: \"Unlock mining 4 HEHE tokens every 4 hours\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                                lineNumber: 171,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                        lineNumber: 162,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                lineNumber: 160,\n                                columnNumber: 13\n                            }, this) : (user === null || user === void 0 ? void 0 : user.currentMiningSession) && timeRemaining > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"animate-slide-up\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-300 mb-6 text-lg font-medium\",\n                                        children: \"⚡ Mining in progress... ⚡\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                        lineNumber: 178,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-gray-700/50 rounded-full h-4 mb-3 overflow-hidden\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-gradient-to-r from-yellow-400 via-orange-500 to-red-500 h-4 rounded-full transition-all duration-1000 relative\",\n                                                    style: {\n                                                        width: \"\".concat(getMiningProgress(), \"%\")\n                                                    },\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent shimmer\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                                        lineNumber: 185,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                                    lineNumber: 181,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                                lineNumber: 180,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-white font-semibold\",\n                                                children: [\n                                                    \"Progress: \",\n                                                    getMiningProgress().toFixed(1),\n                                                    \"% ⚡\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                                lineNumber: 188,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                        lineNumber: 179,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-800/80 backdrop-blur-sm rounded-2xl p-6 mb-4 border border-yellow-400/30\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-4xl font-mono text-yellow-400 mb-3 animate-pulse font-bold\",\n                                                children: formatTime(timeRemaining)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                                lineNumber: 193,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-white text-lg font-medium\",\n                                                children: \"Time remaining ⏰\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                                lineNumber: 196,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                        lineNumber: 192,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-center space-x-2 text-white\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-2xl animate-coin-flip\",\n                                                children: \"\\uD83D\\uDCB0\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                                lineNumber: 199,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-lg font-bold\",\n                                                children: [\n                                                    \"Earning: \",\n                                                    user.currentMiningSession.tokensEarned,\n                                                    \" HEHE\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                                lineNumber: 200,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-2xl animate-coin-flip\",\n                                                style: {\n                                                    animationDelay: '0.5s'\n                                                },\n                                                children: \"\\uD83D\\uDCB0\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                                lineNumber: 201,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                        lineNumber: 198,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                lineNumber: 177,\n                                columnNumber: 13\n                            }, this) : canClaim && (user === null || user === void 0 ? void 0 : user.currentMiningSession) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"animate-bounce-in\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold bg-gradient-to-r from-green-400 to-blue-500 bg-clip-text text-transparent mb-4\",\n                                                children: \"\\uD83C\\uDF89 Mining Complete! \\uD83C\\uDF89\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                                lineNumber: 207,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-gray-800/90 backdrop-blur-sm rounded-2xl p-6 mb-6 border border-green-400/30\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-center space-x-3 mb-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-3xl animate-bounce\",\n                                                                children: \"\\uD83D\\uDC8E\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                                                lineNumber: 212,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-2xl font-bold text-white\",\n                                                                children: [\n                                                                    user.currentMiningSession.tokensEarned,\n                                                                    \" HEHE\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                                                lineNumber: 213,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-3xl animate-bounce\",\n                                                                style: {\n                                                                    animationDelay: '0.3s'\n                                                                },\n                                                                children: \"\\uD83D\\uDC8E\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                                                lineNumber: 214,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                                        lineNumber: 211,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-white\",\n                                                        children: \"Ready to claim your rewards!\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                                        lineNumber: 216,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                                lineNumber: 210,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                        lineNumber: 206,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleClaimTokens,\n                                        disabled: isLoading,\n                                        className: \"group w-full bg-gradient-to-r from-green-500 via-green-600 to-blue-600 hover:from-green-600 hover:via-green-700 hover:to-blue-700 disabled:from-gray-600 disabled:via-gray-700 disabled:to-gray-800 text-white font-bold py-4 px-6 rounded-xl transition-all duration-300 transform hover:scale-105 disabled:transform-none shadow-lg hover:shadow-green-500/25 relative overflow-hidden\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 shimmer\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                                lineNumber: 224,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"relative z-10 text-lg\",\n                                                children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-center space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"animate-spin rounded-full h-5 w-5 border-2 border-white border-t-transparent\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                                            lineNumber: 228,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"Claiming...\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                                            lineNumber: 229,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                                    lineNumber: 227,\n                                                    columnNumber: 21\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-center space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"group-hover:animate-bounce\",\n                                                            children: \"\\uD83D\\uDCB0\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                                            lineNumber: 233,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"Claim Tokens\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                                            lineNumber: 234,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"group-hover:animate-bounce\",\n                                                            children: \"\\uD83D\\uDCB0\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                                            lineNumber: 235,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                                    lineNumber: 232,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                                lineNumber: 225,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                        lineNumber: 219,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                lineNumber: 205,\n                                columnNumber: 13\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"animate-slide-up\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-300 mb-6 text-lg font-medium\",\n                                        children: \"\\uD83D\\uDE80 Ready to mine! \\uD83D\\uDE80\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                        lineNumber: 243,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-800/90 backdrop-blur-sm rounded-2xl p-6 mb-6 border border-blue-400/30\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-center space-x-3 mb-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-2xl animate-pulse\",\n                                                        children: \"⚡\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                                        lineNumber: 246,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-lg font-bold text-white\",\n                                                        children: [\n                                                            \"Mining Power: \",\n                                                            user === null || user === void 0 ? void 0 : user.miningPower,\n                                                            \" HEHE/4h\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                                        lineNumber: 247,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-2xl animate-pulse\",\n                                                        style: {\n                                                            animationDelay: '0.5s'\n                                                        },\n                                                        children: \"⚡\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                                        lineNumber: 248,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                                lineNumber: 245,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-white\",\n                                                children: \"Start your mining session now!\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                                lineNumber: 250,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                        lineNumber: 244,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleStartMining,\n                                        disabled: isLoading,\n                                        className: \"group w-full bg-gradient-to-r from-yellow-500 via-orange-500 to-red-500 hover:from-yellow-600 hover:via-orange-600 hover:to-red-600 disabled:from-gray-600 disabled:via-gray-700 disabled:to-gray-800 text-white font-bold py-4 px-6 rounded-xl transition-all duration-300 transform hover:scale-105 disabled:transform-none shadow-lg hover:shadow-yellow-500/25 relative overflow-hidden\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 shimmer\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                                lineNumber: 257,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"relative z-10 text-lg\",\n                                                children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-center space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"animate-spin rounded-full h-5 w-5 border-2 border-white border-t-transparent\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                                            lineNumber: 261,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"Starting...\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                                            lineNumber: 262,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                                    lineNumber: 260,\n                                                    columnNumber: 21\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-center space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"group-hover:animate-bounce\",\n                                                            children: \"⛏️\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                                            lineNumber: 266,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"Start Mining\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                                            lineNumber: 267,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"group-hover:animate-bounce\",\n                                                            children: \"⛏️\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                                            lineNumber: 268,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                                    lineNumber: 265,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                                lineNumber: 258,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                        lineNumber: 252,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                lineNumber: 242,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                        lineNumber: 142,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                lineNumber: 138,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-2 gap-4 animate-slide-up\",\n                style: {\n                    animationDelay: '0.3s'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gray-800/90 backdrop-blur-sm rounded-2xl p-6 border border-yellow-400/30 hover-lift group\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2 mb-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-yellow-400 text-xl group-hover:animate-coin-flip\",\n                                        children: \"\\uD83D\\uDCB0\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                        lineNumber: 282,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-white font-medium\",\n                                        children: \"Total Balance\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                        lineNumber: 283,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                lineNumber: 281,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-2xl font-bold text-yellow-400\",\n                                children: [\n                                    (user === null || user === void 0 ? void 0 : (_user_totalBalance = user.totalBalance) === null || _user_totalBalance === void 0 ? void 0 : _user_totalBalance.toFixed(2)) || '0.00',\n                                    \" HEHE\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                lineNumber: 285,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                        lineNumber: 280,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gray-800/90 backdrop-blur-sm rounded-2xl p-6 border border-blue-400/30 hover-lift group\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2 mb-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-blue-400 text-xl group-hover:animate-pulse\",\n                                        children: \"⚡\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                        lineNumber: 291,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-white font-medium\",\n                                        children: \"Mining Power\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                        lineNumber: 292,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                lineNumber: 290,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-2xl font-bold text-blue-400\",\n                                children: [\n                                    (user === null || user === void 0 ? void 0 : (_user_miningPower = user.miningPower) === null || _user_miningPower === void 0 ? void 0 : _user_miningPower.toFixed(2)) || '4.00',\n                                    \"/4h\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                lineNumber: 294,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                        lineNumber: 289,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                lineNumber: 279,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n        lineNumber: 117,\n        columnNumber: 5\n    }, this);\n}\n_s(MiningScreen, \"QrLVhiFnbJ7nKmpdWPloWf9fPJg=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth\n    ];\n});\n_c = MiningScreen;\nvar _c;\n$RefreshReg$(_c, \"MiningScreen\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/MiningScreen.tsx\n"));

/***/ })

});