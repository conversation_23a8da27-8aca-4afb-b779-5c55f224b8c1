"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/LandingPage.tsx":
/*!****************************************!*\
  !*** ./src/components/LandingPage.tsx ***!
  \****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ LandingPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-jsx/style */ \"(app-pages-browser)/./node_modules/styled-jsx/style.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./src/lib/api.ts\");\n/* harmony import */ var _lib_telegram__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/telegram */ \"(app-pages-browser)/./src/lib/telegram.ts\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction LandingPage(param) {\n    let { onLaunchApp } = param;\n    _s();\n    const { login } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)('');\n    const [loadingText, setLoadingText] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)('Initializing...');\n    const loadingSteps = [\n        'Initializing...',\n        'Connecting to Telegram...',\n        'Authenticating user...',\n        'Loading user data...',\n        'Preparing mining interface...',\n        'Almost ready...'\n    ];\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"LandingPage.useEffect\": ()=>{\n            // Initialize Telegram Web App\n            (0,_lib_telegram__WEBPACK_IMPORTED_MODULE_5__.initTelegramWebApp)();\n        }\n    }[\"LandingPage.useEffect\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"LandingPage.useEffect\": ()=>{\n            if (isLoading) {\n                let stepIndex = 0;\n                const interval = setInterval({\n                    \"LandingPage.useEffect.interval\": ()=>{\n                        stepIndex = (stepIndex + 1) % loadingSteps.length;\n                        setLoadingText(loadingSteps[stepIndex]);\n                    }\n                }[\"LandingPage.useEffect.interval\"], 800);\n                return ({\n                    \"LandingPage.useEffect\": ()=>clearInterval(interval)\n                })[\"LandingPage.useEffect\"];\n            }\n        }\n    }[\"LandingPage.useEffect\"], [\n        isLoading\n    ]);\n    const handleLaunchApp = async ()=>{\n        setIsLoading(true);\n        setError('');\n        // Trigger haptic feedback\n        (0,_lib_telegram__WEBPACK_IMPORTED_MODULE_5__.triggerHapticFeedback)('impact', 'medium');\n        try {\n            // Simulate loading time for better UX\n            await new Promise((resolve)=>setTimeout(resolve, 1000));\n            // Try Telegram authentication first\n            if ((0,_lib_telegram__WEBPACK_IMPORTED_MODULE_5__.isTelegramWebApp)()) {\n                const telegramUser = (0,_lib_telegram__WEBPACK_IMPORTED_MODULE_5__.getTelegramUser)();\n                if (telegramUser) {\n                    const response = await _lib_api__WEBPACK_IMPORTED_MODULE_4__.apiClient.loginWithTelegram(telegramUser);\n                    if (response.success) {\n                        (0,_lib_telegram__WEBPACK_IMPORTED_MODULE_5__.triggerHapticFeedback)('notification', 'success');\n                        login(response.user, response.token);\n                        onLaunchApp();\n                        return;\n                    }\n                }\n            }\n            // Fallback to mock authentication for development\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_4__.apiClient.loginMock();\n            if (response.success) {\n                (0,_lib_telegram__WEBPACK_IMPORTED_MODULE_5__.triggerHapticFeedback)('notification', 'success');\n                login(response.user, response.token);\n                onLaunchApp();\n            } else {\n                (0,_lib_telegram__WEBPACK_IMPORTED_MODULE_5__.triggerHapticFeedback)('notification', 'error');\n                setError(response.error || 'Authentication failed');\n            }\n        } catch (error) {\n            (0,_lib_telegram__WEBPACK_IMPORTED_MODULE_5__.triggerHapticFeedback)('notification', 'error');\n            setError('Network error occurred. Please try again.');\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 flex items-center justify-center relative overflow-hidden\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute inset-0\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute top-20 left-20 w-32 h-32 bg-yellow-400 rounded-full opacity-20 animate-pulse\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/LandingPage.tsx\",\n                            lineNumber: 93,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute top-40 right-32 w-24 h-24 bg-orange-400 rounded-full opacity-20 animate-pulse delay-300\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/LandingPage.tsx\",\n                            lineNumber: 94,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute bottom-32 left-32 w-40 h-40 bg-yellow-300 rounded-full opacity-20 animate-pulse delay-700\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/LandingPage.tsx\",\n                            lineNumber: 95,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute bottom-20 right-20 w-28 h-28 bg-orange-300 rounded-full opacity-20 animate-pulse delay-500\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/LandingPage.tsx\",\n                            lineNumber: 96,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/LandingPage.tsx\",\n                    lineNumber: 92,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center z-10\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-8 animate-bounce\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                src: \"/hehe-logo.svg\",\n                                alt: \"Hehe Miner\",\n                                width: 120,\n                                height: 120,\n                                className: \"mx-auto drop-shadow-2xl\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/LandingPage.tsx\",\n                                lineNumber: 102,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/LandingPage.tsx\",\n                            lineNumber: 101,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-16 h-16 border-4 border-yellow-400 border-t-transparent rounded-full animate-spin mx-auto\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/LandingPage.tsx\",\n                                    lineNumber: 113,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute inset-0 w-16 h-16 border-4 border-orange-400 border-b-transparent rounded-full animate-spin mx-auto\",\n                                    style: {\n                                        animationDirection: 'reverse',\n                                        animationDuration: '1.5s'\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/LandingPage.tsx\",\n                                    lineNumber: 114,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/LandingPage.tsx\",\n                            lineNumber: 112,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-2xl font-bold text-white mb-2 animate-pulse\",\n                            children: loadingText\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/LandingPage.tsx\",\n                            lineNumber: 118,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-64 h-2 bg-gray-700 rounded-full mx-auto overflow-hidden\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-full bg-gradient-to-r from-yellow-400 to-orange-400 rounded-full animate-pulse\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/LandingPage.tsx\",\n                                lineNumber: 124,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/LandingPage.tsx\",\n                            lineNumber: 123,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-300 mt-4 text-sm\",\n                            children: \"Please wait while we prepare your mining experience...\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/LandingPage.tsx\",\n                            lineNumber: 127,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/LandingPage.tsx\",\n                    lineNumber: 99,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/LandingPage.tsx\",\n            lineNumber: 90,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"jsx-6bd0f39f565b36fe\" + \" \" + \"min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 flex items-center justify-center relative overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"jsx-6bd0f39f565b36fe\" + \" \" + \"absolute inset-0\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-6bd0f39f565b36fe\" + \" \" + \"absolute top-20 left-20 w-32 h-32 bg-yellow-400 rounded-full opacity-20 animate-float\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/LandingPage.tsx\",\n                        lineNumber: 139,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-6bd0f39f565b36fe\" + \" \" + \"absolute top-40 right-32 w-24 h-24 bg-orange-400 rounded-full opacity-20 animate-float delay-300\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/LandingPage.tsx\",\n                        lineNumber: 140,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-6bd0f39f565b36fe\" + \" \" + \"absolute bottom-32 left-32 w-40 h-40 bg-yellow-300 rounded-full opacity-20 animate-float delay-700\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/LandingPage.tsx\",\n                        lineNumber: 141,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-6bd0f39f565b36fe\" + \" \" + \"absolute bottom-20 right-20 w-28 h-28 bg-orange-300 rounded-full opacity-20 animate-float delay-500\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/LandingPage.tsx\",\n                        lineNumber: 142,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/LandingPage.tsx\",\n                lineNumber: 138,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"jsx-6bd0f39f565b36fe\" + \" \" + \"text-center z-10 max-w-md mx-auto px-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-6bd0f39f565b36fe\" + \" \" + \"mb-8 animate-bounce\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            src: \"/hehe-logo.svg\",\n                            alt: \"Hehe Miner\",\n                            width: 150,\n                            height: 150,\n                            className: \"mx-auto drop-shadow-2xl\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/LandingPage.tsx\",\n                            lineNumber: 148,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/LandingPage.tsx\",\n                        lineNumber: 147,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"jsx-6bd0f39f565b36fe\" + \" \" + \"text-5xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-yellow-400 to-orange-400 mb-4 animate-pulse\",\n                        children: \"HEHE MINER\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/LandingPage.tsx\",\n                        lineNumber: 158,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"jsx-6bd0f39f565b36fe\" + \" \" + \"text-xl text-gray-300 mb-8 leading-relaxed\",\n                        children: \"Start your crypto mining journey with the most fun and rewarding mining game!\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/LandingPage.tsx\",\n                        lineNumber: 162,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-6bd0f39f565b36fe\" + \" \" + \"mb-8 space-y-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-6bd0f39f565b36fe\" + \" \" + \"flex items-center justify-center text-gray-300\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"jsx-6bd0f39f565b36fe\" + \" \" + \"text-yellow-400 mr-2\",\n                                        children: \"⚡\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/LandingPage.tsx\",\n                                        lineNumber: 169,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"jsx-6bd0f39f565b36fe\",\n                                        children: \"Mine HEHE tokens every 4 hours\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/LandingPage.tsx\",\n                                        lineNumber: 170,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/LandingPage.tsx\",\n                                lineNumber: 168,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-6bd0f39f565b36fe\" + \" \" + \"flex items-center justify-center text-gray-300\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"jsx-6bd0f39f565b36fe\" + \" \" + \"text-yellow-400 mr-2\",\n                                        children: \"\\uD83D\\uDE80\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/LandingPage.tsx\",\n                                        lineNumber: 173,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"jsx-6bd0f39f565b36fe\",\n                                        children: \"Upgrade your mining power\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/LandingPage.tsx\",\n                                        lineNumber: 174,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/LandingPage.tsx\",\n                                lineNumber: 172,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-6bd0f39f565b36fe\" + \" \" + \"flex items-center justify-center text-gray-300\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"jsx-6bd0f39f565b36fe\" + \" \" + \"text-yellow-400 mr-2\",\n                                        children: \"\\uD83D\\uDC65\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/LandingPage.tsx\",\n                                        lineNumber: 177,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"jsx-6bd0f39f565b36fe\",\n                                        children: \"Refer friends and earn rewards\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/LandingPage.tsx\",\n                                        lineNumber: 178,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/LandingPage.tsx\",\n                                lineNumber: 176,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/LandingPage.tsx\",\n                        lineNumber: 167,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: handleLaunchApp,\n                        disabled: isLoading,\n                        className: \"jsx-6bd0f39f565b36fe\" + \" \" + \"w-full bg-gradient-to-r from-yellow-400 to-orange-400 text-black font-bold py-4 px-8 rounded-xl text-lg shadow-2xl transform transition-all duration-300 hover:scale-105 hover:shadow-yellow-400/50 disabled:opacity-50 disabled:cursor-not-allowed animate-pulse\",\n                        children: \"\\uD83D\\uDE80 Launch App\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/LandingPage.tsx\",\n                        lineNumber: 183,\n                        columnNumber: 9\n                    }, this),\n                    error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-6bd0f39f565b36fe\" + \" \" + \"mt-4 p-3 bg-red-500/20 border border-red-500 rounded-lg text-red-300 text-sm\",\n                        children: error\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/LandingPage.tsx\",\n                        lineNumber: 192,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"jsx-6bd0f39f565b36fe\" + \" \" + \"text-gray-400 text-xs mt-6\",\n                        children: \"Powered by Telegram • Secure • Decentralized\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/LandingPage.tsx\",\n                        lineNumber: 197,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/LandingPage.tsx\",\n                lineNumber: 145,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default()), {\n                id: \"6bd0f39f565b36fe\",\n                children: \"@-webkit-keyframes float{0%,100%{-webkit-transform:translatey(0px)rotate(0deg);transform:translatey(0px)rotate(0deg)}50%{-webkit-transform:translatey(-20px)rotate(180deg);transform:translatey(-20px)rotate(180deg)}}@-moz-keyframes float{0%,100%{-moz-transform:translatey(0px)rotate(0deg);transform:translatey(0px)rotate(0deg)}50%{-moz-transform:translatey(-20px)rotate(180deg);transform:translatey(-20px)rotate(180deg)}}@-o-keyframes float{0%,100%{-o-transform:translatey(0px)rotate(0deg);transform:translatey(0px)rotate(0deg)}50%{-o-transform:translatey(-20px)rotate(180deg);transform:translatey(-20px)rotate(180deg)}}@keyframes float{0%,100%{-webkit-transform:translatey(0px)rotate(0deg);-moz-transform:translatey(0px)rotate(0deg);-o-transform:translatey(0px)rotate(0deg);transform:translatey(0px)rotate(0deg)}50%{-webkit-transform:translatey(-20px)rotate(180deg);-moz-transform:translatey(-20px)rotate(180deg);-o-transform:translatey(-20px)rotate(180deg);transform:translatey(-20px)rotate(180deg)}}.animate-float.jsx-6bd0f39f565b36fe{-webkit-animation:float 6s ease-in-out infinite;-moz-animation:float 6s ease-in-out infinite;-o-animation:float 6s ease-in-out infinite;animation:float 6s ease-in-out infinite}\"\n            }, void 0, false, void 0, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/LandingPage.tsx\",\n        lineNumber: 136,\n        columnNumber: 5\n    }, this);\n}\n_s(LandingPage, \"+TIOeCDKPF1IF8zNlX3TPSLRBTo=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth\n    ];\n});\n_c = LandingPage;\nvar _c;\n$RefreshReg$(_c, \"LandingPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL0xhbmRpbmdQYWdlLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7O0FBRTJDO0FBQ0s7QUFDWDtBQUN3RTtBQUMvRTtBQU1mLFNBQVNTLFlBQVksS0FBaUM7UUFBakMsRUFBRUMsV0FBVyxFQUFvQixHQUFqQzs7SUFDbEMsTUFBTSxFQUFFQyxLQUFLLEVBQUUsR0FBR1QsOERBQU9BO0lBQ3pCLE1BQU0sQ0FBQ1UsV0FBV0MsYUFBYSxHQUFHYiwrQ0FBUUEsQ0FBQztJQUMzQyxNQUFNLENBQUNjLE9BQU9DLFNBQVMsR0FBR2YsK0NBQVFBLENBQUM7SUFDbkMsTUFBTSxDQUFDZ0IsYUFBYUMsZUFBZSxHQUFHakIsK0NBQVFBLENBQUM7SUFFL0MsTUFBTWtCLGVBQWU7UUFDbkI7UUFDQTtRQUNBO1FBQ0E7UUFDQTtRQUNBO0tBQ0Q7SUFFRGpCLGdEQUFTQTtpQ0FBQztZQUNSLDhCQUE4QjtZQUM5QkcsaUVBQWtCQTtRQUNwQjtnQ0FBRyxFQUFFO0lBRUxILGdEQUFTQTtpQ0FBQztZQUNSLElBQUlXLFdBQVc7Z0JBQ2IsSUFBSU8sWUFBWTtnQkFDaEIsTUFBTUMsV0FBV0M7c0RBQVk7d0JBQzNCRixZQUFZLENBQUNBLFlBQVksS0FBS0QsYUFBYUksTUFBTTt3QkFDakRMLGVBQWVDLFlBQVksQ0FBQ0MsVUFBVTtvQkFDeEM7cURBQUc7Z0JBRUg7NkNBQU8sSUFBTUksY0FBY0g7O1lBQzdCO1FBQ0Y7Z0NBQUc7UUFBQ1I7S0FBVTtJQUVkLE1BQU1ZLGtCQUFrQjtRQUN0QlgsYUFBYTtRQUNiRSxTQUFTO1FBRVQsMEJBQTBCO1FBQzFCUixvRUFBcUJBLENBQUMsVUFBVTtRQUVoQyxJQUFJO1lBQ0Ysc0NBQXNDO1lBQ3RDLE1BQU0sSUFBSWtCLFFBQVFDLENBQUFBLFVBQVdDLFdBQVdELFNBQVM7WUFFakQsb0NBQW9DO1lBQ3BDLElBQUlwQiwrREFBZ0JBLElBQUk7Z0JBQ3RCLE1BQU1zQixlQUFldkIsOERBQWVBO2dCQUNwQyxJQUFJdUIsY0FBYztvQkFDaEIsTUFBTUMsV0FBVyxNQUFNMUIsK0NBQVNBLENBQUMyQixpQkFBaUIsQ0FBQ0Y7b0JBQ25ELElBQUlDLFNBQVNFLE9BQU8sRUFBRTt3QkFDcEJ4QixvRUFBcUJBLENBQUMsZ0JBQWdCO3dCQUN0Q0ksTUFBTWtCLFNBQVNHLElBQUksRUFBRUgsU0FBU0ksS0FBSzt3QkFDbkN2Qjt3QkFDQTtvQkFDRjtnQkFDRjtZQUNGO1lBRUEsa0RBQWtEO1lBQ2xELE1BQU1tQixXQUFXLE1BQU0xQiwrQ0FBU0EsQ0FBQytCLFNBQVM7WUFDMUMsSUFBSUwsU0FBU0UsT0FBTyxFQUFFO2dCQUNwQnhCLG9FQUFxQkEsQ0FBQyxnQkFBZ0I7Z0JBQ3RDSSxNQUFNa0IsU0FBU0csSUFBSSxFQUFFSCxTQUFTSSxLQUFLO2dCQUNuQ3ZCO1lBQ0YsT0FBTztnQkFDTEgsb0VBQXFCQSxDQUFDLGdCQUFnQjtnQkFDdENRLFNBQVNjLFNBQVNmLEtBQUssSUFBSTtZQUM3QjtRQUNGLEVBQUUsT0FBT0EsT0FBTztZQUNkUCxvRUFBcUJBLENBQUMsZ0JBQWdCO1lBQ3RDUSxTQUFTO1FBQ1gsU0FBVTtZQUNSRixhQUFhO1FBQ2Y7SUFDRjtJQUVBLElBQUlELFdBQVc7UUFDYixxQkFDRSw4REFBQ3VCO1lBQUlDLFdBQVU7OzhCQUViLDhEQUFDRDtvQkFBSUMsV0FBVTs7c0NBQ2IsOERBQUNEOzRCQUFJQyxXQUFVOzs7Ozs7c0NBQ2YsOERBQUNEOzRCQUFJQyxXQUFVOzs7Ozs7c0NBQ2YsOERBQUNEOzRCQUFJQyxXQUFVOzs7Ozs7c0NBQ2YsOERBQUNEOzRCQUFJQyxXQUFVOzs7Ozs7Ozs7Ozs7OEJBR2pCLDhEQUFDRDtvQkFBSUMsV0FBVTs7c0NBRWIsOERBQUNEOzRCQUFJQyxXQUFVO3NDQUNiLDRFQUFDNUIsa0RBQUtBO2dDQUNKNkIsS0FBSTtnQ0FDSkMsS0FBSTtnQ0FDSkMsT0FBTztnQ0FDUEMsUUFBUTtnQ0FDUkosV0FBVTs7Ozs7Ozs7Ozs7c0NBS2QsOERBQUNEOzRCQUFJQyxXQUFVOzs4Q0FDYiw4REFBQ0Q7b0NBQUlDLFdBQVU7Ozs7Ozs4Q0FDZiw4REFBQ0Q7b0NBQUlDLFdBQVU7b0NBQStHSyxPQUFPO3dDQUFFQyxvQkFBb0I7d0NBQVdDLG1CQUFtQjtvQ0FBTzs7Ozs7Ozs7Ozs7O3NDQUlsTSw4REFBQ0M7NEJBQUdSLFdBQVU7c0NBQ1hwQjs7Ozs7O3NDQUlILDhEQUFDbUI7NEJBQUlDLFdBQVU7c0NBQ2IsNEVBQUNEO2dDQUFJQyxXQUFVOzs7Ozs7Ozs7OztzQ0FHakIsOERBQUNTOzRCQUFFVCxXQUFVO3NDQUE2Qjs7Ozs7Ozs7Ozs7Ozs7Ozs7O0lBTWxEO0lBRUEscUJBQ0UsOERBQUNEO2tEQUFjOzswQkFFYiw4REFBQ0E7MERBQWM7O2tDQUNiLDhEQUFDQTtrRUFBYzs7Ozs7O2tDQUNmLDhEQUFDQTtrRUFBYzs7Ozs7O2tDQUNmLDhEQUFDQTtrRUFBYzs7Ozs7O2tDQUNmLDhEQUFDQTtrRUFBYzs7Ozs7Ozs7Ozs7OzBCQUdqQiw4REFBQ0E7MERBQWM7O2tDQUViLDhEQUFDQTtrRUFBYztrQ0FDYiw0RUFBQzNCLGtEQUFLQTs0QkFDSjZCLEtBQUk7NEJBQ0pDLEtBQUk7NEJBQ0pDLE9BQU87NEJBQ1BDLFFBQVE7NEJBQ1JKLFdBQVU7Ozs7Ozs7Ozs7O2tDQUtkLDhEQUFDVTtrRUFBYTtrQ0FBcUg7Ozs7OztrQ0FJbkksOERBQUNEO2tFQUFZO2tDQUE2Qzs7Ozs7O2tDQUsxRCw4REFBQ1Y7a0VBQWM7OzBDQUNiLDhEQUFDQTswRUFBYzs7a0RBQ2IsOERBQUNZO2tGQUFlO2tEQUF1Qjs7Ozs7O2tEQUN2Qyw4REFBQ0E7O2tEQUFLOzs7Ozs7Ozs7Ozs7MENBRVIsOERBQUNaOzBFQUFjOztrREFDYiw4REFBQ1k7a0ZBQWU7a0RBQXVCOzs7Ozs7a0RBQ3ZDLDhEQUFDQTs7a0RBQUs7Ozs7Ozs7Ozs7OzswQ0FFUiw4REFBQ1o7MEVBQWM7O2tEQUNiLDhEQUFDWTtrRkFBZTtrREFBdUI7Ozs7OztrREFDdkMsOERBQUNBOztrREFBSzs7Ozs7Ozs7Ozs7Ozs7Ozs7O2tDQUtWLDhEQUFDQzt3QkFDQ0MsU0FBU3pCO3dCQUNUMEIsVUFBVXRDO2tFQUNBO2tDQUNYOzs7Ozs7b0JBSUFFLHVCQUNDLDhEQUFDcUI7a0VBQWM7a0NBQ1pyQjs7Ozs7O2tDQUlMLDhEQUFDK0I7a0VBQVk7a0NBQTZCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBZ0JsRDtHQXhNd0JwQzs7UUFDSlAsMERBQU9BOzs7S0FESE8iLCJzb3VyY2VzIjpbIi9Vc2Vycy90YWhhZmFyb29xdWkvRG9jdW1lbnRzL2F1Z21lbnQtcHJvamVjdHMvSGVoZS1jb2luL2hlaGUtbWluZXIvc3JjL2NvbXBvbmVudHMvTGFuZGluZ1BhZ2UudHN4Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50J1xuXG5pbXBvcnQgeyB1c2VTdGF0ZSwgdXNlRWZmZWN0IH0gZnJvbSAncmVhY3QnXG5pbXBvcnQgeyB1c2VBdXRoIH0gZnJvbSAnQC9jb250ZXh0cy9BdXRoQ29udGV4dCdcbmltcG9ydCB7IGFwaUNsaWVudCB9IGZyb20gJ0AvbGliL2FwaSdcbmltcG9ydCB7IGluaXRUZWxlZ3JhbVdlYkFwcCwgZ2V0VGVsZWdyYW1Vc2VyLCBpc1RlbGVncmFtV2ViQXBwLCB0cmlnZ2VySGFwdGljRmVlZGJhY2sgfSBmcm9tICdAL2xpYi90ZWxlZ3JhbSdcbmltcG9ydCBJbWFnZSBmcm9tICduZXh0L2ltYWdlJ1xuXG5pbnRlcmZhY2UgTGFuZGluZ1BhZ2VQcm9wcyB7XG4gIG9uTGF1bmNoQXBwOiAoKSA9PiB2b2lkXG59XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIExhbmRpbmdQYWdlKHsgb25MYXVuY2hBcHAgfTogTGFuZGluZ1BhZ2VQcm9wcykge1xuICBjb25zdCB7IGxvZ2luIH0gPSB1c2VBdXRoKClcbiAgY29uc3QgW2lzTG9hZGluZywgc2V0SXNMb2FkaW5nXSA9IHVzZVN0YXRlKGZhbHNlKVxuICBjb25zdCBbZXJyb3IsIHNldEVycm9yXSA9IHVzZVN0YXRlKCcnKVxuICBjb25zdCBbbG9hZGluZ1RleHQsIHNldExvYWRpbmdUZXh0XSA9IHVzZVN0YXRlKCdJbml0aWFsaXppbmcuLi4nKVxuXG4gIGNvbnN0IGxvYWRpbmdTdGVwcyA9IFtcbiAgICAnSW5pdGlhbGl6aW5nLi4uJyxcbiAgICAnQ29ubmVjdGluZyB0byBUZWxlZ3JhbS4uLicsXG4gICAgJ0F1dGhlbnRpY2F0aW5nIHVzZXIuLi4nLFxuICAgICdMb2FkaW5nIHVzZXIgZGF0YS4uLicsXG4gICAgJ1ByZXBhcmluZyBtaW5pbmcgaW50ZXJmYWNlLi4uJyxcbiAgICAnQWxtb3N0IHJlYWR5Li4uJ1xuICBdXG5cbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICAvLyBJbml0aWFsaXplIFRlbGVncmFtIFdlYiBBcHBcbiAgICBpbml0VGVsZWdyYW1XZWJBcHAoKVxuICB9LCBbXSlcblxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGlmIChpc0xvYWRpbmcpIHtcbiAgICAgIGxldCBzdGVwSW5kZXggPSAwXG4gICAgICBjb25zdCBpbnRlcnZhbCA9IHNldEludGVydmFsKCgpID0+IHtcbiAgICAgICAgc3RlcEluZGV4ID0gKHN0ZXBJbmRleCArIDEpICUgbG9hZGluZ1N0ZXBzLmxlbmd0aFxuICAgICAgICBzZXRMb2FkaW5nVGV4dChsb2FkaW5nU3RlcHNbc3RlcEluZGV4XSlcbiAgICAgIH0sIDgwMClcblxuICAgICAgcmV0dXJuICgpID0+IGNsZWFySW50ZXJ2YWwoaW50ZXJ2YWwpXG4gICAgfVxuICB9LCBbaXNMb2FkaW5nXSlcblxuICBjb25zdCBoYW5kbGVMYXVuY2hBcHAgPSBhc3luYyAoKSA9PiB7XG4gICAgc2V0SXNMb2FkaW5nKHRydWUpXG4gICAgc2V0RXJyb3IoJycpXG5cbiAgICAvLyBUcmlnZ2VyIGhhcHRpYyBmZWVkYmFja1xuICAgIHRyaWdnZXJIYXB0aWNGZWVkYmFjaygnaW1wYWN0JywgJ21lZGl1bScpXG5cbiAgICB0cnkge1xuICAgICAgLy8gU2ltdWxhdGUgbG9hZGluZyB0aW1lIGZvciBiZXR0ZXIgVVhcbiAgICAgIGF3YWl0IG5ldyBQcm9taXNlKHJlc29sdmUgPT4gc2V0VGltZW91dChyZXNvbHZlLCAxMDAwKSlcblxuICAgICAgLy8gVHJ5IFRlbGVncmFtIGF1dGhlbnRpY2F0aW9uIGZpcnN0XG4gICAgICBpZiAoaXNUZWxlZ3JhbVdlYkFwcCgpKSB7XG4gICAgICAgIGNvbnN0IHRlbGVncmFtVXNlciA9IGdldFRlbGVncmFtVXNlcigpXG4gICAgICAgIGlmICh0ZWxlZ3JhbVVzZXIpIHtcbiAgICAgICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGFwaUNsaWVudC5sb2dpbldpdGhUZWxlZ3JhbSh0ZWxlZ3JhbVVzZXIpXG4gICAgICAgICAgaWYgKHJlc3BvbnNlLnN1Y2Nlc3MpIHtcbiAgICAgICAgICAgIHRyaWdnZXJIYXB0aWNGZWVkYmFjaygnbm90aWZpY2F0aW9uJywgJ3N1Y2Nlc3MnKVxuICAgICAgICAgICAgbG9naW4ocmVzcG9uc2UudXNlciwgcmVzcG9uc2UudG9rZW4pXG4gICAgICAgICAgICBvbkxhdW5jaEFwcCgpXG4gICAgICAgICAgICByZXR1cm5cbiAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgIH1cblxuICAgICAgLy8gRmFsbGJhY2sgdG8gbW9jayBhdXRoZW50aWNhdGlvbiBmb3IgZGV2ZWxvcG1lbnRcbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgYXBpQ2xpZW50LmxvZ2luTW9jaygpXG4gICAgICBpZiAocmVzcG9uc2Uuc3VjY2Vzcykge1xuICAgICAgICB0cmlnZ2VySGFwdGljRmVlZGJhY2soJ25vdGlmaWNhdGlvbicsICdzdWNjZXNzJylcbiAgICAgICAgbG9naW4ocmVzcG9uc2UudXNlciwgcmVzcG9uc2UudG9rZW4pXG4gICAgICAgIG9uTGF1bmNoQXBwKClcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIHRyaWdnZXJIYXB0aWNGZWVkYmFjaygnbm90aWZpY2F0aW9uJywgJ2Vycm9yJylcbiAgICAgICAgc2V0RXJyb3IocmVzcG9uc2UuZXJyb3IgfHwgJ0F1dGhlbnRpY2F0aW9uIGZhaWxlZCcpXG4gICAgICB9XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIHRyaWdnZXJIYXB0aWNGZWVkYmFjaygnbm90aWZpY2F0aW9uJywgJ2Vycm9yJylcbiAgICAgIHNldEVycm9yKCdOZXR3b3JrIGVycm9yIG9jY3VycmVkLiBQbGVhc2UgdHJ5IGFnYWluLicpXG4gICAgfSBmaW5hbGx5IHtcbiAgICAgIHNldElzTG9hZGluZyhmYWxzZSlcbiAgICB9XG4gIH1cblxuICBpZiAoaXNMb2FkaW5nKSB7XG4gICAgcmV0dXJuIChcbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWluLWgtc2NyZWVuIGJnLWdyYWRpZW50LXRvLWJyIGZyb20tcHVycGxlLTkwMCB2aWEtYmx1ZS05MDAgdG8taW5kaWdvLTkwMCBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciByZWxhdGl2ZSBvdmVyZmxvdy1oaWRkZW5cIj5cbiAgICAgICAgey8qIEFuaW1hdGVkIGJhY2tncm91bmQgZWxlbWVudHMgKi99XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYWJzb2x1dGUgaW5zZXQtMFwiPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYWJzb2x1dGUgdG9wLTIwIGxlZnQtMjAgdy0zMiBoLTMyIGJnLXllbGxvdy00MDAgcm91bmRlZC1mdWxsIG9wYWNpdHktMjAgYW5pbWF0ZS1wdWxzZVwiPjwvZGl2PlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYWJzb2x1dGUgdG9wLTQwIHJpZ2h0LTMyIHctMjQgaC0yNCBiZy1vcmFuZ2UtNDAwIHJvdW5kZWQtZnVsbCBvcGFjaXR5LTIwIGFuaW1hdGUtcHVsc2UgZGVsYXktMzAwXCI+PC9kaXY+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhYnNvbHV0ZSBib3R0b20tMzIgbGVmdC0zMiB3LTQwIGgtNDAgYmcteWVsbG93LTMwMCByb3VuZGVkLWZ1bGwgb3BhY2l0eS0yMCBhbmltYXRlLXB1bHNlIGRlbGF5LTcwMFwiPjwvZGl2PlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYWJzb2x1dGUgYm90dG9tLTIwIHJpZ2h0LTIwIHctMjggaC0yOCBiZy1vcmFuZ2UtMzAwIHJvdW5kZWQtZnVsbCBvcGFjaXR5LTIwIGFuaW1hdGUtcHVsc2UgZGVsYXktNTAwXCI+PC9kaXY+XG4gICAgICAgIDwvZGl2PlxuXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXIgei0xMFwiPlxuICAgICAgICAgIHsvKiBMb2dvICovfVxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWItOCBhbmltYXRlLWJvdW5jZVwiPlxuICAgICAgICAgICAgPEltYWdlXG4gICAgICAgICAgICAgIHNyYz1cIi9oZWhlLWxvZ28uc3ZnXCJcbiAgICAgICAgICAgICAgYWx0PVwiSGVoZSBNaW5lclwiXG4gICAgICAgICAgICAgIHdpZHRoPXsxMjB9XG4gICAgICAgICAgICAgIGhlaWdodD17MTIwfVxuICAgICAgICAgICAgICBjbGFzc05hbWU9XCJteC1hdXRvIGRyb3Atc2hhZG93LTJ4bFwiXG4gICAgICAgICAgICAvPlxuICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgey8qIExvYWRpbmcgc3Bpbm5lciAqL31cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInJlbGF0aXZlIG1iLTZcIj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy0xNiBoLTE2IGJvcmRlci00IGJvcmRlci15ZWxsb3ctNDAwIGJvcmRlci10LXRyYW5zcGFyZW50IHJvdW5kZWQtZnVsbCBhbmltYXRlLXNwaW4gbXgtYXV0b1wiPjwvZGl2PlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhYnNvbHV0ZSBpbnNldC0wIHctMTYgaC0xNiBib3JkZXItNCBib3JkZXItb3JhbmdlLTQwMCBib3JkZXItYi10cmFuc3BhcmVudCByb3VuZGVkLWZ1bGwgYW5pbWF0ZS1zcGluIG14LWF1dG9cIiBzdHlsZT17eyBhbmltYXRpb25EaXJlY3Rpb246ICdyZXZlcnNlJywgYW5pbWF0aW9uRHVyYXRpb246ICcxLjVzJyB9fT48L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgIHsvKiBMb2FkaW5nIHRleHQgKi99XG4gICAgICAgICAgPGgyIGNsYXNzTmFtZT1cInRleHQtMnhsIGZvbnQtYm9sZCB0ZXh0LXdoaXRlIG1iLTIgYW5pbWF0ZS1wdWxzZVwiPlxuICAgICAgICAgICAge2xvYWRpbmdUZXh0fVxuICAgICAgICAgIDwvaDI+XG4gICAgICAgICAgXG4gICAgICAgICAgey8qIFByb2dyZXNzIGJhciAqL31cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctNjQgaC0yIGJnLWdyYXktNzAwIHJvdW5kZWQtZnVsbCBteC1hdXRvIG92ZXJmbG93LWhpZGRlblwiPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJoLWZ1bGwgYmctZ3JhZGllbnQtdG8tciBmcm9tLXllbGxvdy00MDAgdG8tb3JhbmdlLTQwMCByb3VuZGVkLWZ1bGwgYW5pbWF0ZS1wdWxzZVwiPjwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTMwMCBtdC00IHRleHQtc21cIj5cbiAgICAgICAgICAgIFBsZWFzZSB3YWl0IHdoaWxlIHdlIHByZXBhcmUgeW91ciBtaW5pbmcgZXhwZXJpZW5jZS4uLlxuICAgICAgICAgIDwvcD5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L2Rpdj5cbiAgICApXG4gIH1cblxuICByZXR1cm4gKFxuICAgIDxkaXYgY2xhc3NOYW1lPVwibWluLWgtc2NyZWVuIGJnLWdyYWRpZW50LXRvLWJyIGZyb20tcHVycGxlLTkwMCB2aWEtYmx1ZS05MDAgdG8taW5kaWdvLTkwMCBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciByZWxhdGl2ZSBvdmVyZmxvdy1oaWRkZW5cIj5cbiAgICAgIHsvKiBBbmltYXRlZCBiYWNrZ3JvdW5kIGVsZW1lbnRzICovfVxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJhYnNvbHV0ZSBpbnNldC0wXCI+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYWJzb2x1dGUgdG9wLTIwIGxlZnQtMjAgdy0zMiBoLTMyIGJnLXllbGxvdy00MDAgcm91bmRlZC1mdWxsIG9wYWNpdHktMjAgYW5pbWF0ZS1mbG9hdFwiPjwvZGl2PlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFic29sdXRlIHRvcC00MCByaWdodC0zMiB3LTI0IGgtMjQgYmctb3JhbmdlLTQwMCByb3VuZGVkLWZ1bGwgb3BhY2l0eS0yMCBhbmltYXRlLWZsb2F0IGRlbGF5LTMwMFwiPjwvZGl2PlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFic29sdXRlIGJvdHRvbS0zMiBsZWZ0LTMyIHctNDAgaC00MCBiZy15ZWxsb3ctMzAwIHJvdW5kZWQtZnVsbCBvcGFjaXR5LTIwIGFuaW1hdGUtZmxvYXQgZGVsYXktNzAwXCI+PC9kaXY+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYWJzb2x1dGUgYm90dG9tLTIwIHJpZ2h0LTIwIHctMjggaC0yOCBiZy1vcmFuZ2UtMzAwIHJvdW5kZWQtZnVsbCBvcGFjaXR5LTIwIGFuaW1hdGUtZmxvYXQgZGVsYXktNTAwXCI+PC9kaXY+XG4gICAgICA8L2Rpdj5cblxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlciB6LTEwIG1heC13LW1kIG14LWF1dG8gcHgtNlwiPlxuICAgICAgICB7LyogTG9nbyAqL31cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtYi04IGFuaW1hdGUtYm91bmNlXCI+XG4gICAgICAgICAgPEltYWdlXG4gICAgICAgICAgICBzcmM9XCIvaGVoZS1sb2dvLnN2Z1wiXG4gICAgICAgICAgICBhbHQ9XCJIZWhlIE1pbmVyXCJcbiAgICAgICAgICAgIHdpZHRoPXsxNTB9XG4gICAgICAgICAgICBoZWlnaHQ9ezE1MH1cbiAgICAgICAgICAgIGNsYXNzTmFtZT1cIm14LWF1dG8gZHJvcC1zaGFkb3ctMnhsXCJcbiAgICAgICAgICAvPlxuICAgICAgICA8L2Rpdj5cblxuICAgICAgICB7LyogVGl0bGUgKi99XG4gICAgICAgIDxoMSBjbGFzc05hbWU9XCJ0ZXh0LTV4bCBmb250LWJvbGQgdGV4dC10cmFuc3BhcmVudCBiZy1jbGlwLXRleHQgYmctZ3JhZGllbnQtdG8tciBmcm9tLXllbGxvdy00MDAgdG8tb3JhbmdlLTQwMCBtYi00IGFuaW1hdGUtcHVsc2VcIj5cbiAgICAgICAgICBIRUhFIE1JTkVSXG4gICAgICAgIDwvaDE+XG5cbiAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC14bCB0ZXh0LWdyYXktMzAwIG1iLTggbGVhZGluZy1yZWxheGVkXCI+XG4gICAgICAgICAgU3RhcnQgeW91ciBjcnlwdG8gbWluaW5nIGpvdXJuZXkgd2l0aCB0aGUgbW9zdCBmdW4gYW5kIHJld2FyZGluZyBtaW5pbmcgZ2FtZSFcbiAgICAgICAgPC9wPlxuXG4gICAgICAgIHsvKiBGZWF0dXJlcyAqL31cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtYi04IHNwYWNlLXktM1wiPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgdGV4dC1ncmF5LTMwMFwiPlxuICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC15ZWxsb3ctNDAwIG1yLTJcIj7imqE8L3NwYW4+XG4gICAgICAgICAgICA8c3Bhbj5NaW5lIEhFSEUgdG9rZW5zIGV2ZXJ5IDQgaG91cnM8L3NwYW4+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciB0ZXh0LWdyYXktMzAwXCI+XG4gICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXllbGxvdy00MDAgbXItMlwiPvCfmoA8L3NwYW4+XG4gICAgICAgICAgICA8c3Bhbj5VcGdyYWRlIHlvdXIgbWluaW5nIHBvd2VyPC9zcGFuPlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgdGV4dC1ncmF5LTMwMFwiPlxuICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC15ZWxsb3ctNDAwIG1yLTJcIj7wn5GlPC9zcGFuPlxuICAgICAgICAgICAgPHNwYW4+UmVmZXIgZnJpZW5kcyBhbmQgZWFybiByZXdhcmRzPC9zcGFuPlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cblxuICAgICAgICB7LyogTGF1bmNoIGJ1dHRvbiAqL31cbiAgICAgICAgPGJ1dHRvblxuICAgICAgICAgIG9uQ2xpY2s9e2hhbmRsZUxhdW5jaEFwcH1cbiAgICAgICAgICBkaXNhYmxlZD17aXNMb2FkaW5nfVxuICAgICAgICAgIGNsYXNzTmFtZT1cInctZnVsbCBiZy1ncmFkaWVudC10by1yIGZyb20teWVsbG93LTQwMCB0by1vcmFuZ2UtNDAwIHRleHQtYmxhY2sgZm9udC1ib2xkIHB5LTQgcHgtOCByb3VuZGVkLXhsIHRleHQtbGcgc2hhZG93LTJ4bCB0cmFuc2Zvcm0gdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMzAwIGhvdmVyOnNjYWxlLTEwNSBob3ZlcjpzaGFkb3cteWVsbG93LTQwMC81MCBkaXNhYmxlZDpvcGFjaXR5LTUwIGRpc2FibGVkOmN1cnNvci1ub3QtYWxsb3dlZCBhbmltYXRlLXB1bHNlXCJcbiAgICAgICAgPlxuICAgICAgICAgIPCfmoAgTGF1bmNoIEFwcFxuICAgICAgICA8L2J1dHRvbj5cblxuICAgICAgICB7ZXJyb3IgJiYgKFxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibXQtNCBwLTMgYmctcmVkLTUwMC8yMCBib3JkZXIgYm9yZGVyLXJlZC01MDAgcm91bmRlZC1sZyB0ZXh0LXJlZC0zMDAgdGV4dC1zbVwiPlxuICAgICAgICAgICAge2Vycm9yfVxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICApfVxuXG4gICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtZ3JheS00MDAgdGV4dC14cyBtdC02XCI+XG4gICAgICAgICAgUG93ZXJlZCBieSBUZWxlZ3JhbSDigKIgU2VjdXJlIOKAoiBEZWNlbnRyYWxpemVkXG4gICAgICAgIDwvcD5cbiAgICAgIDwvZGl2PlxuXG4gICAgICA8c3R5bGUganN4PntgXG4gICAgICAgIEBrZXlmcmFtZXMgZmxvYXQge1xuICAgICAgICAgIDAlLCAxMDAlIHsgdHJhbnNmb3JtOiB0cmFuc2xhdGVZKDBweCkgcm90YXRlKDBkZWcpOyB9XG4gICAgICAgICAgNTAlIHsgdHJhbnNmb3JtOiB0cmFuc2xhdGVZKC0yMHB4KSByb3RhdGUoMTgwZGVnKTsgfVxuICAgICAgICB9XG4gICAgICAgIC5hbmltYXRlLWZsb2F0IHtcbiAgICAgICAgICBhbmltYXRpb246IGZsb2F0IDZzIGVhc2UtaW4tb3V0IGluZmluaXRlO1xuICAgICAgICB9XG4gICAgICBgfTwvc3R5bGU+XG4gICAgPC9kaXY+XG4gIClcbn1cbiJdLCJuYW1lcyI6WyJ1c2VTdGF0ZSIsInVzZUVmZmVjdCIsInVzZUF1dGgiLCJhcGlDbGllbnQiLCJpbml0VGVsZWdyYW1XZWJBcHAiLCJnZXRUZWxlZ3JhbVVzZXIiLCJpc1RlbGVncmFtV2ViQXBwIiwidHJpZ2dlckhhcHRpY0ZlZWRiYWNrIiwiSW1hZ2UiLCJMYW5kaW5nUGFnZSIsIm9uTGF1bmNoQXBwIiwibG9naW4iLCJpc0xvYWRpbmciLCJzZXRJc0xvYWRpbmciLCJlcnJvciIsInNldEVycm9yIiwibG9hZGluZ1RleHQiLCJzZXRMb2FkaW5nVGV4dCIsImxvYWRpbmdTdGVwcyIsInN0ZXBJbmRleCIsImludGVydmFsIiwic2V0SW50ZXJ2YWwiLCJsZW5ndGgiLCJjbGVhckludGVydmFsIiwiaGFuZGxlTGF1bmNoQXBwIiwiUHJvbWlzZSIsInJlc29sdmUiLCJzZXRUaW1lb3V0IiwidGVsZWdyYW1Vc2VyIiwicmVzcG9uc2UiLCJsb2dpbldpdGhUZWxlZ3JhbSIsInN1Y2Nlc3MiLCJ1c2VyIiwidG9rZW4iLCJsb2dpbk1vY2siLCJkaXYiLCJjbGFzc05hbWUiLCJzcmMiLCJhbHQiLCJ3aWR0aCIsImhlaWdodCIsInN0eWxlIiwiYW5pbWF0aW9uRGlyZWN0aW9uIiwiYW5pbWF0aW9uRHVyYXRpb24iLCJoMiIsInAiLCJoMSIsInNwYW4iLCJidXR0b24iLCJvbkNsaWNrIiwiZGlzYWJsZWQiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/LandingPage.tsx\n"));

/***/ })

});