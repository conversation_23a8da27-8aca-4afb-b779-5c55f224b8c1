"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/SubscriptionScreen.tsx":
/*!***********************************************!*\
  !*** ./src/components/SubscriptionScreen.tsx ***!
  \***********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SubscriptionScreen)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./src/lib/api.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction SubscriptionScreen() {\n    var _user_miningPower, _user_miningPower1;\n    _s();\n    const { user, updateUser } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [purchasingPlan, setPurchasingPlan] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [success, setSuccess] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const handlePurchaseBasicPlan = async ()=>{\n        setPurchasingPlan('basic');\n        setIsLoading(true);\n        setError('');\n        setSuccess('');\n        try {\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_3__.apiClient.purchaseBasicPlan();\n            if (response.success) {\n                setSuccess('Basic plan purchased successfully! You can now start mining.');\n                updateUser({\n                    hasBasicPlan: true\n                });\n            } else {\n                setError(response.error || 'Failed to purchase basic plan');\n            }\n        } catch (error) {\n            setError('Network error occurred');\n        } finally{\n            setIsLoading(false);\n            setPurchasingPlan(null);\n        }\n    };\n    const handlePurchaseSpeedUpgrade = async ()=>{\n        setPurchasingPlan('speed');\n        setIsLoading(true);\n        setError('');\n        setSuccess('');\n        try {\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_3__.apiClient.purchaseSpeedUpgrade();\n            if (response.success) {\n                setSuccess('Speed upgrade purchased! Your mining power has increased.');\n                updateUser({\n                    miningPower: response.user.miningPower,\n                    speedUpgrades: response.user.speedUpgrades\n                });\n            } else {\n                setError(response.error || 'Failed to purchase speed upgrade');\n            }\n        } catch (error) {\n            setError('Network error occurred');\n        } finally{\n            setIsLoading(false);\n            setPurchasingPlan(null);\n        }\n    };\n    // Check if user has active mining session\n    const hasActiveMining = (user === null || user === void 0 ? void 0 : user.currentMiningSession) && (user === null || user === void 0 ? void 0 : user.canMine) === false;\n    const isUpgradeDisabled = hasActiveMining || isLoading && purchasingPlan === 'speed';\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"p-6 max-w-md mx-auto\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-2xl font-bold text-white mb-2\",\n                        children: \"Upgrades\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/SubscriptionScreen.tsx\",\n                        lineNumber: 70,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-400\",\n                        children: \"Enhance your mining capabilities\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/SubscriptionScreen.tsx\",\n                        lineNumber: 71,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/SubscriptionScreen.tsx\",\n                lineNumber: 69,\n                columnNumber: 7\n            }, this),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-red-500/10 border border-red-500/20 rounded-lg p-3 mb-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-red-400 text-sm\",\n                    children: error\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/SubscriptionScreen.tsx\",\n                    lineNumber: 77,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/SubscriptionScreen.tsx\",\n                lineNumber: 76,\n                columnNumber: 9\n            }, this),\n            success && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-green-500/10 border border-green-500/20 rounded-lg p-3 mb-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-green-400 text-sm\",\n                    children: success\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/SubscriptionScreen.tsx\",\n                    lineNumber: 83,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/SubscriptionScreen.tsx\",\n                lineNumber: 82,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gray-800 rounded-lg border border-gray-700 p-4 mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-white font-semibold mb-3\",\n                        children: \"Current Status\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/SubscriptionScreen.tsx\",\n                        lineNumber: 89,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-gray-300\",\n                                        children: \"Plan Status\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/SubscriptionScreen.tsx\",\n                                        lineNumber: 92,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium \".concat((user === null || user === void 0 ? void 0 : user.hasBasicPlan) ? 'text-green-400' : 'text-red-400'),\n                                        children: (user === null || user === void 0 ? void 0 : user.hasBasicPlan) ? 'Basic Plan Active' : 'No Plan'\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/SubscriptionScreen.tsx\",\n                                        lineNumber: 93,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/SubscriptionScreen.tsx\",\n                                lineNumber: 91,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-gray-300\",\n                                        children: \"Mining Power\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/SubscriptionScreen.tsx\",\n                                        lineNumber: 98,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-white font-medium\",\n                                        children: [\n                                            (user === null || user === void 0 ? void 0 : (_user_miningPower = user.miningPower) === null || _user_miningPower === void 0 ? void 0 : _user_miningPower.toFixed(2)) || '4.00',\n                                            \" HEHE/4h\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/SubscriptionScreen.tsx\",\n                                        lineNumber: 99,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/SubscriptionScreen.tsx\",\n                                lineNumber: 97,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-gray-300\",\n                                        children: \"Speed Upgrades\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/SubscriptionScreen.tsx\",\n                                        lineNumber: 104,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-white font-medium\",\n                                        children: (user === null || user === void 0 ? void 0 : user.speedUpgrades) || 0\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/SubscriptionScreen.tsx\",\n                                        lineNumber: 105,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/SubscriptionScreen.tsx\",\n                                lineNumber: 103,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/SubscriptionScreen.tsx\",\n                        lineNumber: 90,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/SubscriptionScreen.tsx\",\n                lineNumber: 88,\n                columnNumber: 7\n            }, this),\n            !(user === null || user === void 0 ? void 0 : user.hasBasicPlan) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg p-6 mb-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-12 h-12 bg-white/20 rounded-full mx-auto flex items-center justify-center mb-3\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-xl\",\n                                children: \"⛏️\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/SubscriptionScreen.tsx\",\n                                lineNumber: 117,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/SubscriptionScreen.tsx\",\n                            lineNumber: 116,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-white text-lg font-bold mb-2\",\n                            children: \"Basic Mining Plan\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/SubscriptionScreen.tsx\",\n                            lineNumber: 119,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-white/80 text-sm mb-4\",\n                            children: \"Unlock the ability to mine HEHE tokens\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/SubscriptionScreen.tsx\",\n                            lineNumber: 120,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white/10 rounded-lg p-3 mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-white font-semibold text-lg\",\n                                    children: \"$1.00\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/SubscriptionScreen.tsx\",\n                                    lineNumber: 125,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-white/80 text-sm\",\n                                    children: \"One-time purchase\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/SubscriptionScreen.tsx\",\n                                    lineNumber: 126,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/SubscriptionScreen.tsx\",\n                            lineNumber: 124,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-left mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"text-white font-semibold mb-2\",\n                                    children: \"Includes:\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/SubscriptionScreen.tsx\",\n                                    lineNumber: 130,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"text-white/80 text-sm space-y-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"• Mine 4 HEHE tokens every 4 hours\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/SubscriptionScreen.tsx\",\n                                            lineNumber: 132,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"• Access to all tasks and referrals\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/SubscriptionScreen.tsx\",\n                                            lineNumber: 133,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"• Eligible for future airdrops\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/SubscriptionScreen.tsx\",\n                                            lineNumber: 134,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"• Ability to purchase speed upgrades\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/SubscriptionScreen.tsx\",\n                                            lineNumber: 135,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/SubscriptionScreen.tsx\",\n                                    lineNumber: 131,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/SubscriptionScreen.tsx\",\n                            lineNumber: 129,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: handlePurchaseBasicPlan,\n                            disabled: isLoading && purchasingPlan === 'basic',\n                            className: \"w-full bg-white text-blue-600 font-semibold py-3 px-4 rounded-lg hover:bg-gray-100 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors\",\n                            children: isLoading && purchasingPlan === 'basic' ? 'Processing...' : 'Purchase Basic Plan'\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/SubscriptionScreen.tsx\",\n                            lineNumber: 139,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/SubscriptionScreen.tsx\",\n                    lineNumber: 115,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/SubscriptionScreen.tsx\",\n                lineNumber: 114,\n                columnNumber: 9\n            }, this),\n            (user === null || user === void 0 ? void 0 : user.hasBasicPlan) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gray-800 rounded-lg border border-gray-700 p-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-12 h-12 bg-yellow-500/20 rounded-full mx-auto flex items-center justify-center mb-3\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-xl\",\n                                children: \"⚡\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/SubscriptionScreen.tsx\",\n                                lineNumber: 155,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/SubscriptionScreen.tsx\",\n                            lineNumber: 154,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-white text-lg font-bold mb-2\",\n                            children: \"Speed Upgrade\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/SubscriptionScreen.tsx\",\n                            lineNumber: 157,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-400 text-sm mb-4\",\n                            children: \"Increase your mining power by 0.25 HEHE per 4 hours\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/SubscriptionScreen.tsx\",\n                            lineNumber: 158,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-yellow-500/10 border border-yellow-500/20 rounded-lg p-3 mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-yellow-400 font-semibold text-lg\",\n                                    children: \"$1.00\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/SubscriptionScreen.tsx\",\n                                    lineNumber: 163,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-yellow-300 text-sm\",\n                                    children: \"Per upgrade\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/SubscriptionScreen.tsx\",\n                                    lineNumber: 164,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/SubscriptionScreen.tsx\",\n                            lineNumber: 162,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-left mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"text-white font-semibold mb-2\",\n                                    children: \"Upgrade Details:\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/SubscriptionScreen.tsx\",\n                                    lineNumber: 168,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"text-gray-300 text-sm space-y-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"• +0.25 HEHE tokens per 4-hour cycle\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/SubscriptionScreen.tsx\",\n                                            lineNumber: 170,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"• Permanent increase to mining power\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/SubscriptionScreen.tsx\",\n                                            lineNumber: 171,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"• No limit on number of upgrades\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/SubscriptionScreen.tsx\",\n                                            lineNumber: 172,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"• Compounds with existing upgrades\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/SubscriptionScreen.tsx\",\n                                            lineNumber: 173,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/SubscriptionScreen.tsx\",\n                                    lineNumber: 169,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/SubscriptionScreen.tsx\",\n                            lineNumber: 167,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gray-700 rounded-lg p-3 mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-300 text-sm\",\n                                    children: [\n                                        \"Current: \",\n                                        user === null || user === void 0 ? void 0 : (_user_miningPower1 = user.miningPower) === null || _user_miningPower1 === void 0 ? void 0 : _user_miningPower1.toFixed(2),\n                                        \" HEHE/4h\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/SubscriptionScreen.tsx\",\n                                    lineNumber: 178,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-white text-sm\",\n                                    children: [\n                                        \"After upgrade: \",\n                                        (((user === null || user === void 0 ? void 0 : user.miningPower) || 4) + 0.25).toFixed(2),\n                                        \" HEHE/4h\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/SubscriptionScreen.tsx\",\n                                    lineNumber: 181,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/SubscriptionScreen.tsx\",\n                            lineNumber: 177,\n                            columnNumber: 13\n                        }, this),\n                        hasActiveMining && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-orange-500/10 border border-orange-500/20 rounded-lg p-3 mb-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-orange-400 text-sm\",\n                                children: \"⚠️ Cannot purchase upgrades during active mining session. Complete your current mining first.\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/SubscriptionScreen.tsx\",\n                                lineNumber: 188,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/SubscriptionScreen.tsx\",\n                            lineNumber: 187,\n                            columnNumber: 15\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: handlePurchaseSpeedUpgrade,\n                            disabled: isUpgradeDisabled,\n                            className: \"w-full font-semibold py-3 px-4 rounded-lg transition-colors \".concat(isUpgradeDisabled ? 'bg-gray-600 cursor-not-allowed text-gray-400' : 'bg-yellow-600 hover:bg-yellow-700 text-white'),\n                            children: isLoading && purchasingPlan === 'speed' ? 'Processing...' : hasActiveMining ? 'Mining in Progress - Upgrade Locked' : 'Purchase Speed Upgrade'\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/SubscriptionScreen.tsx\",\n                            lineNumber: 194,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/SubscriptionScreen.tsx\",\n                    lineNumber: 153,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/SubscriptionScreen.tsx\",\n                lineNumber: 152,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-6 bg-blue-500/10 border border-blue-500/20 rounded-lg p-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                        className: \"text-blue-400 font-semibold mb-2\",\n                        children: \"\\uD83D\\uDCA1 Upgrade Tips\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/SubscriptionScreen.tsx\",\n                        lineNumber: 216,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                        className: \"text-blue-300 text-sm space-y-1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: \"• Speed upgrades stack - buy multiple for even faster mining\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/SubscriptionScreen.tsx\",\n                                lineNumber: 218,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: \"• All purchases are simulated for this demo\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/SubscriptionScreen.tsx\",\n                                lineNumber: 219,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: \"• In production, this would integrate with real payment processors\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/SubscriptionScreen.tsx\",\n                                lineNumber: 220,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: \"• Your upgrades are permanent and apply to all future mining sessions\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/SubscriptionScreen.tsx\",\n                                lineNumber: 221,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/SubscriptionScreen.tsx\",\n                        lineNumber: 217,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/SubscriptionScreen.tsx\",\n                lineNumber: 215,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/SubscriptionScreen.tsx\",\n        lineNumber: 68,\n        columnNumber: 5\n    }, this);\n}\n_s(SubscriptionScreen, \"1EenzStgAjxrCkt1o5tHaz+iXYk=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth\n    ];\n});\n_c = SubscriptionScreen;\nvar _c;\n$RefreshReg$(_c, \"SubscriptionScreen\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/SubscriptionScreen.tsx\n"));

/***/ })

});