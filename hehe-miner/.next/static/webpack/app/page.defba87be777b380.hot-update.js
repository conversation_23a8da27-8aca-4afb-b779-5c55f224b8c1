"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/lib/api.ts":
/*!************************!*\
  !*** ./src/lib/api.ts ***!
  \************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   apiClient: () => (/* binding */ apiClient)\n/* harmony export */ });\nconst API_BASE_URL = \"http://localhost:3000\" || (0);\nclass ApiClient {\n    setToken(token) {\n        this.token = token;\n    }\n    async request(endpoint) {\n        let options = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};\n        const url = \"\".concat(this.baseURL, \"/api\").concat(endpoint);\n        const headers = {\n            'Content-Type': 'application/json',\n            ...options.headers\n        };\n        if (this.token) {\n            headers.Authorization = \"Bearer \".concat(this.token);\n        }\n        try {\n            const response = await fetch(url, {\n                ...options,\n                headers\n            });\n            const data = await response.json();\n            return data;\n        } catch (error) {\n            console.error('API request failed:', error);\n            return {\n                success: false,\n                error: 'Network error occurred'\n            };\n        }\n    }\n    async get(endpoint) {\n        return this.request(endpoint, {\n            method: 'GET'\n        });\n    }\n    async post(endpoint, data) {\n        return this.request(endpoint, {\n            method: 'POST',\n            body: data ? JSON.stringify(data) : undefined\n        });\n    }\n    async put(endpoint, data) {\n        return this.request(endpoint, {\n            method: 'PUT',\n            body: data ? JSON.stringify(data) : undefined\n        });\n    }\n    async delete(endpoint) {\n        return this.request(endpoint, {\n            method: 'DELETE'\n        });\n    }\n    // Auth methods\n    async loginWithTelegram(telegramData) {\n        return this.post('/auth/telegram', telegramData);\n    }\n    async loginMock() {\n        return this.post('/auth/telegram', {\n            mock: true\n        });\n    }\n    // User methods\n    async getUserProfile() {\n        return this.get('/user/profile');\n    }\n    // Mining methods\n    async startMining() {\n        return this.post('/mining/start');\n    }\n    async claimMining(sessionId) {\n        return this.post('/mining/claim', {\n            sessionId\n        });\n    }\n    // Subscription methods\n    async purchaseBasicPlan() {\n        return this.post('/subscription/basic-plan');\n    }\n    async purchaseSpeedUpgrade() {\n        return this.post('/subscription/speed-upgrade');\n    }\n    // Tasks methods\n    async getTasks() {\n        return this.get('/tasks');\n    }\n    async completeTask(taskId) {\n        return this.post('/tasks/complete', {\n            taskId\n        });\n    }\n    // Referrals methods\n    async getReferrals() {\n        return this.get('/referrals');\n    }\n    async createReferral(referredTelegramId) {\n        return this.post('/referrals', {\n            referredTelegramId\n        });\n    }\n    constructor(baseURL){\n        this.token = null;\n        this.baseURL = baseURL;\n    }\n}\nconst apiClient = new ApiClient(API_BASE_URL);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/api.ts\n"));

/***/ })

});