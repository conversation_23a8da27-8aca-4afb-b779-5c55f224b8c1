"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/MiningScreen.tsx":
/*!*****************************************!*\
  !*** ./src/components/MiningScreen.tsx ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ MiningScreen)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./src/lib/api.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction MiningScreen() {\n    var _user_totalBalance, _user_miningPower;\n    _s();\n    const { user, updateUser } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [timeRemaining, setTimeRemaining] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [canClaim, setCanClaim] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [success, setSuccess] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"MiningScreen.useEffect\": ()=>{\n            if (user === null || user === void 0 ? void 0 : user.currentMiningSession) {\n                const interval = setInterval({\n                    \"MiningScreen.useEffect.interval\": ()=>{\n                        updateMiningTimer();\n                    }\n                }[\"MiningScreen.useEffect.interval\"], 1000);\n                return ({\n                    \"MiningScreen.useEffect\": ()=>clearInterval(interval)\n                })[\"MiningScreen.useEffect\"];\n            }\n        }\n    }[\"MiningScreen.useEffect\"], [\n        user === null || user === void 0 ? void 0 : user.currentMiningSession\n    ]);\n    const updateMiningTimer = ()=>{\n        if (!(user === null || user === void 0 ? void 0 : user.currentMiningSession)) return;\n        const startTime = new Date(user.currentMiningSession.startTime).getTime();\n        const miningDuration = 4 * 60 * 60 * 1000 // 4 hours\n        ;\n        const endTime = startTime + miningDuration;\n        const now = Date.now();\n        const remaining = endTime - now;\n        if (remaining <= 0) {\n            setTimeRemaining(0);\n            setCanClaim(true);\n        } else {\n            setTimeRemaining(remaining);\n            setCanClaim(false);\n        }\n    };\n    const formatTime = (milliseconds)=>{\n        const hours = Math.floor(milliseconds / (1000 * 60 * 60));\n        const minutes = Math.floor(milliseconds % (1000 * 60 * 60) / (1000 * 60));\n        const seconds = Math.floor(milliseconds % (1000 * 60) / 1000);\n        return \"\".concat(hours.toString().padStart(2, '0'), \":\").concat(minutes.toString().padStart(2, '0'), \":\").concat(seconds.toString().padStart(2, '0'));\n    };\n    const handleStartMining = async ()=>{\n        if (!(user === null || user === void 0 ? void 0 : user.hasBasicPlan)) {\n            setError('You need to purchase the basic plan first!');\n            return;\n        }\n        setIsLoading(true);\n        setError('');\n        setSuccess('');\n        try {\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_3__.apiClient.startMining();\n            if (response.success) {\n                setSuccess('Mining started successfully!');\n                // Refresh user profile\n                const profileResponse = await _lib_api__WEBPACK_IMPORTED_MODULE_3__.apiClient.getUserProfile();\n                if (profileResponse.success) {\n                    updateUser(profileResponse.user);\n                }\n            } else {\n                setError(response.error || 'Failed to start mining');\n            }\n        } catch (error) {\n            setError('Network error occurred');\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const handleClaimTokens = async ()=>{\n        if (!(user === null || user === void 0 ? void 0 : user.currentMiningSession)) return;\n        setIsLoading(true);\n        setError('');\n        setSuccess('');\n        try {\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_3__.apiClient.claimMining(user.currentMiningSession.id);\n            if (response.success) {\n                setSuccess(\"Claimed \".concat(response.tokensEarned, \" HEHE tokens!\"));\n                // Refresh user profile\n                const profileResponse = await _lib_api__WEBPACK_IMPORTED_MODULE_3__.apiClient.getUserProfile();\n                if (profileResponse.success) {\n                    updateUser(profileResponse.user);\n                }\n            } else {\n                setError(response.error || 'Failed to claim tokens');\n            }\n        } catch (error) {\n            setError('Network error occurred');\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const getMiningProgress = ()=>{\n        if (!(user === null || user === void 0 ? void 0 : user.currentMiningSession) || timeRemaining <= 0) return 100;\n        const startTime = new Date(user.currentMiningSession.startTime).getTime();\n        const miningDuration = 4 * 60 * 60 * 1000 // 4 hours\n        ;\n        const elapsed = Date.now() - startTime;\n        return Math.min(elapsed / miningDuration * 100, 100);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"p-6 max-w-md mx-auto relative\",\n        children: [\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-red-500/20 border border-red-400/30 rounded-xl p-4 mb-6 glass animate-bounce-in\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-red-400 text-xl animate-bounce\",\n                            children: \"⚠️\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                            lineNumber: 123,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-red-300 font-medium\",\n                            children: error\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                            lineNumber: 124,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                    lineNumber: 122,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                lineNumber: 121,\n                columnNumber: 9\n            }, this),\n            success && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-green-500/20 border border-green-400/30 rounded-xl p-4 mb-6 glass animate-bounce-in\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-green-400 text-xl animate-bounce\",\n                            children: \"\\uD83C\\uDF89\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                            lineNumber: 132,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-green-300 font-medium\",\n                            children: success\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                            lineNumber: 133,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                    lineNumber: 131,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                lineNumber: 130,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"glass-dark rounded-3xl p-8 mb-8 border border-white/20 hover-lift animate-slide-up relative overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-gradient-to-r from-yellow-400/10 via-orange-500/10 to-red-500/10 animate-pulse\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                        lineNumber: 141,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center relative z-10\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-32 h-32 bg-gradient-to-r from-yellow-400 via-orange-500 to-red-500 rounded-full mx-auto flex items-center justify-center animate-pulse-glow relative\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-4xl animate-float\",\n                                                children: \"⛏️\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                                lineNumber: 146,\n                                                columnNumber: 15\n                                            }, this),\n                                            (user === null || user === void 0 ? void 0 : user.currentMiningSession) && timeRemaining > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute inset-0 rounded-full border-4 border-transparent border-t-white animate-spin\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                                lineNumber: 148,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                        lineNumber: 145,\n                                        columnNumber: 13\n                                    }, this),\n                                    canClaim && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute -top-2 -right-2 w-8 h-8 bg-gradient-to-r from-green-400 to-blue-500 rounded-full animate-ping\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                        lineNumber: 152,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                lineNumber: 144,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-3xl font-bold bg-gradient-to-r from-yellow-400 via-orange-500 to-red-500 bg-clip-text text-transparent mb-4\",\n                                children: \"Mining Status\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                lineNumber: 156,\n                                columnNumber: 11\n                            }, this),\n                            !(user === null || user === void 0 ? void 0 : user.hasBasicPlan) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center animate-slide-up\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-300 mb-6 text-lg\",\n                                        children: \"Purchase the basic plan to start mining! \\uD83D\\uDE80\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                        lineNumber: 162,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"glass rounded-2xl p-6 border border-yellow-400/30\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-center space-x-3 mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-3xl animate-bounce\",\n                                                        children: \"\\uD83D\\uDCA1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                                        lineNumber: 165,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-2xl animate-bounce\",\n                                                        style: {\n                                                            animationDelay: '0.2s'\n                                                        },\n                                                        children: \"⛏️\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                                        lineNumber: 166,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-3xl animate-bounce\",\n                                                        style: {\n                                                            animationDelay: '0.4s'\n                                                        },\n                                                        children: \"\\uD83D\\uDC8E\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                                        lineNumber: 167,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                                lineNumber: 164,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-yellow-300 font-bold text-lg mb-2\",\n                                                children: \"Basic Plan: $1\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                                lineNumber: 169,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-300\",\n                                                children: \"Unlock mining 4 HEHE tokens every 4 hours\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                                lineNumber: 172,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                        lineNumber: 163,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                lineNumber: 161,\n                                columnNumber: 13\n                            }, this) : (user === null || user === void 0 ? void 0 : user.currentMiningSession) && timeRemaining > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"animate-slide-up\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-300 mb-6 text-lg font-medium\",\n                                        children: \"⚡ Mining in progress... ⚡\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                        lineNumber: 179,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-gray-700/50 rounded-full h-4 mb-3 overflow-hidden\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-gradient-to-r from-yellow-400 via-orange-500 to-red-500 h-4 rounded-full transition-all duration-1000 relative\",\n                                                    style: {\n                                                        width: \"\".concat(getMiningProgress(), \"%\")\n                                                    },\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent shimmer\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                                        lineNumber: 186,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                                    lineNumber: 182,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                                lineNumber: 181,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-white font-semibold\",\n                                                children: [\n                                                    \"Progress: \",\n                                                    getMiningProgress().toFixed(1),\n                                                    \"% ⚡\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                                lineNumber: 189,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                        lineNumber: 180,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-800/80 backdrop-blur-sm rounded-2xl p-6 mb-4 border border-yellow-400/30\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-4xl font-mono text-yellow-400 mb-3 animate-pulse font-bold\",\n                                                children: formatTime(timeRemaining)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                                lineNumber: 194,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-white text-lg font-medium\",\n                                                children: \"Time remaining ⏰\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                                lineNumber: 197,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                        lineNumber: 193,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-center space-x-2 text-white\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-2xl animate-coin-flip\",\n                                                children: \"\\uD83D\\uDCB0\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                                lineNumber: 200,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-lg font-bold\",\n                                                children: [\n                                                    \"Earning: \",\n                                                    user.currentMiningSession.tokensEarned,\n                                                    \" HEHE\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                                lineNumber: 201,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-2xl animate-coin-flip\",\n                                                style: {\n                                                    animationDelay: '0.5s'\n                                                },\n                                                children: \"\\uD83D\\uDCB0\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                                lineNumber: 202,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                        lineNumber: 199,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                lineNumber: 178,\n                                columnNumber: 13\n                            }, this) : canClaim && (user === null || user === void 0 ? void 0 : user.currentMiningSession) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"animate-bounce-in\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold bg-gradient-to-r from-green-400 to-blue-500 bg-clip-text text-transparent mb-4\",\n                                                children: \"\\uD83C\\uDF89 Mining Complete! \\uD83C\\uDF89\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                                lineNumber: 208,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-gray-800/90 backdrop-blur-sm rounded-2xl p-6 mb-6 border border-green-400/30\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-center space-x-3 mb-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-3xl animate-bounce\",\n                                                                children: \"\\uD83D\\uDC8E\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                                                lineNumber: 213,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-2xl font-bold text-white\",\n                                                                children: [\n                                                                    user.currentMiningSession.tokensEarned,\n                                                                    \" HEHE\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                                                lineNumber: 214,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-3xl animate-bounce\",\n                                                                style: {\n                                                                    animationDelay: '0.3s'\n                                                                },\n                                                                children: \"\\uD83D\\uDC8E\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                                                lineNumber: 215,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                                        lineNumber: 212,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-white\",\n                                                        children: \"Ready to claim your rewards!\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                                        lineNumber: 217,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                                lineNumber: 211,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                        lineNumber: 207,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleClaimTokens,\n                                        disabled: isLoading,\n                                        className: \"group w-full bg-gradient-to-r from-green-500 via-green-600 to-blue-600 hover:from-green-600 hover:via-green-700 hover:to-blue-700 disabled:from-gray-600 disabled:via-gray-700 disabled:to-gray-800 text-white font-bold py-4 px-6 rounded-xl transition-all duration-300 transform hover:scale-105 disabled:transform-none shadow-lg hover:shadow-green-500/25 relative overflow-hidden\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 shimmer\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                                lineNumber: 225,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"relative z-10 text-lg\",\n                                                children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-center space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"animate-spin rounded-full h-5 w-5 border-2 border-white border-t-transparent\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                                            lineNumber: 229,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"Claiming...\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                                            lineNumber: 230,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                                    lineNumber: 228,\n                                                    columnNumber: 21\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-center space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"group-hover:animate-bounce\",\n                                                            children: \"\\uD83D\\uDCB0\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                                            lineNumber: 234,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"Claim Tokens\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                                            lineNumber: 235,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"group-hover:animate-bounce\",\n                                                            children: \"\\uD83D\\uDCB0\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                                            lineNumber: 236,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                                    lineNumber: 233,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                                lineNumber: 226,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                        lineNumber: 220,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                lineNumber: 206,\n                                columnNumber: 13\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"animate-slide-up\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-white mb-6 text-lg font-medium\",\n                                        children: \"\\uD83D\\uDE80 Ready to mine! \\uD83D\\uDE80\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                        lineNumber: 244,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-800/90 backdrop-blur-sm rounded-2xl p-6 mb-6 border border-blue-400/30\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-center space-x-3 mb-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-2xl animate-pulse\",\n                                                        children: \"⚡\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                                        lineNumber: 247,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-lg font-bold text-white\",\n                                                        children: [\n                                                            \"Mining Power: \",\n                                                            user === null || user === void 0 ? void 0 : user.miningPower,\n                                                            \" HEHE/4h\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                                        lineNumber: 248,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-2xl animate-pulse\",\n                                                        style: {\n                                                            animationDelay: '0.5s'\n                                                        },\n                                                        children: \"⚡\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                                        lineNumber: 249,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                                lineNumber: 246,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-white\",\n                                                children: \"Start your mining session now!\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                                lineNumber: 251,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                        lineNumber: 245,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleStartMining,\n                                        disabled: isLoading,\n                                        className: \"group w-full bg-gradient-to-r from-yellow-500 via-orange-500 to-red-500 hover:from-yellow-600 hover:via-orange-600 hover:to-red-600 disabled:from-gray-600 disabled:via-gray-700 disabled:to-gray-800 text-white font-bold py-4 px-6 rounded-xl transition-all duration-300 transform hover:scale-105 disabled:transform-none shadow-lg hover:shadow-yellow-500/25 relative overflow-hidden\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 shimmer\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                                lineNumber: 258,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"relative z-10 text-lg\",\n                                                children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-center space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"animate-spin rounded-full h-5 w-5 border-2 border-white border-t-transparent\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                                            lineNumber: 262,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"Starting...\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                                            lineNumber: 263,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                                    lineNumber: 261,\n                                                    columnNumber: 21\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-center space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"group-hover:animate-bounce\",\n                                                            children: \"⛏️\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                                            lineNumber: 267,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"Start Mining\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                                            lineNumber: 268,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"group-hover:animate-bounce\",\n                                                            children: \"⛏️\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                                            lineNumber: 269,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                                    lineNumber: 266,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                                lineNumber: 259,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                        lineNumber: 253,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                lineNumber: 243,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                        lineNumber: 143,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                lineNumber: 139,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-2 gap-4 animate-slide-up\",\n                style: {\n                    animationDelay: '0.3s'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gray-800/90 backdrop-blur-sm rounded-2xl p-6 border border-yellow-400/30 hover-lift group\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2 mb-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-yellow-400 text-xl group-hover:animate-coin-flip\",\n                                        children: \"\\uD83D\\uDCB0\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                        lineNumber: 283,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-white font-medium\",\n                                        children: \"Total Balance\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                        lineNumber: 284,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                lineNumber: 282,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-2xl font-bold text-yellow-400\",\n                                children: [\n                                    (user === null || user === void 0 ? void 0 : (_user_totalBalance = user.totalBalance) === null || _user_totalBalance === void 0 ? void 0 : _user_totalBalance.toFixed(2)) || '0.00',\n                                    \" HEHE\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                lineNumber: 286,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                        lineNumber: 281,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gray-800/90 backdrop-blur-sm rounded-2xl p-6 border border-blue-400/30 hover-lift group\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2 mb-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-blue-400 text-xl group-hover:animate-pulse\",\n                                        children: \"⚡\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                        lineNumber: 292,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-white font-medium\",\n                                        children: \"Mining Power\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                        lineNumber: 293,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                lineNumber: 291,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-2xl font-bold text-blue-400\",\n                                children: [\n                                    (user === null || user === void 0 ? void 0 : (_user_miningPower = user.miningPower) === null || _user_miningPower === void 0 ? void 0 : _user_miningPower.toFixed(2)) || '4.00',\n                                    \"/4h\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                lineNumber: 295,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                        lineNumber: 290,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                lineNumber: 280,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n        lineNumber: 118,\n        columnNumber: 5\n    }, this);\n}\n_s(MiningScreen, \"QrLVhiFnbJ7nKmpdWPloWf9fPJg=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth\n    ];\n});\n_c = MiningScreen;\nvar _c;\n$RefreshReg$(_c, \"MiningScreen\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/MiningScreen.tsx\n"));

/***/ })

});