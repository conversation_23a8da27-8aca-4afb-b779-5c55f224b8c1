"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/MiningScreen.tsx":
/*!*****************************************!*\
  !*** ./src/components/MiningScreen.tsx ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ MiningScreen)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./src/lib/api.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction MiningScreen() {\n    var _user_totalBalance, _user_miningPower;\n    _s();\n    const { user, updateUser } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [timeRemaining, setTimeRemaining] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [canClaim, setCanClaim] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [success, setSuccess] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"MiningScreen.useEffect\": ()=>{\n            if (user === null || user === void 0 ? void 0 : user.currentMiningSession) {\n                const interval = setInterval({\n                    \"MiningScreen.useEffect.interval\": ()=>{\n                        updateMiningTimer();\n                    }\n                }[\"MiningScreen.useEffect.interval\"], 1000);\n                return ({\n                    \"MiningScreen.useEffect\": ()=>clearInterval(interval)\n                })[\"MiningScreen.useEffect\"];\n            }\n        }\n    }[\"MiningScreen.useEffect\"], [\n        user === null || user === void 0 ? void 0 : user.currentMiningSession\n    ]);\n    const updateMiningTimer = ()=>{\n        if (!(user === null || user === void 0 ? void 0 : user.currentMiningSession)) return;\n        const startTime = new Date(user.currentMiningSession.startTime).getTime();\n        const miningDuration = 4 * 60 * 60 * 1000 // 4 hours\n        ;\n        const endTime = startTime + miningDuration;\n        const now = Date.now();\n        const remaining = endTime - now;\n        if (remaining <= 0) {\n            setTimeRemaining(0);\n            setCanClaim(true);\n        } else {\n            setTimeRemaining(remaining);\n            setCanClaim(false);\n        }\n    };\n    const formatTime = (milliseconds)=>{\n        const hours = Math.floor(milliseconds / (1000 * 60 * 60));\n        const minutes = Math.floor(milliseconds % (1000 * 60 * 60) / (1000 * 60));\n        const seconds = Math.floor(milliseconds % (1000 * 60) / 1000);\n        return \"\".concat(hours.toString().padStart(2, '0'), \":\").concat(minutes.toString().padStart(2, '0'), \":\").concat(seconds.toString().padStart(2, '0'));\n    };\n    const handleStartMining = async ()=>{\n        if (!(user === null || user === void 0 ? void 0 : user.hasBasicPlan)) {\n            setError('You need to purchase the basic plan first!');\n            return;\n        }\n        setIsLoading(true);\n        setError('');\n        setSuccess('');\n        try {\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_3__.apiClient.startMining();\n            if (response.success) {\n                setSuccess('Mining started successfully!');\n                // Refresh user profile\n                const profileResponse = await _lib_api__WEBPACK_IMPORTED_MODULE_3__.apiClient.getUserProfile();\n                if (profileResponse.success) {\n                    updateUser(profileResponse.user);\n                }\n            } else {\n                setError(response.error || 'Failed to start mining');\n            }\n        } catch (error) {\n            setError('Network error occurred');\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const handleClaimTokens = async ()=>{\n        if (!(user === null || user === void 0 ? void 0 : user.currentMiningSession)) return;\n        setIsLoading(true);\n        setError('');\n        setSuccess('');\n        try {\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_3__.apiClient.claimMining(user.currentMiningSession.id);\n            if (response.success) {\n                setSuccess(\"Claimed \".concat(response.tokensEarned, \" HEHE tokens!\"));\n                // Refresh user profile\n                const profileResponse = await _lib_api__WEBPACK_IMPORTED_MODULE_3__.apiClient.getUserProfile();\n                if (profileResponse.success) {\n                    updateUser(profileResponse.user);\n                }\n            } else {\n                setError(response.error || 'Failed to claim tokens');\n            }\n        } catch (error) {\n            setError('Network error occurred');\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const getMiningProgress = ()=>{\n        if (!(user === null || user === void 0 ? void 0 : user.currentMiningSession) || timeRemaining <= 0) return 100;\n        const startTime = new Date(user.currentMiningSession.startTime).getTime();\n        const miningDuration = 4 * 60 * 60 * 1000 // 4 hours\n        ;\n        const elapsed = Date.now() - startTime;\n        return Math.min(elapsed / miningDuration * 100, 100);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"p-6 max-w-md mx-auto relative\",\n        children: [\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-red-500/20 border border-red-400/30 rounded-xl p-4 mb-6 glass animate-bounce-in\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-red-400 text-xl animate-bounce\",\n                            children: \"⚠️\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                            lineNumber: 122,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-red-300 font-medium\",\n                            children: error\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                            lineNumber: 123,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                    lineNumber: 121,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                lineNumber: 120,\n                columnNumber: 9\n            }, this),\n            success && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-green-500/20 border border-green-400/30 rounded-xl p-4 mb-6 glass animate-bounce-in\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-green-400 text-xl animate-bounce\",\n                            children: \"\\uD83C\\uDF89\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                            lineNumber: 131,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-green-300 font-medium\",\n                            children: success\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                            lineNumber: 132,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                    lineNumber: 130,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                lineNumber: 129,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"glass-dark rounded-3xl p-8 mb-8 border border-white/20 hover-lift animate-slide-up relative overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-gradient-to-r from-yellow-400/10 via-orange-500/10 to-red-500/10 animate-pulse\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                        lineNumber: 140,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center relative z-10\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-32 h-32 bg-gradient-to-r from-yellow-400 via-orange-500 to-red-500 rounded-full mx-auto flex items-center justify-center animate-pulse-glow relative\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-4xl animate-float\",\n                                                children: \"⛏️\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                                lineNumber: 145,\n                                                columnNumber: 15\n                                            }, this),\n                                            (user === null || user === void 0 ? void 0 : user.currentMiningSession) && timeRemaining > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute inset-0 rounded-full border-4 border-transparent border-t-white animate-spin\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                                lineNumber: 147,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                        lineNumber: 144,\n                                        columnNumber: 13\n                                    }, this),\n                                    canClaim && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute -top-2 -right-2 w-8 h-8 bg-gradient-to-r from-green-400 to-blue-500 rounded-full animate-ping\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                        lineNumber: 151,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                lineNumber: 143,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-3xl font-bold bg-gradient-to-r from-yellow-400 via-orange-500 to-red-500 bg-clip-text text-transparent mb-4\",\n                                children: \"Mining Status\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                lineNumber: 155,\n                                columnNumber: 11\n                            }, this),\n                            !(user === null || user === void 0 ? void 0 : user.hasBasicPlan) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center animate-slide-up\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-300 mb-6 text-lg\",\n                                        children: \"Purchase the basic plan to start mining! \\uD83D\\uDE80\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                        lineNumber: 161,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"glass rounded-2xl p-6 border border-yellow-400/30\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-center space-x-3 mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-3xl animate-bounce\",\n                                                        children: \"\\uD83D\\uDCA1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                                        lineNumber: 164,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-2xl animate-bounce\",\n                                                        style: {\n                                                            animationDelay: '0.2s'\n                                                        },\n                                                        children: \"⛏️\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                                        lineNumber: 165,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-3xl animate-bounce\",\n                                                        style: {\n                                                            animationDelay: '0.4s'\n                                                        },\n                                                        children: \"\\uD83D\\uDC8E\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                                        lineNumber: 166,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                                lineNumber: 163,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-yellow-300 font-bold text-lg mb-2\",\n                                                children: \"Basic Plan: $1\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                                lineNumber: 168,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-300\",\n                                                children: \"Unlock mining 4 HEHE tokens every 4 hours\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                                lineNumber: 171,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                        lineNumber: 162,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                lineNumber: 160,\n                                columnNumber: 13\n                            }, this) : (user === null || user === void 0 ? void 0 : user.currentMiningSession) && timeRemaining > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"animate-slide-up\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-300 mb-6 text-lg font-medium\",\n                                        children: \"⚡ Mining in progress... ⚡\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                        lineNumber: 178,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-gray-700/50 rounded-full h-4 mb-3 overflow-hidden\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-gradient-to-r from-yellow-400 via-orange-500 to-red-500 h-4 rounded-full transition-all duration-1000 relative\",\n                                                    style: {\n                                                        width: \"\".concat(getMiningProgress(), \"%\")\n                                                    },\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent shimmer\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                                        lineNumber: 185,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                                    lineNumber: 181,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                                lineNumber: 180,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-300 font-semibold\",\n                                                children: [\n                                                    \"Progress: \",\n                                                    getMiningProgress().toFixed(1),\n                                                    \"% ⚡\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                                lineNumber: 188,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                        lineNumber: 179,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-800/80 backdrop-blur-sm rounded-2xl p-6 mb-4 border border-yellow-400/30\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-4xl font-mono text-yellow-400 mb-3 animate-pulse font-bold\",\n                                                children: formatTime(timeRemaining)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                                lineNumber: 193,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-white text-lg font-medium\",\n                                                children: \"Time remaining ⏰\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                                lineNumber: 196,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                        lineNumber: 192,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-center space-x-2 text-white\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-2xl animate-coin-flip\",\n                                                children: \"\\uD83D\\uDCB0\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                                lineNumber: 199,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-lg font-bold\",\n                                                children: [\n                                                    \"Earning: \",\n                                                    user.currentMiningSession.tokensEarned,\n                                                    \" HEHE\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                                lineNumber: 200,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-2xl animate-coin-flip\",\n                                                style: {\n                                                    animationDelay: '0.5s'\n                                                },\n                                                children: \"\\uD83D\\uDCB0\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                                lineNumber: 201,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                        lineNumber: 198,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                lineNumber: 177,\n                                columnNumber: 13\n                            }, this) : canClaim && (user === null || user === void 0 ? void 0 : user.currentMiningSession) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"animate-bounce-in\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold bg-gradient-to-r from-green-400 to-blue-500 bg-clip-text text-transparent mb-4\",\n                                                children: \"\\uD83C\\uDF89 Mining Complete! \\uD83C\\uDF89\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                                lineNumber: 207,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-gray-800/90 backdrop-blur-sm rounded-2xl p-6 mb-6 border border-green-400/30\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-center space-x-3 mb-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-3xl animate-bounce\",\n                                                                children: \"\\uD83D\\uDC8E\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                                                lineNumber: 212,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-2xl font-bold text-white\",\n                                                                children: [\n                                                                    user.currentMiningSession.tokensEarned,\n                                                                    \" HEHE\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                                                lineNumber: 213,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-3xl animate-bounce\",\n                                                                style: {\n                                                                    animationDelay: '0.3s'\n                                                                },\n                                                                children: \"\\uD83D\\uDC8E\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                                                lineNumber: 214,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                                        lineNumber: 211,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-white\",\n                                                        children: \"Ready to claim your rewards!\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                                        lineNumber: 216,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                                lineNumber: 210,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                        lineNumber: 206,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleClaimTokens,\n                                        disabled: isLoading,\n                                        className: \"group w-full bg-gradient-to-r from-green-500 via-green-600 to-blue-600 hover:from-green-600 hover:via-green-700 hover:to-blue-700 disabled:from-gray-600 disabled:via-gray-700 disabled:to-gray-800 text-white font-bold py-4 px-6 rounded-xl transition-all duration-300 transform hover:scale-105 disabled:transform-none shadow-lg hover:shadow-green-500/25 relative overflow-hidden\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 shimmer\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                                lineNumber: 224,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"relative z-10 text-lg\",\n                                                children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-center space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"animate-spin rounded-full h-5 w-5 border-2 border-white border-t-transparent\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                                            lineNumber: 228,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"Claiming...\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                                            lineNumber: 229,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                                    lineNumber: 227,\n                                                    columnNumber: 21\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-center space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"group-hover:animate-bounce\",\n                                                            children: \"\\uD83D\\uDCB0\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                                            lineNumber: 233,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"Claim Tokens\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                                            lineNumber: 234,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"group-hover:animate-bounce\",\n                                                            children: \"\\uD83D\\uDCB0\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                                            lineNumber: 235,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                                    lineNumber: 232,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                                lineNumber: 225,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                        lineNumber: 219,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                lineNumber: 205,\n                                columnNumber: 13\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"animate-slide-up\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-300 mb-6 text-lg font-medium\",\n                                        children: \"\\uD83D\\uDE80 Ready to mine! \\uD83D\\uDE80\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                        lineNumber: 243,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"glass rounded-2xl p-6 mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-center space-x-3 mb-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-2xl animate-pulse\",\n                                                        children: \"⚡\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                                        lineNumber: 246,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-lg font-bold text-white\",\n                                                        children: [\n                                                            \"Mining Power: \",\n                                                            user === null || user === void 0 ? void 0 : user.miningPower,\n                                                            \" HEHE/4h\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                                        lineNumber: 247,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-2xl animate-pulse\",\n                                                        style: {\n                                                            animationDelay: '0.5s'\n                                                        },\n                                                        children: \"⚡\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                                        lineNumber: 248,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                                lineNumber: 245,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-300\",\n                                                children: \"Start your mining session now!\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                                lineNumber: 250,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                        lineNumber: 244,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleStartMining,\n                                        disabled: isLoading,\n                                        className: \"group w-full bg-gradient-to-r from-yellow-500 via-orange-500 to-red-500 hover:from-yellow-600 hover:via-orange-600 hover:to-red-600 disabled:from-gray-600 disabled:via-gray-700 disabled:to-gray-800 text-white font-bold py-4 px-6 rounded-xl transition-all duration-300 transform hover:scale-105 disabled:transform-none shadow-lg hover:shadow-yellow-500/25 relative overflow-hidden\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 shimmer\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                                lineNumber: 257,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"relative z-10 text-lg\",\n                                                children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-center space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"animate-spin rounded-full h-5 w-5 border-2 border-white border-t-transparent\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                                            lineNumber: 261,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"Starting...\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                                            lineNumber: 262,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                                    lineNumber: 260,\n                                                    columnNumber: 21\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-center space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"group-hover:animate-bounce\",\n                                                            children: \"⛏️\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                                            lineNumber: 266,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"Start Mining\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                                            lineNumber: 267,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"group-hover:animate-bounce\",\n                                                            children: \"⛏️\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                                            lineNumber: 268,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                                    lineNumber: 265,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                                lineNumber: 258,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                        lineNumber: 252,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                lineNumber: 242,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                        lineNumber: 142,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                lineNumber: 138,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-2 gap-4 animate-slide-up\",\n                style: {\n                    animationDelay: '0.3s'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gray-800/90 backdrop-blur-sm rounded-2xl p-6 border border-yellow-400/30 hover-lift group\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2 mb-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-yellow-400 text-xl group-hover:animate-coin-flip\",\n                                        children: \"\\uD83D\\uDCB0\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                        lineNumber: 282,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-white font-medium\",\n                                        children: \"Total Balance\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                        lineNumber: 283,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                lineNumber: 281,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-2xl font-bold text-yellow-400\",\n                                children: [\n                                    (user === null || user === void 0 ? void 0 : (_user_totalBalance = user.totalBalance) === null || _user_totalBalance === void 0 ? void 0 : _user_totalBalance.toFixed(2)) || '0.00',\n                                    \" HEHE\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                lineNumber: 285,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                        lineNumber: 280,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gray-800/90 backdrop-blur-sm rounded-2xl p-6 border border-blue-400/30 hover-lift group\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2 mb-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-blue-400 text-xl group-hover:animate-pulse\",\n                                        children: \"⚡\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                        lineNumber: 291,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-white font-medium\",\n                                        children: \"Mining Power\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                        lineNumber: 292,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                lineNumber: 290,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-2xl font-bold text-blue-400\",\n                                children: [\n                                    (user === null || user === void 0 ? void 0 : (_user_miningPower = user.miningPower) === null || _user_miningPower === void 0 ? void 0 : _user_miningPower.toFixed(2)) || '4.00',\n                                    \"/4h\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                                lineNumber: 294,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                        lineNumber: 289,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n                lineNumber: 279,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx\",\n        lineNumber: 117,\n        columnNumber: 5\n    }, this);\n}\n_s(MiningScreen, \"QrLVhiFnbJ7nKmpdWPloWf9fPJg=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth\n    ];\n});\n_c = MiningScreen;\nvar _c;\n$RefreshReg$(_c, \"MiningScreen\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/MiningScreen.tsx\n"));

/***/ })

});