(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[177],{283:(e,t,r)=>{"use strict";r.d(t,{A:()=>s,AuthProvider:()=>u});var o=r(5155),a=r(2115);let l=(0,a.createContext)(void 0);function u(e){let{children:t}=e,[r,u]=(0,a.useState)(null),[s,n]=(0,a.useState)(null),[i,c]=(0,a.useState)(!0);return(0,a.useEffect)(()=>{let e=localStorage.getItem("auth_token"),t=localStorage.getItem("user_data");if(e&&t)try{n(e),u(JSON.parse(t))}catch(e){console.error("Error parsing stored user data:",e),localStorage.removeItem("auth_token"),localStorage.removeItem("user_data")}c(!1)},[]),(0,o.jsx)(l.Provider,{value:{user:r,token:s,login:(e,t)=>{u(e),n(t),localStorage.setItem("auth_token",t),localStorage.setItem("user_data",JSON.stringify(e))},logout:()=>{u(null),n(null),localStorage.removeItem("auth_token"),localStorage.removeItem("user_data")},updateUser:e=>{if(r){let t={...r,...e};u(t),localStorage.setItem("user_data",JSON.stringify(t))}},isLoading:i},children:t})}function s(){let e=(0,a.useContext)(l);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}},347:()=>{},8652:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,347,23)),Promise.resolve().then(r.bind(r,283))}},e=>{var t=t=>e(e.s=t);e.O(0,[690,441,684,358],()=>t(8652)),_N_E=e.O()}]);