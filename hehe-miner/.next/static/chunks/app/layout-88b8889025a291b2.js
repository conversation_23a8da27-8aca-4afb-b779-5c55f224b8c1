(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[177],{283:(e,t,a)=>{"use strict";a.d(t,{A:()=>n,AuthProvider:()=>s});var r=a(5155),o=a(2115);let l=(0,o.createContext)(void 0);function s(e){let{children:t}=e,[a,s]=(0,o.useState)(null),[n,i]=(0,o.useState)(null),[u,c]=(0,o.useState)(!0);return(0,o.useEffect)(()=>{let e=localStorage.getItem("auth_token"),t=localStorage.getItem("user_data");if(e&&t)try{i(e),s(JSON.parse(t))}catch(e){console.error("Error parsing stored user data:",e),localStorage.removeItem("auth_token"),localStorage.removeItem("user_data")}c(!1)},[]),(0,r.jsx)(l.Provider,{value:{user:a,token:n,login:(e,t)=>{s(e),i(t),localStorage.setItem("auth_token",t),localStorage.setItem("user_data",JSON.stringify(e))},logout:()=>{s(null),i(null),localStorage.removeItem("auth_token"),localStorage.removeItem("user_data")},updateUser:e=>{if(a){let t={...a,...e};s(t),localStorage.setItem("user_data",JSON.stringify(t))}},isLoading:u},children:t})}function n(){let e=(0,o.useContext)(l);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}},347:()=>{},3280:(e,t,a)=>{Promise.resolve().then(a.t.bind(a,4147,23)),Promise.resolve().then(a.t.bind(a,8489,23)),Promise.resolve().then(a.t.bind(a,347,23)),Promise.resolve().then(a.bind(a,283))},4147:e=>{e.exports={style:{fontFamily:"'Geist', 'Geist Fallback'",fontStyle:"normal"},className:"__className_5cfdac",variable:"__variable_5cfdac"}},8489:e=>{e.exports={style:{fontFamily:"'Geist Mono', 'Geist Mono Fallback'",fontStyle:"normal"},className:"__className_9a8899",variable:"__variable_9a8899"}}},e=>{var t=t=>e(e.s=t);e.O(0,[896,441,684,358],()=>t(3280)),_N_E=e.O()}]);