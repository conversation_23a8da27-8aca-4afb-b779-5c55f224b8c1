(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[974],{283:(e,s,t)=>{"use strict";t.d(s,{A:()=>i,AuthProvider:()=>n});var a=t(5155),r=t(2115);let l=(0,r.createContext)(void 0);function n(e){let{children:s}=e,[t,n]=(0,r.useState)(null),[i,o]=(0,r.useState)(null),[d,c]=(0,r.useState)(!0);return(0,r.useEffect)(()=>{let e=localStorage.getItem("auth_token"),s=localStorage.getItem("user_data");if(e&&s)try{o(e),n(JSON.parse(s))}catch(e){console.error("Error parsing stored user data:",e),localStorage.removeItem("auth_token"),localStorage.removeItem("user_data")}c(!1)},[]),(0,a.jsx)(l.Provider,{value:{user:t,token:i,login:(e,s)=>{n(e),o(s),localStorage.setItem("auth_token",s),localStorage.setItem("user_data",JSON.stringify(e))},logout:()=>{n(null),o(null),localStorage.removeItem("auth_token"),localStorage.removeItem("user_data")},updateUser:e=>{if(t){let s={...t,...e};n(s),localStorage.setItem("user_data",JSON.stringify(s))}},isLoading:d},children:s})}function i(){let e=(0,r.useContext)(l);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}},1730:(e,s,t)=>{Promise.resolve().then(t.bind(t,9663))},9663:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>N});var a=t(5155),r=t(283),l=t(2115);class n{setToken(e){this.token=e}async request(e){let s=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},t="".concat(this.baseURL,"/api").concat(e),a={"Content-Type":"application/json",...s.headers};this.token&&(a.Authorization="Bearer ".concat(this.token));try{let e=await fetch(t,{...s,headers:a});return await e.json()}catch(e){return console.error("API request failed:",e),{success:!1,error:"Network error occurred"}}}async get(e){return this.request(e,{method:"GET"})}async post(e,s){return this.request(e,{method:"POST",body:s?JSON.stringify(s):void 0})}async put(e,s){return this.request(e,{method:"PUT",body:s?JSON.stringify(s):void 0})}async delete(e){return this.request(e,{method:"DELETE"})}async loginWithTelegram(e){return this.post("/auth/telegram",e)}async loginMock(){return this.post("/auth/telegram",{mock:!0})}async getUserProfile(){return this.get("/user/profile")}async startMining(){return this.post("/mining/start")}async claimMining(e){return this.post("/mining/claim",{sessionId:e})}async purchaseBasicPlan(){return this.post("/subscription/basic-plan")}async purchaseSpeedUpgrade(){return this.post("/subscription/speed-upgrade")}async getTasks(){return this.get("/tasks")}async completeTask(e){return this.post("/tasks/complete",{taskId:e})}async getReferrals(){return this.get("/referrals")}async createReferral(e){return this.post("/referrals",{referredTelegramId:e})}constructor(e){this.token=null,this.baseURL=e}}let i=new n("https://hehe-miner.vercel.app");var o=t(9137),d=t.n(o);function c(){var e;return(null==(e=window.Telegram)?void 0:e.WebApp)?window.Telegram.WebApp:null}function m(e,s){let t=c();(null==t?void 0:t.HapticFeedback)&&("impact"===e?t.HapticFeedback.impactOccurred(s||"medium"):t.HapticFeedback.notificationOccurred(s||"success"))}t(8777),t(9509);var x=t(6766);function u(e){let{onLaunchApp:s}=e,{login:t}=(0,r.A)(),[n,o]=(0,l.useState)(!1),[u,h]=(0,l.useState)(""),[p,g]=(0,l.useState)("Initializing..."),b=["Initializing...","Connecting to Telegram...","Authenticating user...","Loading user data...","Preparing mining interface...","Almost ready..."];(0,l.useEffect)(()=>{let e=c();e&&(e.ready(),e.expand())},[]),(0,l.useEffect)(()=>{if(n){let e=0,s=setInterval(()=>{e=(e+1)%b.length,g(b[e])},800);return()=>clearInterval(s)}},[n]);let f=async()=>{o(!0),h(""),m("impact","medium");try{var e;if(await new Promise(e=>setTimeout(e,1e3)),null==(e=window.Telegram)?void 0:e.WebApp){let e=function(){var e;let s=c();return(null==s||null==(e=s.initDataUnsafe)?void 0:e.user)||null}();if(e){let a=await i.loginWithTelegram(e);if(a.success){m("notification","success"),t(a.user,a.token),s();return}}}let a=await i.loginMock();a.success?(m("notification","success"),t(a.user,a.token),s()):(m("notification","error"),h(a.error||"Authentication failed"))}catch(e){m("notification","error"),h("Network error occurred. Please try again.")}finally{o(!1)}};return n?(0,a.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 flex items-center justify-center relative overflow-hidden",children:[(0,a.jsxs)("div",{className:"absolute inset-0",children:[(0,a.jsx)("div",{className:"absolute top-20 left-20 w-32 h-32 bg-yellow-400 rounded-full opacity-20 animate-pulse"}),(0,a.jsx)("div",{className:"absolute top-40 right-32 w-24 h-24 bg-orange-400 rounded-full opacity-20 animate-pulse delay-300"}),(0,a.jsx)("div",{className:"absolute bottom-32 left-32 w-40 h-40 bg-yellow-300 rounded-full opacity-20 animate-pulse delay-700"}),(0,a.jsx)("div",{className:"absolute bottom-20 right-20 w-28 h-28 bg-orange-300 rounded-full opacity-20 animate-pulse delay-500"})]}),(0,a.jsxs)("div",{className:"text-center z-10",children:[(0,a.jsx)("div",{className:"mb-8 animate-bounce",children:(0,a.jsx)(x.default,{src:"/hehe-logo.svg",alt:"Hehe Miner",width:120,height:120,className:"mx-auto drop-shadow-2xl"})}),(0,a.jsxs)("div",{className:"relative mb-6",children:[(0,a.jsx)("div",{className:"w-16 h-16 border-4 border-yellow-400 border-t-transparent rounded-full animate-spin mx-auto"}),(0,a.jsx)("div",{className:"absolute inset-0 w-16 h-16 border-4 border-orange-400 border-b-transparent rounded-full animate-spin mx-auto",style:{animationDirection:"reverse",animationDuration:"1.5s"}})]}),(0,a.jsx)("h2",{className:"text-2xl font-bold text-white mb-2 animate-pulse",children:p}),(0,a.jsx)("div",{className:"w-64 h-2 bg-gray-700 rounded-full mx-auto overflow-hidden",children:(0,a.jsx)("div",{className:"h-full bg-gradient-to-r from-yellow-400 to-orange-400 rounded-full animate-pulse"})}),(0,a.jsx)("p",{className:"text-gray-300 mt-4 text-sm",children:"Please wait while we prepare your mining experience..."})]})]}):(0,a.jsxs)("div",{className:"jsx-6bd0f39f565b36fe min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 flex items-center justify-center relative overflow-hidden",children:[(0,a.jsxs)("div",{className:"jsx-6bd0f39f565b36fe absolute inset-0",children:[(0,a.jsx)("div",{className:"jsx-6bd0f39f565b36fe absolute top-20 left-20 w-32 h-32 bg-yellow-400 rounded-full opacity-20 animate-float"}),(0,a.jsx)("div",{className:"jsx-6bd0f39f565b36fe absolute top-40 right-32 w-24 h-24 bg-orange-400 rounded-full opacity-20 animate-float delay-300"}),(0,a.jsx)("div",{className:"jsx-6bd0f39f565b36fe absolute bottom-32 left-32 w-40 h-40 bg-yellow-300 rounded-full opacity-20 animate-float delay-700"}),(0,a.jsx)("div",{className:"jsx-6bd0f39f565b36fe absolute bottom-20 right-20 w-28 h-28 bg-orange-300 rounded-full opacity-20 animate-float delay-500"})]}),(0,a.jsxs)("div",{className:"jsx-6bd0f39f565b36fe text-center z-10 max-w-md mx-auto px-6",children:[(0,a.jsx)("div",{className:"jsx-6bd0f39f565b36fe mb-8 animate-bounce",children:(0,a.jsx)(x.default,{src:"/hehe-logo.svg",alt:"Hehe Miner",width:150,height:150,className:"mx-auto drop-shadow-2xl"})}),(0,a.jsx)("h1",{className:"jsx-6bd0f39f565b36fe text-5xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-yellow-400 to-orange-400 mb-4 animate-pulse",children:"HEHE MINER"}),(0,a.jsx)("p",{className:"jsx-6bd0f39f565b36fe text-xl text-gray-300 mb-8 leading-relaxed",children:"Start your crypto mining journey with the most fun and rewarding mining game!"}),(0,a.jsxs)("div",{className:"jsx-6bd0f39f565b36fe mb-8 space-y-3",children:[(0,a.jsxs)("div",{className:"jsx-6bd0f39f565b36fe flex items-center justify-center text-gray-300",children:[(0,a.jsx)("span",{className:"jsx-6bd0f39f565b36fe text-yellow-400 mr-2",children:"⚡"}),(0,a.jsx)("span",{className:"jsx-6bd0f39f565b36fe",children:"Mine HEHE tokens every 4 hours"})]}),(0,a.jsxs)("div",{className:"jsx-6bd0f39f565b36fe flex items-center justify-center text-gray-300",children:[(0,a.jsx)("span",{className:"jsx-6bd0f39f565b36fe text-yellow-400 mr-2",children:"\uD83D\uDE80"}),(0,a.jsx)("span",{className:"jsx-6bd0f39f565b36fe",children:"Upgrade your mining power"})]}),(0,a.jsxs)("div",{className:"jsx-6bd0f39f565b36fe flex items-center justify-center text-gray-300",children:[(0,a.jsx)("span",{className:"jsx-6bd0f39f565b36fe text-yellow-400 mr-2",children:"\uD83D\uDC65"}),(0,a.jsx)("span",{className:"jsx-6bd0f39f565b36fe",children:"Refer friends and earn rewards"})]})]}),(0,a.jsx)("button",{onClick:f,disabled:n,className:"jsx-6bd0f39f565b36fe w-full bg-gradient-to-r from-yellow-400 to-orange-400 text-black font-bold py-4 px-8 rounded-xl text-lg shadow-2xl transform transition-all duration-300 hover:scale-105 hover:shadow-yellow-400/50 disabled:opacity-50 disabled:cursor-not-allowed animate-pulse",children:"\uD83D\uDE80 Launch App"}),u&&(0,a.jsx)("div",{className:"jsx-6bd0f39f565b36fe mt-4 p-3 bg-red-500/20 border border-red-500 rounded-lg text-red-300 text-sm",children:u}),(0,a.jsx)("p",{className:"jsx-6bd0f39f565b36fe text-gray-400 text-xs mt-6",children:"Powered by Telegram • Secure • Decentralized"})]}),(0,a.jsx)(d(),{id:"6bd0f39f565b36fe",children:"@-webkit-keyframes float{0%,100%{-webkit-transform:translatey(0px)rotate(0deg);transform:translatey(0px)rotate(0deg)}50%{-webkit-transform:translatey(-20px)rotate(180deg);transform:translatey(-20px)rotate(180deg)}}@-moz-keyframes float{0%,100%{-moz-transform:translatey(0px)rotate(0deg);transform:translatey(0px)rotate(0deg)}50%{-moz-transform:translatey(-20px)rotate(180deg);transform:translatey(-20px)rotate(180deg)}}@-o-keyframes float{0%,100%{-o-transform:translatey(0px)rotate(0deg);transform:translatey(0px)rotate(0deg)}50%{-o-transform:translatey(-20px)rotate(180deg);transform:translatey(-20px)rotate(180deg)}}@keyframes float{0%,100%{-webkit-transform:translatey(0px)rotate(0deg);-moz-transform:translatey(0px)rotate(0deg);-o-transform:translatey(0px)rotate(0deg);transform:translatey(0px)rotate(0deg)}50%{-webkit-transform:translatey(-20px)rotate(180deg);-moz-transform:translatey(-20px)rotate(180deg);-o-transform:translatey(-20px)rotate(180deg);transform:translatey(-20px)rotate(180deg)}}.animate-float.jsx-6bd0f39f565b36fe{-webkit-animation:float 6s ease-in-out infinite;-moz-animation:float 6s ease-in-out infinite;-o-animation:float 6s ease-in-out infinite;animation:float 6s ease-in-out infinite}"})]})}function h(){var e,s;let{user:t,updateUser:n}=(0,r.A)(),[o,d]=(0,l.useState)(!1),[c,m]=(0,l.useState)(0),[u,h]=(0,l.useState)(!1),[p,g]=(0,l.useState)(""),[b,f]=(0,l.useState)("");(0,l.useEffect)(()=>{if(null==t?void 0:t.currentMiningSession){let e=setInterval(()=>{j()},1e3);return()=>clearInterval(e)}},[null==t?void 0:t.currentMiningSession]);let j=()=>{if(!(null==t?void 0:t.currentMiningSession))return;let e=new Date(t.currentMiningSession.startTime).getTime()+144e5-Date.now();e<=0?(m(0),h(!0)):(m(e),h(!1))},N=async()=>{if(!(null==t?void 0:t.hasBasicPlan))return void g("You need to purchase the basic plan first!");d(!0),g(""),f("");try{let e=await i.startMining();if(e.success){f("Mining started successfully!");let e=await i.getUserProfile();e.success&&n(e.user)}else g(e.error||"Failed to start mining")}catch(e){g("Network error occurred")}finally{d(!1)}},v=async()=>{if(null==t?void 0:t.currentMiningSession){d(!0),g(""),f("");try{let e=await i.claimMining(t.currentMiningSession.id);if(e.success){f("Claimed ".concat(e.tokensEarned," HEHE tokens!"));let s=await i.getUserProfile();s.success&&n(s.user)}else g(e.error||"Failed to claim tokens")}catch(e){g("Network error occurred")}finally{d(!1)}}},y=()=>{if(!(null==t?void 0:t.currentMiningSession)||c<=0)return 100;let e=new Date(t.currentMiningSession.startTime).getTime();return Math.min((Date.now()-e)/144e5*100,100)};return(0,a.jsxs)("div",{className:"p-6 max-w-md mx-auto relative",children:[p&&(0,a.jsx)("div",{className:"bg-red-500/20 border border-red-400/30 rounded-xl p-4 mb-6 glass animate-bounce-in",children:(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("span",{className:"text-red-400 text-xl animate-bounce",children:"⚠️"}),(0,a.jsx)("p",{className:"text-red-300 font-medium",children:p})]})}),b&&(0,a.jsx)("div",{className:"bg-green-500/20 border border-green-400/30 rounded-xl p-4 mb-6 glass animate-bounce-in",children:(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("span",{className:"text-green-400 text-xl animate-bounce",children:"\uD83C\uDF89"}),(0,a.jsx)("p",{className:"text-green-300 font-medium",children:b})]})}),(0,a.jsxs)("div",{className:"glass-dark rounded-3xl p-8 mb-8 border border-white/20 hover-lift animate-slide-up relative overflow-hidden",children:[(0,a.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-yellow-400/10 via-orange-500/10 to-red-500/10 animate-pulse"}),(0,a.jsxs)("div",{className:"text-center relative z-10",children:[(0,a.jsxs)("div",{className:"relative mb-6",children:[(0,a.jsxs)("div",{className:"w-32 h-32 bg-gradient-to-r from-yellow-400 via-orange-500 to-red-500 rounded-full mx-auto flex items-center justify-center animate-pulse-glow relative",children:[(0,a.jsx)(x.default,{src:"/hehe-logo.svg",alt:"Hehe Miner",width:80,height:80,className:"animate-float"}),(null==t?void 0:t.currentMiningSession)&&c>0&&(0,a.jsx)("div",{className:"absolute inset-0 rounded-full border-4 border-transparent border-t-white animate-spin"})]}),u&&(0,a.jsx)("div",{className:"absolute -top-2 -right-2 w-8 h-8 bg-gradient-to-r from-green-400 to-blue-500 rounded-full animate-ping"})]}),(0,a.jsx)("h2",{className:"text-3xl font-bold bg-gradient-to-r from-yellow-400 via-orange-500 to-red-500 bg-clip-text text-transparent mb-4",children:"Mining Status"}),(null==t?void 0:t.hasBasicPlan)?(null==t?void 0:t.currentMiningSession)&&c>0?(0,a.jsxs)("div",{className:"animate-slide-up",children:[(0,a.jsx)("p",{className:"text-gray-300 mb-6 text-lg font-medium",children:"⚡ Mining in progress... ⚡"}),(0,a.jsxs)("div",{className:"mb-6",children:[(0,a.jsx)("div",{className:"bg-gray-700/50 rounded-full h-4 mb-3 overflow-hidden",children:(0,a.jsx)("div",{className:"bg-gradient-to-r from-yellow-400 via-orange-500 to-red-500 h-4 rounded-full transition-all duration-1000 relative",style:{width:"".concat(y(),"%")},children:(0,a.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent shimmer"})})}),(0,a.jsxs)("p",{className:"text-white font-semibold",children:["Progress: ",y().toFixed(1),"% ⚡"]})]}),(0,a.jsxs)("div",{className:"bg-gray-800/80 backdrop-blur-sm rounded-2xl p-6 mb-4 border border-yellow-400/30",children:[(0,a.jsx)("div",{className:"text-4xl font-mono text-yellow-400 mb-3 animate-pulse font-bold",children:(e=>{let s=Math.floor(e/36e5),t=Math.floor(e%36e5/6e4),a=Math.floor(e%6e4/1e3);return"".concat(s.toString().padStart(2,"0"),":").concat(t.toString().padStart(2,"0"),":").concat(a.toString().padStart(2,"0"))})(c)}),(0,a.jsx)("p",{className:"text-white text-lg font-medium",children:"Time remaining ⏰"})]}),(0,a.jsxs)("div",{className:"flex items-center justify-center space-x-2 text-white",children:[(0,a.jsx)("span",{className:"text-2xl animate-coin-flip",children:"\uD83D\uDCB0"}),(0,a.jsxs)("span",{className:"text-lg font-bold",children:["Earning: ",t.currentMiningSession.tokensEarned," HEHE"]}),(0,a.jsx)("span",{className:"text-2xl animate-coin-flip",style:{animationDelay:"0.5s"},children:"\uD83D\uDCB0"})]})]}):u&&(null==t?void 0:t.currentMiningSession)?(0,a.jsxs)("div",{className:"animate-bounce-in",children:[(0,a.jsxs)("div",{className:"mb-6",children:[(0,a.jsx)("p",{className:"text-2xl font-bold bg-gradient-to-r from-green-400 to-blue-500 bg-clip-text text-transparent mb-4",children:"\uD83C\uDF89 Mining Complete! \uD83C\uDF89"}),(0,a.jsxs)("div",{className:"bg-gray-800/90 backdrop-blur-sm rounded-2xl p-6 mb-6 border border-green-400/30",children:[(0,a.jsxs)("div",{className:"flex items-center justify-center space-x-3 mb-3",children:[(0,a.jsx)("span",{className:"text-3xl animate-bounce",children:"\uD83D\uDC8E"}),(0,a.jsxs)("span",{className:"text-2xl font-bold text-white",children:[t.currentMiningSession.tokensEarned," HEHE"]}),(0,a.jsx)("span",{className:"text-3xl animate-bounce",style:{animationDelay:"0.3s"},children:"\uD83D\uDC8E"})]}),(0,a.jsx)("p",{className:"text-white",children:"Ready to claim your rewards!"})]})]}),(0,a.jsxs)("button",{onClick:v,disabled:o,className:"group w-full bg-gradient-to-r from-green-500 via-green-600 to-blue-600 hover:from-green-600 hover:via-green-700 hover:to-blue-700 disabled:from-gray-600 disabled:via-gray-700 disabled:to-gray-800 text-white font-bold py-4 px-6 rounded-xl transition-all duration-300 transform hover:scale-105 disabled:transform-none shadow-lg hover:shadow-green-500/25 relative overflow-hidden",children:[(0,a.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 shimmer"}),(0,a.jsx)("span",{className:"relative z-10 text-lg",children:o?(0,a.jsxs)("div",{className:"flex items-center justify-center space-x-2",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-5 w-5 border-2 border-white border-t-transparent"}),(0,a.jsx)("span",{children:"Claiming..."})]}):(0,a.jsxs)("div",{className:"flex items-center justify-center space-x-2",children:[(0,a.jsx)("span",{className:"group-hover:animate-bounce",children:"\uD83D\uDCB0"}),(0,a.jsx)("span",{children:"Claim Tokens"}),(0,a.jsx)("span",{className:"group-hover:animate-bounce",children:"\uD83D\uDCB0"})]})})]})]}):(0,a.jsxs)("div",{className:"animate-slide-up",children:[(0,a.jsx)("p",{className:"text-white mb-6 text-lg font-medium",children:"\uD83D\uDE80 Ready to mine! \uD83D\uDE80"}),(0,a.jsxs)("div",{className:"bg-gray-800/90 backdrop-blur-sm rounded-2xl p-6 mb-6 border border-blue-400/30",children:[(0,a.jsxs)("div",{className:"flex items-center justify-center space-x-3 mb-3",children:[(0,a.jsx)("span",{className:"text-2xl animate-pulse",children:"⚡"}),(0,a.jsxs)("span",{className:"text-lg font-bold text-white",children:["Mining Power: ",null==t?void 0:t.miningPower," HEHE/4h"]}),(0,a.jsx)("span",{className:"text-2xl animate-pulse",style:{animationDelay:"0.5s"},children:"⚡"})]}),(0,a.jsx)("p",{className:"text-white",children:"Start your mining session now!"})]}),(0,a.jsxs)("button",{onClick:N,disabled:o,className:"group w-full bg-gradient-to-r from-yellow-500 via-orange-500 to-red-500 hover:from-yellow-600 hover:via-orange-600 hover:to-red-600 disabled:from-gray-600 disabled:via-gray-700 disabled:to-gray-800 text-white font-bold py-4 px-6 rounded-xl transition-all duration-300 transform hover:scale-105 disabled:transform-none shadow-lg hover:shadow-yellow-500/25 relative overflow-hidden",children:[(0,a.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 shimmer"}),(0,a.jsx)("span",{className:"relative z-10 text-lg",children:o?(0,a.jsxs)("div",{className:"flex items-center justify-center space-x-2",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-5 w-5 border-2 border-white border-t-transparent"}),(0,a.jsx)("span",{children:"Starting..."})]}):(0,a.jsxs)("div",{className:"flex items-center justify-center space-x-2",children:[(0,a.jsx)("span",{className:"group-hover:animate-bounce",children:"⛏️"}),(0,a.jsx)("span",{children:"Start Mining"}),(0,a.jsx)("span",{className:"group-hover:animate-bounce",children:"⛏️"})]})})]})]}):(0,a.jsxs)("div",{className:"text-center animate-slide-up",children:[(0,a.jsx)("p",{className:"text-gray-300 mb-6 text-lg",children:"Purchase the basic plan to start mining! \uD83D\uDE80"}),(0,a.jsxs)("div",{className:"glass rounded-2xl p-6 border border-yellow-400/30",children:[(0,a.jsxs)("div",{className:"flex items-center justify-center space-x-3 mb-4",children:[(0,a.jsx)("span",{className:"text-3xl animate-bounce",children:"\uD83D\uDCA1"}),(0,a.jsx)("span",{className:"text-2xl animate-bounce",style:{animationDelay:"0.2s"},children:"⛏️"}),(0,a.jsx)("span",{className:"text-3xl animate-bounce",style:{animationDelay:"0.4s"},children:"\uD83D\uDC8E"})]}),(0,a.jsx)("p",{className:"text-yellow-300 font-bold text-lg mb-2",children:"Basic Plan: $1"}),(0,a.jsx)("p",{className:"text-gray-300",children:"Unlock mining 4 HEHE tokens every 4 hours"})]})]})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4 animate-slide-up",style:{animationDelay:"0.3s"},children:[(0,a.jsxs)("div",{className:"bg-gray-800/90 backdrop-blur-sm rounded-2xl p-6 border border-yellow-400/30 hover-lift group",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2 mb-2",children:[(0,a.jsx)("span",{className:"text-yellow-400 text-xl group-hover:animate-coin-flip",children:"\uD83D\uDCB0"}),(0,a.jsx)("p",{className:"text-white font-medium",children:"Total Balance"})]}),(0,a.jsxs)("p",{className:"text-2xl font-bold text-yellow-400",children:[(null==t||null==(e=t.totalBalance)?void 0:e.toFixed(2))||"0.00"," HEHE"]})]}),(0,a.jsxs)("div",{className:"bg-gray-800/90 backdrop-blur-sm rounded-2xl p-6 border border-blue-400/30 hover-lift group",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2 mb-2",children:[(0,a.jsx)("span",{className:"text-blue-400 text-xl group-hover:animate-pulse",children:"⚡"}),(0,a.jsx)("p",{className:"text-white font-medium",children:"Mining Power"})]}),(0,a.jsxs)("p",{className:"text-2xl font-bold text-blue-400",children:[(null==t||null==(s=t.miningPower)?void 0:s.toFixed(2))||"4.00","/4h"]})]})]})]})}function p(){let{updateUser:e}=(0,r.A)(),[s,t]=(0,l.useState)([]),[n,o]=(0,l.useState)(!0),[d,c]=(0,l.useState)(null),[m,x]=(0,l.useState)(""),[u,h]=(0,l.useState)("");(0,l.useEffect)(()=>{p()},[]);let p=async()=>{try{let e=await i.getTasks();e.success?t(e.tasks):x(e.error||"Failed to fetch tasks")}catch(e){x("Network error occurred")}finally{o(!1)}},g=async s=>{s.link&&window.open(s.link,"_blank"),c(s.id),x(""),h("");try{let a=await i.completeTask(s.id);a.success?(h("Task completed! Earned ".concat(a.reward," HEHE tokens")),t(e=>e.map(e=>e.id===s.id?{...e,isCompleted:!0,isRewardClaimed:!0}:e)),e({totalBalance:a.newBalance})):x(a.error||"Failed to complete task")}catch(e){x("Network error occurred")}finally{c(null)}};return n?(0,a.jsx)("div",{className:"p-6 max-w-md mx-auto",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-yellow-400 mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-gray-400",children:"Loading tasks..."})]})}):(0,a.jsxs)("div",{className:"p-6 max-w-md mx-auto",children:[(0,a.jsxs)("div",{className:"text-center mb-8 animate-slide-up",children:[(0,a.jsxs)("div",{className:"flex items-center justify-center space-x-3 mb-4",children:[(0,a.jsx)("span",{className:"text-3xl animate-bounce",children:"\uD83D\uDCCB"}),(0,a.jsx)("h2",{className:"text-3xl font-bold bg-gradient-to-r from-blue-400 via-purple-500 to-pink-500 bg-clip-text text-transparent",children:"Tasks"}),(0,a.jsx)("span",{className:"text-3xl animate-bounce",style:{animationDelay:"0.3s"},children:"✨"})]}),(0,a.jsx)("p",{className:"text-gray-300 text-lg",children:"Complete tasks to earn bonus HEHE tokens! \uD83C\uDFAF"})]}),m&&(0,a.jsx)("div",{className:"bg-red-500/20 border border-red-400/30 rounded-xl p-4 mb-6 glass animate-bounce-in",children:(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("span",{className:"text-red-400 text-xl animate-bounce",children:"⚠️"}),(0,a.jsx)("p",{className:"text-red-300 font-medium",children:m})]})}),u&&(0,a.jsx)("div",{className:"bg-green-500/20 border border-green-400/30 rounded-xl p-4 mb-6 glass animate-bounce-in",children:(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("span",{className:"text-green-400 text-xl animate-bounce",children:"\uD83C\uDF89"}),(0,a.jsx)("p",{className:"text-green-300 font-medium",children:u})]})}),(0,a.jsx)("div",{className:"space-y-5 animate-slide-up",style:{animationDelay:"0.2s"},children:0===s.length?(0,a.jsxs)("div",{className:"text-center py-12 glass-dark rounded-2xl border border-white/20",children:[(0,a.jsx)("span",{className:"text-6xl mb-4 block animate-float",children:"\uD83D\uDCCB"}),(0,a.jsx)("p",{className:"text-gray-300 text-lg",children:"No tasks available at the moment"}),(0,a.jsx)("p",{className:"text-gray-400 text-sm mt-2",children:"Check back soon for new opportunities! ✨"})]}):s.map((e,s)=>(0,a.jsxs)("div",{className:"glass-dark rounded-2xl p-6 border hover-lift transition-all duration-300 animate-slide-up ".concat(e.isCompleted?"border-green-400/40 bg-green-500/10":"border-white/20 hover:border-blue-400/40"),style:{animationDelay:"".concat(.1*s,"s")},children:[(0,a.jsxs)("div",{className:"flex items-start justify-between mb-4",children:[(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2 mb-2",children:[(0,a.jsx)("h3",{className:"text-white font-bold text-lg",children:e.title}),e.isCompleted&&(0,a.jsx)("span",{className:"text-green-400 text-xl animate-bounce",children:"✅"})]}),(0,a.jsx)("p",{className:"text-gray-300 mb-4 leading-relaxed",children:e.description}),(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2 glass rounded-lg px-3 py-2",children:[(0,a.jsx)("span",{className:"text-yellow-400 text-lg animate-coin-flip",children:"\uD83D\uDCB0"}),(0,a.jsxs)("span",{className:"text-yellow-300 font-bold",children:["+",e.reward," HEHE"]})]}),e.isCompleted&&(0,a.jsxs)("div",{className:"flex items-center space-x-2 glass rounded-lg px-3 py-2 bg-green-500/20",children:[(0,a.jsx)("span",{className:"text-green-400 animate-pulse",children:"✓"}),(0,a.jsx)("span",{className:"text-green-300 font-medium",children:"Completed"})]})]})]}),(0,a.jsx)("div",{className:"ml-4",children:e.isCompleted?(0,a.jsx)("div",{className:"w-14 h-14 bg-gradient-to-r from-green-400 to-blue-500 rounded-full flex items-center justify-center animate-pulse-glow",children:(0,a.jsx)("span",{className:"text-white text-xl animate-bounce",children:"✓"})}):(0,a.jsxs)("button",{onClick:()=>g(e),disabled:d===e.id,className:"group bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500 hover:from-blue-600 hover:via-purple-600 hover:to-pink-600 disabled:from-gray-600 disabled:via-gray-700 disabled:to-gray-800 text-white px-6 py-3 rounded-xl font-bold transition-all duration-300 transform hover:scale-105 disabled:transform-none shadow-lg hover:shadow-blue-500/25 relative overflow-hidden",children:[(0,a.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 shimmer"}),(0,a.jsx)("span",{className:"relative z-10",children:d===e.id?(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent"}),(0,a.jsx)("span",{children:"Doing..."})]}):(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("span",{className:"group-hover:animate-bounce",children:"\uD83D\uDE80"}),(0,a.jsx)("span",{children:"Do Task"})]})})]})})]}),e.link&&!e.isCompleted&&(0,a.jsx)("div",{className:"glass rounded-lg p-3 mt-4 border border-blue-400/30",children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("span",{className:"text-blue-400 animate-pulse",children:"\uD83D\uDCA1"}),(0,a.jsx)("p",{className:"text-blue-300 text-sm font-medium",children:'Clicking "Do Task" will open the link and mark the task as complete'})]})})]},e.id))}),(0,a.jsxs)("div",{className:"mt-8 glass-dark rounded-2xl p-6 border border-white/20 animate-slide-up",style:{animationDelay:"0.4s"},children:[(0,a.jsxs)("div",{className:"flex items-center justify-center space-x-2 mb-6",children:[(0,a.jsx)("span",{className:"text-2xl animate-bounce",children:"\uD83D\uDCCA"}),(0,a.jsx)("h3",{className:"text-xl font-bold bg-gradient-to-r from-blue-400 to-purple-500 bg-clip-text text-transparent",children:"Task Statistics"}),(0,a.jsx)("span",{className:"text-2xl animate-bounce",style:{animationDelay:"0.3s"},children:"\uD83D\uDCC8"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{className:"text-center glass rounded-xl p-4 hover-lift",children:[(0,a.jsxs)("div",{className:"flex items-center justify-center space-x-2 mb-2",children:[(0,a.jsx)("span",{className:"text-green-400 text-xl animate-pulse",children:"✅"}),(0,a.jsx)("p",{className:"text-gray-300 font-medium",children:"Completed"})]}),(0,a.jsx)("p",{className:"text-3xl font-bold bg-gradient-to-r from-green-400 to-blue-500 bg-clip-text text-transparent",children:s.filter(e=>e.isCompleted).length})]}),(0,a.jsxs)("div",{className:"text-center glass rounded-xl p-4 hover-lift",children:[(0,a.jsxs)("div",{className:"flex items-center justify-center space-x-2 mb-2",children:[(0,a.jsx)("span",{className:"text-blue-400 text-xl animate-pulse",children:"\uD83C\uDFAF"}),(0,a.jsx)("p",{className:"text-gray-300 font-medium",children:"Available"})]}),(0,a.jsx)("p",{className:"text-3xl font-bold bg-gradient-to-r from-blue-400 to-purple-500 bg-clip-text text-transparent",children:s.filter(e=>!e.isCompleted).length})]})]}),(0,a.jsxs)("div",{className:"mt-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,a.jsx)("span",{className:"text-gray-300 font-medium",children:"Progress"}),(0,a.jsxs)("span",{className:"text-gray-300 font-medium",children:[s.length>0?Math.round(s.filter(e=>e.isCompleted).length/s.length*100):0,"%"]})]}),(0,a.jsx)("div",{className:"bg-gray-700/50 rounded-full h-3 overflow-hidden",children:(0,a.jsx)("div",{className:"bg-gradient-to-r from-blue-400 via-purple-500 to-pink-500 h-3 rounded-full transition-all duration-1000 relative",style:{width:"".concat(s.length>0?s.filter(e=>e.isCompleted).length/s.length*100:0,"%")},children:(0,a.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent shimmer"})})})]})]})]})}function g(){let{user:e}=(0,r.A)(),[s,t]=(0,l.useState)([]),[n,o]=(0,l.useState)({totalReferrals:0,totalRewards:0}),[d,c]=(0,l.useState)(""),[m,x]=(0,l.useState)(!0),[u,h]=(0,l.useState)(""),[p,g]=(0,l.useState)(!1);(0,l.useEffect)(()=>{b()},[]);let b=async()=>{try{let e=await i.getReferrals();e.success?(t(e.referrals),o(e.stats),c(e.referralLink)):h(e.error||"Failed to fetch referrals")}catch(e){h("Network error occurred")}finally{x(!1)}},f=async()=>{try{await navigator.clipboard.writeText(d),g(!0),setTimeout(()=>g(!1),2e3)}catch(s){let e=document.createElement("textarea");e.value=d,document.body.appendChild(e),e.select(),document.execCommand("copy"),document.body.removeChild(e),g(!0),setTimeout(()=>g(!1),2e3)}},j=e=>new Date(e).toLocaleDateString("en-US",{month:"short",day:"numeric",year:"numeric"});return m?(0,a.jsx)("div",{className:"p-6 max-w-md mx-auto",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-yellow-400 mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-gray-400",children:"Loading referrals..."})]})}):(0,a.jsxs)("div",{className:"p-6 max-w-md mx-auto",children:[(0,a.jsxs)("div",{className:"text-center mb-6",children:[(0,a.jsx)("h2",{className:"text-2xl font-bold text-white mb-2",children:"Referrals"}),(0,a.jsx)("p",{className:"text-gray-400",children:"Invite friends and earn 0.5 HEHE per referral"})]}),u&&(0,a.jsx)("div",{className:"bg-red-500/10 border border-red-500/20 rounded-lg p-3 mb-4",children:(0,a.jsx)("p",{className:"text-red-400 text-sm",children:u})}),(0,a.jsxs)("div",{className:"bg-gray-800 rounded-lg p-4 border border-gray-700 mb-6",children:[(0,a.jsx)("h3",{className:"text-white font-semibold mb-3",children:"Your Referral Link"}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("input",{type:"text",value:d,readOnly:!0,className:"flex-1 bg-gray-700 text-white px-3 py-2 rounded border border-gray-600 text-sm"}),(0,a.jsx)("button",{onClick:f,className:"px-4 py-2 rounded text-sm font-medium transition-colors ".concat(p?"bg-green-600 text-white":"bg-blue-600 hover:bg-blue-700 text-white"),children:p?"Copied!":"Copy"})]}),(0,a.jsx)("p",{className:"text-gray-400 text-xs mt-2",children:"Share this link with friends to earn 0.5 HEHE tokens for each signup"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4 mb-6",children:[(0,a.jsxs)("div",{className:"bg-gray-800 rounded-lg p-4 border border-gray-700 text-center",children:[(0,a.jsx)("p",{className:"text-gray-400 text-sm",children:"Total Referrals"}),(0,a.jsx)("p",{className:"text-white text-2xl font-bold",children:n.totalReferrals})]}),(0,a.jsxs)("div",{className:"bg-gray-800 rounded-lg p-4 border border-gray-700 text-center",children:[(0,a.jsx)("p",{className:"text-gray-400 text-sm",children:"Total Earned"}),(0,a.jsx)("p",{className:"text-yellow-400 text-2xl font-bold",children:n.totalRewards.toFixed(1)})]})]}),(0,a.jsxs)("div",{className:"bg-gray-800 rounded-lg border border-gray-700",children:[(0,a.jsx)("div",{className:"p-4 border-b border-gray-700",children:(0,a.jsx)("h3",{className:"text-white font-semibold",children:"Recent Referrals"})}),(0,a.jsx)("div",{className:"max-h-64 overflow-y-auto",children:0===s.length?(0,a.jsxs)("div",{className:"p-4 text-center",children:[(0,a.jsx)("p",{className:"text-gray-400",children:"No referrals yet"}),(0,a.jsx)("p",{className:"text-gray-500 text-sm mt-1",children:"Start sharing your referral link to earn rewards!"})]}):(0,a.jsx)("div",{className:"divide-y divide-gray-700",children:s.map(e=>(0,a.jsxs)("div",{className:"p-4 flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsxs)("p",{className:"text-white font-medium",children:[e.referred.firstName," ",e.referred.lastName,e.referred.username&&(0,a.jsxs)("span",{className:"text-gray-400 ml-1",children:["(@",e.referred.username,")"]})]}),(0,a.jsxs)("p",{className:"text-gray-400 text-sm",children:["Joined ",j(e.referred.joinedAt)]})]}),(0,a.jsxs)("div",{className:"text-right",children:[(0,a.jsxs)("p",{className:"text-yellow-400 font-semibold",children:["+",e.reward," HEHE"]}),(0,a.jsx)("p",{className:"text-gray-500 text-xs",children:j(e.createdAt)})]})]},e.id))})})]}),(0,a.jsxs)("div",{className:"mt-6 bg-blue-500/10 border border-blue-500/20 rounded-lg p-4",children:[(0,a.jsx)("h4",{className:"text-blue-400 font-semibold mb-2",children:"How Referrals Work"}),(0,a.jsxs)("ul",{className:"text-blue-300 text-sm space-y-1",children:[(0,a.jsx)("li",{children:"• Share your unique referral link"}),(0,a.jsx)("li",{children:"• When someone signs up using your link, you both benefit"}),(0,a.jsx)("li",{children:"• You earn 0.5 HEHE tokens for each successful referral"}),(0,a.jsx)("li",{children:"• No limit on the number of referrals you can make"})]})]})]})}function b(){var e;let{user:s}=(0,r.A)();return(0,a.jsxs)("div",{className:"p-6 max-w-md mx-auto",children:[(0,a.jsxs)("div",{className:"text-center mb-6",children:[(0,a.jsx)("h2",{className:"text-2xl font-bold text-white mb-2",children:"Airdrop"}),(0,a.jsx)("p",{className:"text-gray-400",children:"Your HEHE token balance and airdrop status"})]}),(0,a.jsxs)("div",{className:"bg-gradient-to-r from-purple-600 to-blue-600 rounded-2xl p-6 mb-6 text-center",children:[(0,a.jsxs)("div",{className:"mb-4",children:[(0,a.jsx)("div",{className:"w-16 h-16 bg-white/20 rounded-full mx-auto flex items-center justify-center mb-3",children:(0,a.jsx)("span",{className:"text-2xl",children:"\uD83E\uDE82"})}),(0,a.jsx)("h3",{className:"text-white text-lg font-semibold mb-1",children:"Total Balance"}),(0,a.jsx)("p",{className:"text-white/80 text-sm",children:"Your accumulated HEHE tokens"})]}),(0,a.jsx)("div",{className:"text-4xl font-bold text-white mb-2",children:(null==s||null==(e=s.totalBalance)?void 0:e.toFixed(2))||"0.00"}),(0,a.jsx)("p",{className:"text-white/80 text-lg",children:"HEHE Tokens"})]}),(0,a.jsx)("div",{className:"bg-gray-800 rounded-lg border border-gray-700 p-6 mb-6",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"w-12 h-12 bg-yellow-500/20 rounded-full mx-auto flex items-center justify-center mb-3",children:(0,a.jsx)("span",{className:"text-xl",children:"⏰"})}),(0,a.jsx)("h3",{className:"text-white text-lg font-semibold mb-2",children:"Airdrop Status"}),(0,a.jsx)("div",{className:"bg-yellow-500/10 border border-yellow-500/20 rounded-lg p-4 mb-4",children:(0,a.jsx)("p",{className:"text-yellow-400 font-semibold text-lg",children:"Coming Soon"})}),(0,a.jsx)("p",{className:"text-gray-400 text-sm",children:"The HEHE token airdrop is currently in development. Keep mining and completing tasks to maximize your token balance!"})]})}),(0,a.jsxs)("div",{className:"bg-gray-800 rounded-lg border border-gray-700 p-4 mb-6",children:[(0,a.jsx)("h3",{className:"text-white font-semibold mb-4",children:"Token Breakdown"}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("span",{className:"text-lg",children:"⛏️"}),(0,a.jsx)("span",{className:"text-gray-300",children:"Mining Rewards"})]}),(0,a.jsx)("span",{className:"text-white font-medium",children:((null==s?void 0:s.totalBalance)||0)>0?"Active":"Start Mining"})]}),(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("span",{className:"text-lg",children:"\uD83D\uDCCB"}),(0,a.jsx)("span",{className:"text-gray-300",children:"Task Rewards"})]}),(0,a.jsx)("span",{className:"text-white font-medium",children:"Available"})]}),(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("span",{className:"text-lg",children:"\uD83D\uDC65"}),(0,a.jsx)("span",{className:"text-gray-300",children:"Referral Rewards"})]}),(0,a.jsx)("span",{className:"text-white font-medium",children:"0.5 HEHE each"})]})]})]}),(0,a.jsxs)("div",{className:"bg-blue-500/10 border border-blue-500/20 rounded-lg p-4",children:[(0,a.jsx)("h4",{className:"text-blue-400 font-semibold mb-3",children:"Airdrop Information"}),(0,a.jsxs)("div",{className:"space-y-2 text-blue-300 text-sm",children:[(0,a.jsxs)("div",{className:"flex items-start space-x-2",children:[(0,a.jsx)("span",{className:"text-blue-400 mt-0.5",children:"•"}),(0,a.jsx)("span",{children:"All HEHE tokens earned through mining, tasks, and referrals will be eligible for the airdrop"})]}),(0,a.jsxs)("div",{className:"flex items-start space-x-2",children:[(0,a.jsx)("span",{className:"text-blue-400 mt-0.5",children:"•"}),(0,a.jsx)("span",{children:"The airdrop will convert your HEHE tokens to the official HEHE cryptocurrency"})]}),(0,a.jsxs)("div",{className:"flex items-start space-x-2",children:[(0,a.jsx)("span",{className:"text-blue-400 mt-0.5",children:"•"}),(0,a.jsx)("span",{children:"Keep accumulating tokens to maximize your airdrop allocation"})]}),(0,a.jsxs)("div",{className:"flex items-start space-x-2",children:[(0,a.jsx)("span",{className:"text-blue-400 mt-0.5",children:"•"}),(0,a.jsx)("span",{children:"Follow our social media channels for airdrop announcements"})]})]})]}),(0,a.jsxs)("div",{className:"mt-6 text-center",children:[(0,a.jsx)("p",{className:"text-gray-400 text-sm mb-3",children:"Want to increase your airdrop allocation?"}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-3",children:[(0,a.jsxs)("div",{className:"bg-gray-800 rounded-lg p-3 border border-gray-700",children:[(0,a.jsx)("p",{className:"text-white font-medium text-sm",children:"Keep Mining"}),(0,a.jsx)("p",{className:"text-gray-400 text-xs",children:"Earn 4+ HEHE every 4 hours"})]}),(0,a.jsxs)("div",{className:"bg-gray-800 rounded-lg p-3 border border-gray-700",children:[(0,a.jsx)("p",{className:"text-white font-medium text-sm",children:"Complete Tasks"}),(0,a.jsx)("p",{className:"text-gray-400 text-xs",children:"Bonus tokens available"})]})]})]})]})}function f(){var e,s;let{user:t,updateUser:n}=(0,r.A)(),[o,d]=(0,l.useState)(!1),[c,m]=(0,l.useState)(null),[x,u]=(0,l.useState)(""),[h,p]=(0,l.useState)(""),g=async()=>{m("basic"),d(!0),u(""),p("");try{let e=await i.purchaseBasicPlan();e.success?(p("Basic plan purchased successfully! You can now start mining."),n({hasBasicPlan:!0})):u(e.error||"Failed to purchase basic plan")}catch(e){u("Network error occurred")}finally{d(!1),m(null)}},b=async()=>{m("speed"),d(!0),u(""),p("");try{let e=await i.purchaseSpeedUpgrade();e.success?(p("Speed upgrade purchased! Your mining power has increased."),n({miningPower:e.user.miningPower,speedUpgrades:e.user.speedUpgrades})):u(e.error||"Failed to purchase speed upgrade")}catch(e){u("Network error occurred")}finally{d(!1),m(null)}},f=(null==t?void 0:t.currentMiningSession)&&(null==t?void 0:t.canMine)===!1,j=f||o&&"speed"===c;return(0,a.jsxs)("div",{className:"p-6 max-w-md mx-auto",children:[(0,a.jsxs)("div",{className:"text-center mb-6",children:[(0,a.jsx)("h2",{className:"text-2xl font-bold text-white mb-2",children:"Upgrades"}),(0,a.jsx)("p",{className:"text-gray-400",children:"Enhance your mining capabilities"})]}),x&&(0,a.jsx)("div",{className:"bg-red-500/10 border border-red-500/20 rounded-lg p-3 mb-4",children:(0,a.jsx)("p",{className:"text-red-400 text-sm",children:x})}),h&&(0,a.jsx)("div",{className:"bg-green-500/10 border border-green-500/20 rounded-lg p-3 mb-4",children:(0,a.jsx)("p",{className:"text-green-400 text-sm",children:h})}),(0,a.jsxs)("div",{className:"bg-gray-800 rounded-lg border border-gray-700 p-4 mb-6",children:[(0,a.jsx)("h3",{className:"text-white font-semibold mb-3",children:"Current Status"}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsx)("span",{className:"text-gray-300",children:"Plan Status"}),(0,a.jsx)("span",{className:"font-medium ".concat((null==t?void 0:t.hasBasicPlan)?"text-green-400":"text-red-400"),children:(null==t?void 0:t.hasBasicPlan)?"Basic Plan Active":"No Plan"})]}),(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsx)("span",{className:"text-gray-300",children:"Mining Power"}),(0,a.jsxs)("span",{className:"text-white font-medium",children:[(null==t||null==(e=t.miningPower)?void 0:e.toFixed(2))||"4.00"," HEHE/4h"]})]}),(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsx)("span",{className:"text-gray-300",children:"Speed Upgrades"}),(0,a.jsx)("span",{className:"text-white font-medium",children:(null==t?void 0:t.speedUpgrades)||0})]})]})]}),!(null==t?void 0:t.hasBasicPlan)&&(0,a.jsx)("div",{className:"bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg p-6 mb-6",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"w-12 h-12 bg-white/20 rounded-full mx-auto flex items-center justify-center mb-3",children:(0,a.jsx)("span",{className:"text-xl",children:"⛏️"})}),(0,a.jsx)("h3",{className:"text-white text-lg font-bold mb-2",children:"Basic Mining Plan"}),(0,a.jsx)("p",{className:"text-white/80 text-sm mb-4",children:"Unlock the ability to mine HEHE tokens"}),(0,a.jsxs)("div",{className:"bg-white/10 rounded-lg p-3 mb-4",children:[(0,a.jsx)("p",{className:"text-white font-semibold text-lg",children:"$1.00"}),(0,a.jsx)("p",{className:"text-white/80 text-sm",children:"One-time purchase"})]}),(0,a.jsxs)("div",{className:"text-left mb-4",children:[(0,a.jsx)("h4",{className:"text-white font-semibold mb-2",children:"Includes:"}),(0,a.jsxs)("ul",{className:"text-white/80 text-sm space-y-1",children:[(0,a.jsx)("li",{children:"• Mine 4 HEHE tokens every 4 hours"}),(0,a.jsx)("li",{children:"• Access to all tasks and referrals"}),(0,a.jsx)("li",{children:"• Eligible for future airdrops"}),(0,a.jsx)("li",{children:"• Ability to purchase speed upgrades"})]})]}),(0,a.jsx)("button",{onClick:g,disabled:o&&"basic"===c,className:"w-full bg-white text-blue-600 font-semibold py-3 px-4 rounded-lg hover:bg-gray-100 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors",children:o&&"basic"===c?"Processing...":"Purchase Basic Plan"})]})}),(null==t?void 0:t.hasBasicPlan)&&(0,a.jsx)("div",{className:"bg-gray-800 rounded-lg border border-gray-700 p-6",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"w-12 h-12 bg-yellow-500/20 rounded-full mx-auto flex items-center justify-center mb-3",children:(0,a.jsx)("span",{className:"text-xl",children:"⚡"})}),(0,a.jsx)("h3",{className:"text-white text-lg font-bold mb-2",children:"Speed Upgrade"}),(0,a.jsx)("p",{className:"text-gray-400 text-sm mb-4",children:"Increase your mining power by 0.25 HEHE per 4 hours"}),(0,a.jsxs)("div",{className:"bg-yellow-500/10 border border-yellow-500/20 rounded-lg p-3 mb-4",children:[(0,a.jsx)("p",{className:"text-yellow-400 font-semibold text-lg",children:"$1.00"}),(0,a.jsx)("p",{className:"text-yellow-300 text-sm",children:"Per upgrade"})]}),(0,a.jsxs)("div",{className:"text-left mb-4",children:[(0,a.jsx)("h4",{className:"text-white font-semibold mb-2",children:"Upgrade Details:"}),(0,a.jsxs)("ul",{className:"text-gray-300 text-sm space-y-1",children:[(0,a.jsx)("li",{children:"• +0.25 HEHE tokens per 4-hour cycle"}),(0,a.jsx)("li",{children:"• Permanent increase to mining power"}),(0,a.jsx)("li",{children:"• No limit on number of upgrades"}),(0,a.jsx)("li",{children:"• Compounds with existing upgrades"})]})]}),(0,a.jsxs)("div",{className:"bg-gray-700 rounded-lg p-3 mb-4",children:[(0,a.jsxs)("p",{className:"text-gray-300 text-sm",children:["Current: ",null==t||null==(s=t.miningPower)?void 0:s.toFixed(2)," HEHE/4h"]}),(0,a.jsxs)("p",{className:"text-white text-sm",children:["After upgrade: ",(((null==t?void 0:t.miningPower)||4)+.25).toFixed(2)," HEHE/4h"]})]}),f&&(0,a.jsx)("div",{className:"bg-orange-500/10 border border-orange-500/20 rounded-lg p-3 mb-4",children:(0,a.jsx)("p",{className:"text-orange-400 text-sm",children:"⚠️ Cannot purchase upgrades during active mining session. Complete your current mining first."})}),(0,a.jsx)("button",{onClick:b,disabled:j,className:"w-full font-semibold py-3 px-4 rounded-lg transition-colors ".concat(j?"bg-gray-600 cursor-not-allowed text-gray-400":"bg-yellow-600 hover:bg-yellow-700 text-white"),children:o&&"speed"===c?"Processing...":f?"Mining in Progress - Upgrade Locked":"Purchase Speed Upgrade"})]})}),(0,a.jsxs)("div",{className:"mt-6 bg-blue-500/10 border border-blue-500/20 rounded-lg p-4",children:[(0,a.jsx)("h4",{className:"text-blue-400 font-semibold mb-2",children:"\uD83D\uDCA1 Upgrade Tips"}),(0,a.jsxs)("ul",{className:"text-blue-300 text-sm space-y-1",children:[(0,a.jsx)("li",{children:"• Speed upgrades stack - buy multiple for even faster mining"}),(0,a.jsx)("li",{children:"• All purchases are simulated for this demo"}),(0,a.jsx)("li",{children:"• In production, this would integrate with real payment processors"}),(0,a.jsx)("li",{children:"• Your upgrades are permanent and apply to all future mining sessions"})]})]})]})}function j(){var e,s;let{user:t,updateUser:n,logout:o}=(0,r.A)(),[d,c]=(0,l.useState)("mining"),[m,x]=(0,l.useState)(!0);(0,l.useEffect)(()=>{u()},[]);let u=async()=>{try{let e=await i.getUserProfile();e.success&&n(e.user)}catch(e){console.error("Failed to fetch user profile:",e)}finally{x(!1)}};return m?(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-yellow-400 mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-gray-400",children:"Loading your profile..."})]})}):(0,a.jsxs)("div",{className:"min-h-screen relative overflow-hidden",children:[(0,a.jsxs)("div",{className:"fixed inset-0 bg-gradient-to-br from-indigo-900 via-purple-900 to-pink-900",children:[(0,a.jsx)("div",{className:"absolute inset-0 opacity-30",style:{backgroundImage:"url(\"data:image/svg+xml,%3Csvg width='40' height='40' viewBox='0 0 40 40' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%23ffffff' fill-opacity='0.03'%3E%3Cpath d='M20 20c0-5.5-4.5-10-10-10s-10 4.5-10 10 4.5 10 10 10 10-4.5 10-10zm10 0c0-5.5-4.5-10-10-10s-10 4.5-10 10 4.5 10 10 10 10-4.5 10-10z'/%3E%3C/g%3E%3C/svg%3E\")"}}),(0,a.jsx)("div",{className:"absolute top-20 left-10 w-3 h-3 bg-yellow-400 rounded-full animate-float opacity-60"}),(0,a.jsx)("div",{className:"absolute top-40 right-20 w-2 h-2 bg-blue-400 rounded-full animate-float opacity-40",style:{animationDelay:"1s"}}),(0,a.jsx)("div",{className:"absolute bottom-40 left-20 w-4 h-4 bg-purple-400 rounded-full animate-float opacity-50",style:{animationDelay:"2s"}}),(0,a.jsx)("div",{className:"absolute bottom-60 right-10 w-2 h-2 bg-green-400 rounded-full animate-float opacity-70",style:{animationDelay:"0.5s"}})]}),(0,a.jsx)("header",{className:"relative z-10 glass-dark border-b border-white/10 px-4 py-4 animate-slide-up",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("div",{className:"w-12 h-12 bg-gradient-to-r from-yellow-400 via-orange-500 to-red-500 rounded-full flex items-center justify-center animate-pulse-glow",children:(0,a.jsx)("span",{className:"text-lg animate-float",children:"⛏️"})}),(0,a.jsx)("div",{className:"absolute -top-1 -right-1 w-4 h-4 bg-gradient-to-r from-green-400 to-blue-500 rounded-full animate-ping"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-xl font-bold bg-gradient-to-r from-yellow-400 via-orange-500 to-red-500 bg-clip-text text-transparent",children:"Hehe Miner"}),(0,a.jsxs)("p",{className:"text-sm text-gray-300 font-medium",children:["Welcome, ",null==t?void 0:t.firstName," ",null==t?void 0:t.lastName," ✨"]})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsxs)("div",{className:"text-right glass rounded-lg p-3 hover-lift",children:[(0,a.jsxs)("p",{className:"text-lg font-bold bg-gradient-to-r from-yellow-400 to-orange-500 bg-clip-text text-transparent animate-coin-flip",children:[(null==t||null==(e=t.totalBalance)?void 0:e.toFixed(2))||"0.00"," HEHE"]}),(0,a.jsxs)("p",{className:"text-xs text-gray-300",children:["⚡ Power: ",(null==t||null==(s=t.miningPower)?void 0:s.toFixed(2))||"4.00","/4h"]})]}),(0,a.jsx)("button",{onClick:()=>{o()},className:"w-10 h-10 glass rounded-full flex items-center justify-center text-gray-300 hover:text-red-400 transition-all duration-300 hover:scale-110 group",children:(0,a.jsx)("span",{className:"group-hover:animate-bounce",children:"\uD83D\uDEAA"})})]})]})}),(0,a.jsx)("main",{className:"relative z-10 pb-24 pt-4",children:(0,a.jsx)("div",{className:"animate-slide-up",children:(()=>{switch(d){case"mining":default:return(0,a.jsx)(h,{});case"tasks":return(0,a.jsx)(p,{});case"referrals":return(0,a.jsx)(g,{});case"airdrop":return(0,a.jsx)(b,{});case"subscription":return(0,a.jsx)(f,{})}})()})}),(0,a.jsx)("nav",{className:"fixed bottom-0 left-0 right-0 z-20 glass-dark border-t border-white/10 backdrop-blur-xl",children:(0,a.jsxs)("div",{className:"flex items-center justify-around py-3 px-2",children:[(0,a.jsxs)("button",{onClick:()=>c("mining"),className:"group flex flex-col items-center py-3 px-4 rounded-xl transition-all duration-300 transform hover:scale-110 ".concat("mining"===d?"bg-gradient-to-r from-yellow-400 to-orange-500 text-white shadow-lg shadow-yellow-500/25":"text-gray-400 hover:text-white hover:bg-white/10"),children:[(0,a.jsx)("span",{className:"text-2xl mb-1 ".concat("mining"===d?"animate-mining-pulse":"group-hover:animate-bounce"),children:"⛏️"}),(0,a.jsx)("span",{className:"text-xs font-semibold",children:"Mine"}),"mining"===d&&(0,a.jsx)("div",{className:"absolute -top-1 -right-1 w-2 h-2 bg-green-400 rounded-full animate-ping"})]}),(0,a.jsxs)("button",{onClick:()=>c("tasks"),className:"group flex flex-col items-center py-3 px-4 rounded-xl transition-all duration-300 transform hover:scale-110 ".concat("tasks"===d?"bg-gradient-to-r from-blue-400 to-purple-500 text-white shadow-lg shadow-blue-500/25":"text-gray-400 hover:text-white hover:bg-white/10"),children:[(0,a.jsx)("span",{className:"text-2xl mb-1 ".concat("tasks"===d?"animate-pulse":"group-hover:animate-bounce"),children:"\uD83D\uDCCB"}),(0,a.jsx)("span",{className:"text-xs font-semibold",children:"Tasks"}),"tasks"===d&&(0,a.jsx)("div",{className:"absolute -top-1 -right-1 w-2 h-2 bg-green-400 rounded-full animate-ping"})]}),(0,a.jsxs)("button",{onClick:()=>c("referrals"),className:"group flex flex-col items-center py-3 px-4 rounded-xl transition-all duration-300 transform hover:scale-110 ".concat("referrals"===d?"bg-gradient-to-r from-purple-400 to-pink-500 text-white shadow-lg shadow-purple-500/25":"text-gray-400 hover:text-white hover:bg-white/10"),children:[(0,a.jsx)("span",{className:"text-2xl mb-1 ".concat("referrals"===d?"animate-pulse":"group-hover:animate-bounce"),children:"\uD83D\uDC65"}),(0,a.jsx)("span",{className:"text-xs font-semibold",children:"Referrals"}),"referrals"===d&&(0,a.jsx)("div",{className:"absolute -top-1 -right-1 w-2 h-2 bg-green-400 rounded-full animate-ping"})]}),(0,a.jsxs)("button",{onClick:()=>c("airdrop"),className:"group flex flex-col items-center py-3 px-4 rounded-xl transition-all duration-300 transform hover:scale-110 ".concat("airdrop"===d?"bg-gradient-to-r from-green-400 to-blue-500 text-white shadow-lg shadow-green-500/25":"text-gray-400 hover:text-white hover:bg-white/10"),children:[(0,a.jsx)("span",{className:"text-2xl mb-1 ".concat("airdrop"===d?"animate-float":"group-hover:animate-bounce"),children:"\uD83E\uDE82"}),(0,a.jsx)("span",{className:"text-xs font-semibold",children:"Airdrop"}),"airdrop"===d&&(0,a.jsx)("div",{className:"absolute -top-1 -right-1 w-2 h-2 bg-green-400 rounded-full animate-ping"})]}),(0,a.jsxs)("button",{onClick:()=>c("subscription"),className:"group flex flex-col items-center py-3 px-4 rounded-xl transition-all duration-300 transform hover:scale-110 ".concat("subscription"===d?"bg-gradient-to-r from-orange-400 to-red-500 text-white shadow-lg shadow-orange-500/25":"text-gray-400 hover:text-white hover:bg-white/10"),children:[(0,a.jsx)("span",{className:"text-2xl mb-1 ".concat("subscription"===d?"animate-pulse":"group-hover:animate-bounce"),children:"⚡"}),(0,a.jsx)("span",{className:"text-xs font-semibold",children:"Upgrade"}),"subscription"===d&&(0,a.jsx)("div",{className:"absolute -top-1 -right-1 w-2 h-2 bg-green-400 rounded-full animate-ping"})]})]})})]})}function N(){let{user:e,token:s,isLoading:t}=(0,r.A)(),[n,o]=(0,l.useState)(!0);return((0,l.useEffect)(()=>{s&&i.setToken(s)},[s]),(0,l.useEffect)(()=>{e&&!t&&o(!1)},[e,t]),t)?(0,a.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 flex items-center justify-center",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-yellow-400 mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-gray-300",children:"Loading Hehe Miner..."})]})}):!e||n?(0,a.jsx)(u,{onLaunchApp:()=>{o(!1)}}):(0,a.jsx)(j,{})}}},e=>{var s=s=>e(e.s=s);e.O(0,[268,727,441,684,358],()=>s(1730)),_N_E=e.O()}]);