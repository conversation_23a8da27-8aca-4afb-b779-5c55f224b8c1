[{"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/app/api/auth/telegram/route.ts": "1", "/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/app/api/mining/claim/route.ts": "2", "/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/app/api/mining/start/route.ts": "3", "/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/app/api/referrals/route.ts": "4", "/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/app/api/subscription/basic-plan/route.ts": "5", "/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/app/api/subscription/speed-upgrade/route.ts": "6", "/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/app/api/tasks/complete/route.ts": "7", "/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/app/api/tasks/route.ts": "8", "/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/app/api/user/profile/route.ts": "9", "/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/app/layout.tsx": "10", "/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/app/page.tsx": "11", "/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/AirdropScreen.tsx": "12", "/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/AuthScreen.tsx": "13", "/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/LandingPage.tsx": "14", "/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MainApp.tsx": "15", "/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx": "16", "/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/ReferralsScreen.tsx": "17", "/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/SubscriptionScreen.tsx": "18", "/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/TasksScreen.tsx": "19", "/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/contexts/AuthContext.tsx": "20", "/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/lib/api.ts": "21", "/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/lib/auth.ts": "22", "/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/lib/prisma.ts": "23", "/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/lib/telegram.ts": "24"}, {"size": 3037, "mtime": 1749578034599, "results": "25", "hashOfConfig": "26"}, {"size": 2057, "mtime": 1749578072662, "results": "27", "hashOfConfig": "26"}, {"size": 2296, "mtime": 1749578061125, "results": "28", "hashOfConfig": "26"}, {"size": 3621, "mtime": 1749578132801, "results": "29", "hashOfConfig": "26"}, {"size": 2617, "mtime": 1749578934806, "results": "30", "hashOfConfig": "26"}, {"size": 2905, "mtime": 1749578870290, "results": "31", "hashOfConfig": "26"}, {"size": 2174, "mtime": 1749578115411, "results": "32", "hashOfConfig": "26"}, {"size": 1874, "mtime": 1749578104662, "results": "33", "hashOfConfig": "26"}, {"size": 2184, "mtime": 1749578046909, "results": "34", "hashOfConfig": "26"}, {"size": 874, "mtime": 1749578260266, "results": "35", "hashOfConfig": "26"}, {"size": 1231, "mtime": 1749580193935, "results": "36", "hashOfConfig": "26"}, {"size": 5415, "mtime": 1749578465723, "results": "37", "hashOfConfig": "26"}, {"size": 9280, "mtime": 1749579447317, "results": "38", "hashOfConfig": "26"}, {"size": 7925, "mtime": 1749580501086, "results": "39", "hashOfConfig": "26"}, {"size": 9629, "mtime": 1749579467153, "results": "40", "hashOfConfig": "26"}, {"size": 14404, "mtime": 1749580299712, "results": "41", "hashOfConfig": "26"}, {"size": 6985, "mtime": 1749578439540, "results": "42", "hashOfConfig": "26"}, {"size": 8929, "mtime": 1749578904201, "results": "43", "hashOfConfig": "26"}, {"size": 11357, "mtime": 1749579418781, "results": "44", "hashOfConfig": "26"}, {"size": 2491, "mtime": 1749578227961, "results": "45", "hashOfConfig": "26"}, {"size": 2922, "mtime": 1749582559540, "results": "46", "hashOfConfig": "26"}, {"size": 978, "mtime": 1749578010996, "results": "47", "hashOfConfig": "26"}, {"size": 279, "mtime": 1749578003517, "results": "48", "hashOfConfig": "26"}, {"size": 3288, "mtime": 1749580450716, "results": "49", "hashOfConfig": "26"}, {"filePath": "50", "messages": "51", "suppressedMessages": "52", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "6qmv07", {"filePath": "53", "messages": "54", "suppressedMessages": "55", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "56", "messages": "57", "suppressedMessages": "58", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "59", "messages": "60", "suppressedMessages": "61", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "62", "messages": "63", "suppressedMessages": "64", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "65", "messages": "66", "suppressedMessages": "67", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "68", "messages": "69", "suppressedMessages": "70", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "71", "messages": "72", "suppressedMessages": "73", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "77", "messages": "78", "suppressedMessages": "79", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "80", "messages": "81", "suppressedMessages": "82", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "83", "messages": "84", "suppressedMessages": "85", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "86", "messages": "87", "suppressedMessages": "88", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "89", "messages": "90", "suppressedMessages": "91", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "92", "messages": "93", "suppressedMessages": "94", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "95", "messages": "96", "suppressedMessages": "97", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "98", "messages": "99", "suppressedMessages": "100", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "101", "messages": "102", "suppressedMessages": "103", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "104", "messages": "105", "suppressedMessages": "106", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "107", "messages": "108", "suppressedMessages": "109", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "110", "messages": "111", "suppressedMessages": "112", "errorCount": 5, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "113", "messages": "114", "suppressedMessages": "115", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "116", "messages": "117", "suppressedMessages": "118", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "119", "messages": "120", "suppressedMessages": "121", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/app/api/auth/telegram/route.ts", [], [], "/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/app/api/mining/claim/route.ts", ["122"], [], "/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/app/api/mining/start/route.ts", [], [], "/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/app/api/referrals/route.ts", ["123"], [], "/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/app/api/subscription/basic-plan/route.ts", [], [], "/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/app/api/subscription/speed-upgrade/route.ts", [], [], "/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/app/api/tasks/complete/route.ts", ["124"], [], "/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/app/api/tasks/route.ts", [], [], "/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/app/api/user/profile/route.ts", [], [], "/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/app/layout.tsx", [], [], "/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/app/page.tsx", [], [], "/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/AirdropScreen.tsx", [], [], "/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/AuthScreen.tsx", ["125"], [], "/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/LandingPage.tsx", ["126", "127"], [], "/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MainApp.tsx", ["128"], [], "/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/MiningScreen.tsx", ["129", "130", "131"], [], "/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/ReferralsScreen.tsx", ["132", "133", "134"], [], "/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/SubscriptionScreen.tsx", ["135", "136"], [], "/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/components/TasksScreen.tsx", ["137", "138", "139", "140"], [], "/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/contexts/AuthContext.tsx", [], [], "/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/lib/api.ts", ["141", "142", "143", "144", "145"], [], "/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/lib/auth.ts", [], [], "/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/lib/prisma.ts", [], [], "/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/src/lib/telegram.ts", ["146", "147"], [], {"ruleId": "148", "severity": 2, "message": "149", "line": 49, "column": 12, "nodeType": null, "messageId": "150", "endLine": 49, "endColumn": 26}, {"ruleId": "148", "severity": 2, "message": "151", "line": 111, "column": 12, "nodeType": null, "messageId": "150", "endLine": 111, "endColumn": 20}, {"ruleId": "148", "severity": 2, "message": "152", "line": 48, "column": 12, "nodeType": null, "messageId": "150", "endLine": 48, "endColumn": 20}, {"ruleId": "148", "severity": 2, "message": "153", "line": 24, "column": 14, "nodeType": null, "messageId": "150", "endLine": 24, "endColumn": 19}, {"ruleId": "154", "severity": 1, "message": "155", "line": 43, "column": 6, "nodeType": "156", "endLine": 43, "endColumn": 17, "suggestions": "157"}, {"ruleId": "148", "severity": 2, "message": "153", "line": 80, "column": 14, "nodeType": null, "messageId": "150", "endLine": 80, "endColumn": 19}, {"ruleId": "154", "severity": 1, "message": "158", "line": 21, "column": 6, "nodeType": "156", "endLine": 21, "endColumn": 8, "suggestions": "159"}, {"ruleId": "154", "severity": 1, "message": "160", "line": 24, "column": 6, "nodeType": "156", "endLine": 24, "endColumn": 34, "suggestions": "161"}, {"ruleId": "148", "severity": 2, "message": "153", "line": 74, "column": 14, "nodeType": null, "messageId": "150", "endLine": 74, "endColumn": 19}, {"ruleId": "148", "severity": 2, "message": "153", "line": 101, "column": 14, "nodeType": null, "messageId": "150", "endLine": 101, "endColumn": 19}, {"ruleId": "148", "severity": 2, "message": "162", "line": 25, "column": 11, "nodeType": null, "messageId": "150", "endLine": 25, "endColumn": 15}, {"ruleId": "148", "severity": 2, "message": "153", "line": 47, "column": 14, "nodeType": null, "messageId": "150", "endLine": 47, "endColumn": 19}, {"ruleId": "148", "severity": 2, "message": "153", "line": 59, "column": 14, "nodeType": null, "messageId": "150", "endLine": 59, "endColumn": 19}, {"ruleId": "148", "severity": 2, "message": "153", "line": 29, "column": 14, "nodeType": null, "messageId": "150", "endLine": 29, "endColumn": 19}, {"ruleId": "148", "severity": 2, "message": "153", "line": 55, "column": 14, "nodeType": null, "messageId": "150", "endLine": 55, "endColumn": 19}, {"ruleId": "148", "severity": 2, "message": "153", "line": 38, "column": 14, "nodeType": null, "messageId": "150", "endLine": 38, "endColumn": 19}, {"ruleId": "148", "severity": 2, "message": "153", "line": 75, "column": 14, "nodeType": null, "messageId": "150", "endLine": 75, "endColumn": 19}, {"ruleId": "163", "severity": 2, "message": "164", "line": 204, "column": 32, "nodeType": "165", "messageId": "166", "suggestions": "167"}, {"ruleId": "163", "severity": 2, "message": "164", "line": 204, "column": 40, "nodeType": "165", "messageId": "166", "suggestions": "168"}, {"ruleId": "169", "severity": 2, "message": "170", "line": 4, "column": 27, "nodeType": "171", "messageId": "172", "endLine": 4, "endColumn": 30, "suggestions": "173"}, {"ruleId": "169", "severity": 2, "message": "170", "line": 8, "column": 18, "nodeType": "171", "messageId": "172", "endLine": 8, "endColumn": 21, "suggestions": "174"}, {"ruleId": "169", "severity": 2, "message": "170", "line": 59, "column": 42, "nodeType": "171", "messageId": "172", "endLine": 59, "endColumn": 45, "suggestions": "175"}, {"ruleId": "169", "severity": 2, "message": "170", "line": 66, "column": 41, "nodeType": "171", "messageId": "172", "endLine": 66, "endColumn": 44, "suggestions": "176"}, {"ruleId": "169", "severity": 2, "message": "170", "line": 78, "column": 41, "nodeType": "171", "messageId": "172", "endLine": 78, "endColumn": 44, "suggestions": "177"}, {"ruleId": "169", "severity": 2, "message": "170", "line": 118, "column": 53, "nodeType": "171", "messageId": "172", "endLine": 118, "endColumn": 56, "suggestions": "178"}, {"ruleId": "169", "severity": 2, "message": "170", "line": 120, "column": 59, "nodeType": "171", "messageId": "172", "endLine": 120, "endColumn": 62, "suggestions": "179"}, "@typescript-eslint/no-unused-vars", "'updatedSession' is assigned a value but never used.", "unusedVar", "'referral' is assigned a value but never used.", "'userTask' is assigned a value but never used.", "'error' is defined but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'loadingSteps'. Either include it or remove the dependency array.", "ArrayExpression", ["180"], "React Hook useEffect has a missing dependency: 'fetchUserProfile'. Either include it or remove the dependency array.", ["181"], "React Hook useEffect has a missing dependency: 'updateMiningTimer'. Either include it or remove the dependency array.", ["182"], "'user' is assigned a value but never used.", "react/no-unescaped-entities", "`\"` can be escaped with `&quot;`, `&ldquo;`, `&#34;`, `&rdquo;`.", "JSXText", "unescapedEntityAlts", ["183", "184", "185", "186"], ["187", "188", "189", "190"], "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["191", "192"], ["193", "194"], ["195", "196"], ["197", "198"], ["199", "200"], ["201", "202"], ["203", "204"], {"desc": "205", "fix": "206"}, {"desc": "207", "fix": "208"}, {"desc": "209", "fix": "210"}, {"messageId": "211", "data": "212", "fix": "213", "desc": "214"}, {"messageId": "211", "data": "215", "fix": "216", "desc": "217"}, {"messageId": "211", "data": "218", "fix": "219", "desc": "220"}, {"messageId": "211", "data": "221", "fix": "222", "desc": "223"}, {"messageId": "211", "data": "224", "fix": "225", "desc": "214"}, {"messageId": "211", "data": "226", "fix": "227", "desc": "217"}, {"messageId": "211", "data": "228", "fix": "229", "desc": "220"}, {"messageId": "211", "data": "230", "fix": "231", "desc": "223"}, {"messageId": "232", "fix": "233", "desc": "234"}, {"messageId": "235", "fix": "236", "desc": "237"}, {"messageId": "232", "fix": "238", "desc": "234"}, {"messageId": "235", "fix": "239", "desc": "237"}, {"messageId": "232", "fix": "240", "desc": "234"}, {"messageId": "235", "fix": "241", "desc": "237"}, {"messageId": "232", "fix": "242", "desc": "234"}, {"messageId": "235", "fix": "243", "desc": "237"}, {"messageId": "232", "fix": "244", "desc": "234"}, {"messageId": "235", "fix": "245", "desc": "237"}, {"messageId": "232", "fix": "246", "desc": "234"}, {"messageId": "235", "fix": "247", "desc": "237"}, {"messageId": "232", "fix": "248", "desc": "234"}, {"messageId": "235", "fix": "249", "desc": "237"}, "Update the dependencies array to be: [isLoading, loadingSteps]", {"range": "250", "text": "251"}, "Update the dependencies array to be: [fetchUserProfile]", {"range": "252", "text": "253"}, "Update the dependencies array to be: [updateMiningTimer, user?.currentMiningSession]", {"range": "254", "text": "255"}, "replaceWithAlt", {"alt": "256"}, {"range": "257", "text": "258"}, "Replace with `&quot;`.", {"alt": "259"}, {"range": "260", "text": "261"}, "Replace with `&ldquo;`.", {"alt": "262"}, {"range": "263", "text": "264"}, "Replace with `&#34;`.", {"alt": "265"}, {"range": "266", "text": "267"}, "Replace with `&rdquo;`.", {"alt": "256"}, {"range": "268", "text": "269"}, {"alt": "259"}, {"range": "270", "text": "271"}, {"alt": "262"}, {"range": "272", "text": "273"}, {"alt": "265"}, {"range": "274", "text": "275"}, "suggestUnknown", {"range": "276", "text": "277"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "278", "text": "279"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", {"range": "280", "text": "277"}, {"range": "281", "text": "279"}, {"range": "282", "text": "277"}, {"range": "283", "text": "279"}, {"range": "284", "text": "277"}, {"range": "285", "text": "279"}, {"range": "286", "text": "277"}, {"range": "287", "text": "279"}, {"range": "288", "text": "277"}, {"range": "289", "text": "279"}, {"range": "290", "text": "277"}, {"range": "291", "text": "279"}, [1186, 1197], "[isLoading, loadingSteps]", [707, 709], "[fetchUserProfile]", [703, 731], "[updateMiningTimer, user?.currentMiningSession]", "&quot;", [8388, 8499], "\n                      Clicking &quot;Do Task\" will open the link and mark the task as complete\n                    ", "&ldquo;", [8388, 8499], "\n                      Clicking &ldquo;Do Task\" will open the link and mark the task as complete\n                    ", "&#34;", [8388, 8499], "\n                      Clicking &#34;Do Task\" will open the link and mark the task as complete\n                    ", "&rdquo;", [8388, 8499], "\n                      Clicking &rdquo;Do Task\" will open the link and mark the task as complete\n                    ", [8388, 8499], "\n                      Clicking \"Do Task&quot; will open the link and mark the task as complete\n                    ", [8388, 8499], "\n                      Clicking \"Do Task&ldquo; will open the link and mark the task as complete\n                    ", [8388, 8499], "\n                      Clicking \"Do Task&#34; will open the link and mark the task as complete\n                    ", [8388, 8499], "\n                      Clicking \"Do Task&rdquo; will open the link and mark the task as complete\n                    ", [168, 171], "unknown", [168, 171], "never", [239, 242], [239, 242], [1332, 1335], [1332, 1335], [1530, 1533], [1530, 1533], [1872, 1875], [1872, 1875], [2809, 2812], [2809, 2812], [2897, 2900], [2897, 2900]]