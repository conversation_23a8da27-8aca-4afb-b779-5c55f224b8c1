# Changelog

## not yet released

None yet.

## v1.4.2 (2021-11-29)

* #35 Backport json-schema 0.4.0 to version 1.4.x

## v1.4.1 (2017-08-02)

* #21 Update verror dep
* #22 Update extsprintf dependency
* #23 update contribution guidelines

## v1.4.0 (2017-03-13)

* #7 Add parseInteger() function for safer number parsing

## v1.3.1 (2016-09-12)

* #13 Incompatible with webpack

## v1.3.0 (2016-06-22)

* #14 add safer version of hasOwnProperty()
* #15 forEachKey() should ignore inherited properties

## v1.2.2 (2015-10-15)

* #11 NPM package shouldn't include any code that does `require('JSV')`
* #12 jsl.node.conf missing definition for "module"

## v1.2.1 (2015-10-14)

* #8 odd date parsing behaviour

## v1.2.0 (2015-10-13)

* #9 want function for returning RFC1123 dates

## v1.1.0 (2015-09-02)

* #6 a new suite of hrtime manipulation routines: `hrtimeAdd()`,
  `hrtimeAccum()`, `hrtimeNanosec()`, `hrtimeMicrosec()` and
  `hrtimeMillisec()`.

## v1.0.0 (2015-09-01)

First tracked release.  Includes everything in previous releases, plus:

* #4 want function for merging objects
