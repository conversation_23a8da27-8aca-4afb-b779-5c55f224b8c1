{"name": "qs", "description": "A querystring parser that supports nesting and arrays, with a depth limit", "homepage": "https://github.com/ljharb/qs", "version": "6.5.3", "repository": {"type": "git", "url": "https://github.com/ljharb/qs.git"}, "main": "lib/index.js", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://ljharb.codes"}], "keywords": ["querystring", "qs"], "engines": {"node": ">=0.6"}, "devDependencies": {"@ljharb/eslint-config": "^20.1.0", "aud": "^1.1.5", "browserify": "^16.5.2", "eclint": "^2.8.1", "eslint": "^8.6.0", "evalmd": "^0.0.17", "iconv-lite": "^0.4.24", "in-publish": "^2.0.1", "mkdirp": "^0.5.1", "nyc": "^10.3.2", "qs-iconv": "^1.0.4", "safe-publish-latest": "^2.0.0", "safer-buffer": "^2.1.2", "tape": "^5.4.0"}, "scripts": {"prepublishOnly": "safe-publish-latest && npm run dist", "prepublish": "not-in-publish || npm run prepublishOnly", "pretest": "npm run --silent readme && npm run --silent lint", "test": "npm run --silent tests-only", "tests-only": "nyc tape 'test/**/*.js'", "posttest": "aud --production", "readme": "evalmd README.md", "postlint": "eclint check $(git ls-files | xargs find 2> /dev/null | grep -vE 'node_modules|\\.git')", "lint": "eslint --ext=js,mjs .", "dist": "mkdirp dist && browserify --standalone Qs lib/index.js > dist/qs.js"}, "license": "BSD-3-<PERSON><PERSON>"}