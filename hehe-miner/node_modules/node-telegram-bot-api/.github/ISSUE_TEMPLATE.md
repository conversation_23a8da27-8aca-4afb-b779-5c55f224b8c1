<!--
This template includes three sections:
1. Bug reporting
2. Feature request
3. Question

Please remove sections that do not apply to your issue
-->



<!--********************************************************************
Reporting a Bug.
*********************************************************************-->

> Bug Report

I have read:

* [Usage information](https://github.com/yagop/node-telegram-bot-api/tree/master/doc/usage.md)
* [Help information](https://github.com/yagop/node-telegram-bot-api/tree/master/doc/help.md)

I am using the latest version of the library.

### Expected Behavior

<!-- Explain what you are trying to achieve -->

### Actual Behavior

<!-- Explain what happens, contrary to what you expected -->

### Steps to reproduce the Behavior

<!-- Explain how we can reproduce the bug -->



<!--********************************************************************
Feature Request.
*********************************************************************-->

> Feature Request

I have:

* searched for such a feature request (https://github.com/yagop/node-telegram-bot-api/labels/enhancement) and found none

### Introduction

<!-- Describe what value this feature would add, and in which use case,
or scenario -->

### Example

<!-- A code snippet of how this feature would work, were it already
implemented -->



<!--********************************************************************
Question.
*********************************************************************-->

> Question

<!-- Ask your question here. Please be precise, adding as much detail
as necessary. Also, add a code snippet(s) if possible. -->
