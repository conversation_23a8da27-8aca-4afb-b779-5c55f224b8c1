"use strict";var m=Object.defineProperty;var a=(r,e)=>m(r,"name",{value:e,configurable:!0});var n=require("../../register-DulCUUwh.cjs");require("../../get-pipe-path-BoR10qr8.cjs");var t=require("../../register-D2KMMyKp.cjs");require("../../require-0KjtDnPR.cjs");var q=require("../../node-features-roYmp9jK.cjs");require("node:module"),require("node:worker_threads"),require("node:url"),require("module"),require("node:path"),require("../../temporary-directory-B83uKxJF.cjs"),require("node:os"),require("get-tsconfig"),require("node:fs"),require("../../index-CylV0-__.cjs"),require("esbuild"),require("node:crypto"),require("../../client-D6NvIMSC.cjs"),require("node:net");const c=a((r,e)=>{if(!e||typeof e=="object"&&!e.parentURL)throw new Error("The current file path (import.meta.url) must be provided in the second argument of tsImport()");const i=typeof e=="string",u=i?e:e.parentURL,s=Date.now().toString(),o=t.register({namespace:s});return!q.isFeatureSupported(q.esmLoadReadFile)&&!t.isBarePackageNamePattern.test(r)&&t.cjsExtensionPattern.test(r)?Promise.resolve(o.require(r,u)):n.register({namespace:s,...i?{}:e}).import(r,u)},"tsImport");exports.register=n.register,exports.tsImport=c;
