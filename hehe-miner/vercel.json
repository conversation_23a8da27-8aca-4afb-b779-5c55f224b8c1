{"name": "hehe-miner", "version": 2, "buildCommand": "npm run build", "outputDirectory": ".next", "installCommand": "npm install", "framework": "nextjs", "functions": {"src/app/api/**/*.ts": {"maxDuration": 30}}, "env": {"JWT_SECRET": "076c92f674d1e859d3954fabc2701864d069f509df7ff93f6c540b2ba6401e67", "TELEGRAM_BOT_TOKEN": "**********************************************", "NODE_ENV": "production", "ENABLE_MOCK_AUTH": "false"}, "build": {"env": {"JWT_SECRET": "076c92f674d1e859d3954fabc2701864d069f509df7ff93f6c540b2ba6401e67", "TELEGRAM_BOT_TOKEN": "**********************************************", "NODE_ENV": "production", "ENABLE_MOCK_AUTH": "false"}}, "headers": [{"source": "/api/(.*)", "headers": [{"key": "Access-Control-Allow-Origin", "value": "*"}, {"key": "Access-Control-Allow-Methods", "value": "GET, POST, PUT, DELETE, OPTIONS"}, {"key": "Access-Control-Allow-Headers", "value": "Content-Type, Authorization"}]}], "rewrites": [{"source": "/api/(.*)", "destination": "/api/$1"}]}