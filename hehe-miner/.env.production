# Production Environment Variables for Vercel Deployment

# Database Configuration (SQLite for now, can upgrade to PostgreSQL later)
DATABASE_URL="file:./dev.db"

# JWT Secret (Production)
JWT_SECRET="076c92f674d1e859d3954fabc2701864d069f509df7ff93f6c540b2ba6401e67"

# Telegram Bot Configuration
TELEGRAM_BOT_TOKEN="**********************************************"

# App Configuration (will be updated with actual Vercel URL)
NEXT_PUBLIC_APP_URL="https://hehe-miner.vercel.app"
NEXT_PUBLIC_APP_NAME="Hehe Miner"

# Production Settings
NODE_ENV="production"
ENABLE_MOCK_AUTH="false"

# Optional: Add these if you want enhanced features
# ANALYTICS_ID=""
# SENTRY_DSN=""
