# Production Environment Variables for Vercel Deployment

# Database Configuration (PostgreSQL with Prisma Accelerate)
DATABASE_URL="prisma+postgres://accelerate.prisma-data.net/?api_key=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJhcGlfa2V5IjoiMDFKWERLSDVXSFBaQUNISllETkc2NUVHM0IiLCJ0ZW5hbnRfaWQiOiIzZDcxYmJhZGFmMDY3Mjk4YTBlNmMwMzkxMzJmMDdlNzZkNGUyNmI4YTg2M2U2NjdlZTU2MDRmNTFiYmVlM2IyIiwiaW50ZXJuYWxfc2VjcmV0IjoiNDU4ODAyYjEtYjFlZS00ZGZmLTkyZGUtMzYxM2FlYTAwYWRiIn0.1XNe84cyNw1_W-_CXNHBP5kZsVTCs9tMEM6VUIBzhhY"

# JWT Secret (Production)
JWT_SECRET="076c92f674d1e859d3954fabc2701864d069f509df7ff93f6c540b2ba6401e67"

# Telegram Bot Configuration
TELEGRAM_BOT_TOKEN="**********************************************"

# App Configuration (will be updated with actual Vercel URL)
NEXT_PUBLIC_APP_URL="https://hehe-miner.vercel.app"
NEXT_PUBLIC_APP_NAME="Hehe Miner"

# Production Settings
NODE_ENV="production"
ENABLE_MOCK_AUTH="false"

# Optional: Add these if you want enhanced features
# ANALYTICS_ID=""
# SENTRY_DSN=""
