# Database Configuration
DATABASE_URL="file:./dev.db"

# JWT Secret (REQUIRED for production - generate a strong secret)
JWT_SECRET="076c92f674d1e859d3954fabc2701864d069f509df7ff93f6c540b2ba6401e67"

# Telegram Bot Configuration (REQUIRED for production)
TELEGRAM_BOT_TOKEN="**********************************************"
TELEGRAM_BOT_SECRET="your-telegram-bot-secret-here"

# App Configuration
NEXT_PUBLIC_APP_URL="http://localhost:3000"
NEXT_PUBLIC_APP_NAME="Hehe Miner"

# Development Settings
NODE_ENV="development"
ENABLE_MOCK_AUTH="true"

# Production Settings (uncomment and configure for production)
# NODE_ENV="production"
# ENABLE_MOCK_AUTH="false"
# NEXT_PUBLIC_APP_URL="https://your-domain.com"

# Optional: Analytics and Monitoring
# ANALYTICS_ID="your-analytics-id"
# SENTRY_DSN="your-sentry-dsn"

# Optional: Email Configuration (for notifications)
# SMTP_HOST="smtp.gmail.com"
# SMTP_PORT="587"
# SMTP_USER="<EMAIL>"
# SMTP_PASS="your-app-password"

# Optional: Redis Configuration (for caching and sessions)
# REDIS_URL="redis://localhost:6379"

# Optional: Rate Limiting
# RATE_LIMIT_WINDOW_MS="900000"  # 15 minutes
# RATE_LIMIT_MAX_REQUESTS="100"  # max requests per window

# Optional: File Upload Configuration
# MAX_FILE_SIZE="5242880"  # 5MB in bytes
# ALLOWED_FILE_TYPES="image/jpeg,image/png,image/gif"

# Optional: Payment Integration (if implementing real payments)
# STRIPE_SECRET_KEY="sk_test_..."
# STRIPE_PUBLISHABLE_KEY="pk_test_..."
# STRIPE_WEBHOOK_SECRET="whsec_..."

# Optional: Social Media Integration
# TWITTER_API_KEY="your-twitter-api-key"
# DISCORD_WEBHOOK_URL="your-discord-webhook-url"
